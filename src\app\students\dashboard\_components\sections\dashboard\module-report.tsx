import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

type ModuleReportProps = {
  modules?: {
    id: string;
    title: string;
    attempts: number;
    score?: number | null;
  }[];
  courseName: string;
};

export default function ModuleReport({
  modules,
  courseName,
}: ModuleReportProps) {
  return (
    <section>
      <Card className="py-0">
        <CardHeader className="bg-gray-100 py-2">
          <CardTitle className="text-center font-semibold">
            {courseName}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold">Module name</TableHead>
                <TableHead className="font-semibold">Attempts</TableHead>
                <TableHead className="font-semibold">Grade</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {!modules ? (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : modules.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    No upcoming schedules found.
                  </TableCell>
                </TableRow>
              ) : (
                <>
                  {modules.map((student) => (
                    <TableRow key={student.id}>
                      <TableCell>{student.title}</TableCell>
                      <TableCell>{student.attempts}</TableCell>
                      <TableCell>
                        {student.score
                          ? `${student.score.toFixed(0)} / 100`
                          : "N/A"}
                      </TableCell>
                    </TableRow>
                  ))}
                </>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </section>
  );
}
