"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { joinMeeting } from "@/lib/server/action/courses/virtual-classrooms";
import { format } from "date-fns";
import { CalendarIcon, Users } from "lucide-react";
import { toast } from "sonner";

type MeetingCardProps = {
  meeting?: {
    id: string;
    title: string;
    description: string;
    startTime: Date;
    endTime: Date;
    creator: {
      firstName: string;
      lastName: string;
    };
  } | null;
};

export default function MeetingCard({
  meeting,
}: {
  meeting: MeetingCardProps["meeting"];
}) {
  if (!meeting)
    return (
      <Card className="col-span-2">
        <CardContent>
          <p className="text-gray-500 text-lg">No ongoing meetings found.</p>
        </CardContent>
      </Card>
    );

  const now = new Date();
  const startTime = new Date(meeting.startTime);
  const endTime = new Date(meeting.endTime);
  const canJoin =
    now >= new Date(startTime.getTime() - 10 * 60 * 1000) && now <= endTime;
  const creatorName =
    meeting.creator.firstName + " " + meeting.creator.lastName;

  const handleJoinMeeting = async (meetingId: string) => {
    const res = await joinMeeting(meetingId);
    if (res.joinUrl) {
      window.open(res.joinUrl, "_blank");
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Card className="hover:shadow-lg transition-shadow col-span-2 w-full">
      <CardHeader>
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-3 flex-1">
            <div className="w-full">
              <CardTitle className="space-y-4">
                <div className="flex justify-between items-center gap-4 w-full">
                  <p className="text-sm">Ongoing</p>
                  <p className="flex items-center text-xs text-gray-500">
                    <CalendarIcon className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" />
                    {format(new Date(meeting.startTime), "PPP p")} -{" "}
                    {format(new Date(meeting.endTime), "p")}
                  </p>
                </div>
                <p className="text-lg line-clamp-2">{meeting.title}</p>
              </CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge
                  variant={
                    canJoin
                      ? "default"
                      : startTime > now
                      ? "secondary"
                      : "destructive"
                  }
                >
                  {canJoin ? "Live" : startTime > now ? "Scheduled" : "Ended"}
                </Badge>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Created by: {creatorName}
              </p>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-gray-600 text-sm line-clamp-2">
          {meeting.description}
        </p>

        {canJoin && (
          <div className="flex w-full">
            <Button
              onClick={() => handleJoinMeeting(meeting.id)}
              className="flex-1"
            >
              <Users className="w-4 h-4 mr-2" />
              Join
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
