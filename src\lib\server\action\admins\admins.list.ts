'use server'

import { appConfig } from "@/config/app";
import { formatTimeAgo } from "@/lib/formatDate";
import prisma from "@/lib/prisma";
import { Prisma } from "@prisma/client";


export type AdminWithUser = Prisma.AdminProfileGetPayload<{
  include: {
    user: true;
  };
}>;

export async function getAdmins({
  page,
  search,
  sortby,
}: {
  page: number;
  search?: string;
  sortby?: string;
}): Promise<{
  admins: AdminWithUser[]; // Corrected type here
  total: number;
}> {
  const whereClause: Prisma.AdminProfileWhereInput = {};
  const orderByClause: Prisma.AdminProfileOrderByWithRelationInput[] = [];

  if (search) {
    whereClause.OR = [
      { user: { firstName: { contains: search } } },
      { user: { lastName: { contains: search } } },
      { user: { email: { contains: search } } },
    ];
  }

  if (sortby) {
    switch (sortby) {
      case "name":
        orderByClause.push({ user: { firstName: "asc" } });
        break;
      case "email":
        orderByClause.push({ user: { email: "asc" } });
        break;
      case "status":
        orderByClause.push({ user: { status: "asc" } });
        break;
      default:
        orderByClause.push({ user: { firstName: "asc" } });
        break;
    }
  } else {
    orderByClause.push({ user: { firstName: "asc" } });
  }

  const queryOptions: Prisma.AdminProfileFindManyArgs = {
    include: {
      user: true,
    },
    where: whereClause,
    orderBy: orderByClause.length > 0 ? orderByClause : [{ user: { firstName: "asc" } }],
    skip: (page - 1) * appConfig.ITEMS_PER_PAGE,
    take: appConfig.ITEMS_PER_PAGE,
  };

  try {
    const admins = await prisma.adminProfile.findMany(queryOptions) as AdminWithUser[];

    const total = await prisma.adminProfile.count({
      where: whereClause,
    });

    return { admins, total };
  } catch (error) {
    console.error("Error fetching admins:", error);
    throw new Error("Failed to fetch admins.");
  }
}

export async function getAdmin(adminId: string) {
  try {
    const admin = await prisma.adminProfile.findUnique({
      where: { id: adminId },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            status: true,
            fileId: true,
            fileUrl: true,
            role: true,
            createdAt: true,
          }
        },
      },
    });

    return admin;
  } catch (error) {
    console.error("Error fetching admin:", error);
    throw new Error("Failed to fetch admin.");
  }
}

// Admin Dashboard
export async function getAdminDashboardData(sessionId: string) {
  try {
    const [totalStudents, totalTeachers, totalCourses, totalProgramLevels, totalCodes, recentCoursesData, recentlyApprovedUsers, recentStudentsData, recentTeachersData] = await Promise.all([
      prisma.studentProfile.count({ where: { sessionId } }),
      prisma.teacherProfile.count({ where: { sessionId } }),
      prisma.course.count({ where: { sessionId } }),
      prisma.program.count({ where: { sessionId } }),
      prisma.code.count({ where: { sessionId } }),
      prisma.course.findMany({
        where: { sessionId },
        select: {
          id: true,
          title: true,
          description: true,
          fileUrl: true,
          teacherAssignments: {
            select: {
              role: true,
              user: {
                select: {
                  firstName: true,
                  lastName: true
                }
              }
            }
          },
          _count: {
            select: {
              enrollments: true
            }
          },
          program: {
            select: {
              name: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 2
      }),
      prisma.user.findMany({
        where: { 
          AND: [
            { role: { in: ["TEACHER", "STUDENT"] } },
            { status: "APPROVED" },
          ]
        },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          role: true,
          createdAt: true,
          fileUrl: true,
          studentProfile: {
            select: {
              school: {
                select: {
                  name: true
                }
              }
            }
          },
          teacherProfile: {
            select: {
              school: {
                select: {
                  name: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 5
      }),
      prisma.studentProfile.findMany({
        where: { sessionId, user: { status: "APPROVED" } },
        select: {
          id: true,
          createdAt: true,
          user: {
            select: {
              firstName: true,
              lastName: true,
              fileUrl: true
            }
          },
          program: {
            select: {
              name: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 5
      }),
      prisma.teacherProfile.findMany({
        where: { sessionId, user: { status: "APPROVED" } },
        select: {
          id: true,
          createdAt: true,
          user: {
            select: {
              firstName: true,
              lastName: true,
              fileUrl: true,
              courseAssignments: {
                select: {
                  course: {
                    select: {
                      program: {
                        select: {
                          name: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 5
      })
    ])

    return {
      counts: {
        totalCodes,
        totalCourses,
        totalProgramLevels,
        totalStudents,
        totalTeachers
      },
      recentCourses: recentCoursesData ? recentCoursesData.map(course => {
        const teacher = course.teacherAssignments.filter(t => t.role === "PRIMARY")[0]?.user
        return {
          id: course.id,
          image: course.fileUrl,
          title: course.title,
          description: course.description || '',
          instructor: `${teacher.firstName} ${teacher.lastName}`,
          students: course._count.enrollments,
          program: course.program.name
        }
      }) : [],
      recentlyApprovedUsers: recentlyApprovedUsers ? recentlyApprovedUsers.map(user => ({
        id: user.id,
        avatar: user.fileUrl,
        name: `${user.firstName} ${user.lastName}`,
        school: user.role === "TEACHER" ? user.teacherProfile?.school?.name : user.studentProfile?.school?.name,
        type: user.role,
        time: formatTimeAgo(user.createdAt)
      })) : [],
      recentStudents: recentStudentsData ? recentStudentsData.map(student => ({
        id: student.id,
        avatar: student.user.fileUrl,
        name: `${student.user.firstName} ${student.user.lastName}`,
        program: student.program.name,
        createdAt: student.createdAt
      })) : [],
      recentTeachers: recentTeachersData ? recentTeachersData.map(teacher => ({
        id: teacher.id,
        avatar: teacher.user.fileUrl,
        name: `${teacher.user.firstName} ${teacher.user.lastName}`,
        program: teacher.user.courseAssignments[0]?.course.program.name,
        createdAt: teacher.createdAt
      })) : []
    }
  } catch (error) {
    console.log(error)
    return null
  }
}