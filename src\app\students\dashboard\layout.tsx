import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import AppSidebar from "./_components/layout/app-sidebar";
import DashboardHeader from "./_components/layout/dashboardHeader";
import { checkStudentLogin } from "@/lib/server/action/students";
import { auth } from "../../../../auth";
import { redirect } from "next/navigation";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();
  if (!session || session.user.role !== "STUDENT") {
    redirect("/sign-in");
  }

  const student = await checkStudentLogin(
    session.user.profileId,
    session.user.courseId as string
  );
  if (!student) {
    redirect("/sign-in");
  }

  return (
    <SidebarProvider>
      <AppSidebar />

      <SidebarInset>
        <DashboardHeader user={session.user} />
        <div className="w-full container mx-auto p-6">{children}</div>
      </SidebarInset>
    </SidebarProvider>
  );
}
