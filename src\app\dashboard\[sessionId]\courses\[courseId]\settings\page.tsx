import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import React from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import CreateCourseTab from "../../_components/tabs/course/create-course-tab";
import CoursePreviewTab from "../../_components/tabs/course/preview-tab";
import CourseSettingsTab from "../../_components/tabs/course/settings-tab";
import Link from "next/link";
import { getCourseWithDetails } from "@/lib/server/action/courses";
import { notFound } from "next/navigation";
import CourseActionButtons from "../../_components/CourseActionButtons";
import { auth } from "@/lib/server/auth";

export default async function CourseSettingsPage({
  params,
}: {
  params: Promise<{ sessionId: string; courseId: string }>;
}) {
  const { sessionId, courseId } = await params;
  const session = await auth();
  const course = await getCourseWithDetails(courseId);
  if (!course || !session || !session.user) {
    notFound();
  }

  const courseTeachers = course.teacherAssignments;
  const primaryTeacher = courseTeachers.find((t) => t.role === "PRIMARY");
  const isAdmin = session.user.role === "ADMIN";

  return (
    <>
      <Button
        variant="outline"
        // onClick={onBack}
        asChild
      >
        <Link href={`/dashboard/${sessionId}/courses/${course.id}`}>
          <ArrowLeft className="w-4 h-4" />
          Back to Course
        </Link>
      </Button>

      {/* Header */}
      <div className="flex lg:items-center justify-between my-2 flex-col lg:flex-row gap-3">
        <div className="flex items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Course Settings</h1>
            <p className="text-gray-600">Fill in the details to course</p>
          </div>
        </div>
        <CourseActionButtons
          sessionId={sessionId}
          courseId={course.id}
          courseStatus={course.status}
        />
      </div>

      <Tabs defaultValue="basic" className="w-full mt-6">
        <TabsList className="flex justify-between w-full">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          {isAdmin && <TabsTrigger value="settings">Settings</TabsTrigger>}
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>
        <TabsContent value="basic" className="space-y-6 mt-4">
          <CreateCourseTab
            sessionId={sessionId}
            courseId={course.id}
            courseTeachers={courseTeachers}
            courseData={{
              title: course.title,
              description: course.description || "",
              programId: course.program.id,
              teacherId:
                courseTeachers.find((t) => t.role === "PRIMARY")?.user.id || "",
              fileUrl: course.fileUrl,
            }}
            isAdmin={isAdmin}
          />
        </TabsContent>
        {isAdmin && (
          <TabsContent value="settings" className="space-y-6 mt-4">
            <CourseSettingsTab courseId={course.id} sessionId={sessionId} />
          </TabsContent>
        )}
        <TabsContent value="preview" className="space-y-6 mt-4">
          <CoursePreviewTab
            {...{
              title: course.title,
              description: course.description || "",
              program: course.program.name,
              primaryTeacherName:
                primaryTeacher?.user.firstName +
                " " +
                primaryTeacher?.user.lastName,
              fileUrl: course.fileUrl,
            }}
          />
        </TabsContent>
      </Tabs>
    </>
  );
}
