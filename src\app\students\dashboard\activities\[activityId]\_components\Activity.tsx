"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Edit, Loader } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import {
  createStudentActivity,
  updateStudentActivity,
} from "@/lib/server/action/students/activities/activities.action";
import { toast } from "sonner";

type StudentResponse = {
  id: string;
  response: string;
  submittedAt: Date;
  status: string;
  score: number;
};

export default function ActivitiesDetail({
  response,
  reviewed,
  activityId,
  studentId,
}: {
  response: StudentResponse;
  reviewed: boolean;
  activityId: string;
  studentId: string;
}) {
  const router = useRouter();

  const [answer, setAnswer] = useState(response.response);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);
      const res = response.id
        ? await updateStudentActivity(response.id, answer)
        : await createStudentActivity(activityId as string, studentId, answer);
      if (res.success) {
        toast.success(res.message);
        router.push(`/students/dashboard/activities`);
      } else {
        toast.error(res.error);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="relative">
      <Textarea
        className="min-h-[200px] resize-none"
        value={answer}
        onChange={(e) => {
          setAnswer(e.target.value);
        }}
        readOnly={reviewed}
      />
      <Button
        size="sm"
        className="absolute bottom-4 right-4 rounded-full w-10 h-10 p-0"
        disabled={reviewed}
      >
        {isSubmitting ? (
          <Loader className="animate-spin" />
        ) : (
          <Edit className="w-4 h-4" />
        )}
      </Button>
    </form>
  );
}
