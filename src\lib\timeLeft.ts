export function getTimeLeft(targetDate: string | number | Date): string {
  const now: Date = new Date();
  const target: Date = new Date(targetDate);

  const diffMs: number = Number(target.getTime()) - Number(now.getTime()); // Ensure both sides are numbers

  if (diffMs <= 0) {
    return "O minute";
  }

  const minutes: number = Math.floor(diffMs / (1000 * 60));
  const hours: number = Math.floor(diffMs / (1000 * 60 * 60));
  const days: number = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (minutes < 60) {
    return `${minutes} minute(s) left`;
  } else if (hours < 24) {
    return `${hours} hour(s) left`;
  } else {
    return `${days} day(s) left`;
  }
}