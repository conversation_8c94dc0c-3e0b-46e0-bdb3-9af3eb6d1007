"use server";

import {apiFetch, getEnv, withErrorHandling} from "@/lib/utils";
import { appConfig } from "@/config/app";
import { auth } from "../../auth";

interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

// Constants with full names
const VIDEO_STREAM_BASE_URL = appConfig.bunny.STREAM_BASE_URL;
const STORAGE_BASE_URL = appConfig.bunny.STORAGE_BASE_URL;
const CDN_URL = appConfig.bunny.CDN_URL;
const BUNNY_LIBRARY_ID = getEnv("BUNNY_LIBRARY_ID");
const ACCESS_KEYS = {
  streamAccessKey: getEnv("BUNNY_STREAM_ACCESS_KEY"),
  storageAccessKey: getEnv("BUNNY_STORAGE_ACCESS_KEY"),
};

// Server Actions
export const getVideoUploadUrl = withErrorHandling(async () => {
  await auth()
  const videoResponse = await apiFetch<BunnyVideoResponse>(
    `${VIDEO_STREAM_BASE_URL}/${BUNNY_LIBRARY_ID}/videos`,
    {
      method: "POST",
      bunnyType: "stream",
      body: { title: "Temp Title", collectionId: "" },
    }
  );

  const uploadUrl = `${VIDEO_STREAM_BASE_URL}/${BUNNY_LIBRARY_ID}/videos/${videoResponse.guid}`;
  return {
    fileId: videoResponse.guid,
    uploadUrl,
    cdnUrl: '',
    accessKey: ACCESS_KEYS.streamAccessKey,
  };
});

export async function generateFileUploadUrl(originalName: string, folder = '') {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split('.').pop();
  const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
  const sanitizedName = nameWithoutExt.replace(/[^a-zA-Z0-9_-]/g, '_');
  
  const fileName = `${sanitizedName}_${timestamp}_${randomString}.${extension}`;

  const uploadUrl = folder ? `${STORAGE_BASE_URL}/${folder}/${fileName}` : `${STORAGE_BASE_URL}/${fileName}`;
  const cdnUrl = folder ? `${CDN_URL}/${folder}/${fileName}` : `${CDN_URL}/${fileName}`;

  return {
    fileId: fileName,
    uploadUrl,
    cdnUrl,
    accessKey: ACCESS_KEYS.storageAccessKey,
  };
}

export const getGeneralUploadUrl = withErrorHandling(
  async (lessonId: string) => {
    const timestampedFileName = `${Date.now()}-${lessonId}-lesson`;
    const uploadUrl = `${STORAGE_BASE_URL}/general/${timestampedFileName}`;
    const cdnUrl = `${CDN_URL}/general/${timestampedFileName}`;

    return {
      fileId: timestampedFileName,
      uploadUrl,
      cdnUrl,
      accessKey: ACCESS_KEYS.storageAccessKey,
    };
  }
);

export const deleteVideo = withErrorHandling(
  async (videoId: string) => {
    await apiFetch(
      `${VIDEO_STREAM_BASE_URL}/${BUNNY_LIBRARY_ID}/videos/${videoId}`,
      { method: "DELETE", bunnyType: "stream" }
    );
    return {};
  }
);

export async function deleteFromBunny(fileName: string, folder = '') {
  try {
    const url = folder ? `${STORAGE_BASE_URL}/${folder}/${fileName}` : `${STORAGE_BASE_URL}/${fileName}`;
    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'AccessKey': ACCESS_KEYS.storageAccessKey,
      },
    });

    if (!response.ok && response.status !== 404) {
      throw new Error(`Delete failed: ${response.status} ${response.statusText}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Bunny delete error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

export async function deleteFromBunnyCDN(fileUrl: string): Promise<boolean> {
  try {
    // Extract file path from CDN URL
    const urlParts = fileUrl.replace(appConfig.bunny.CDN_URL + "/", "");

    const deleteResponse = await fetch(
      `${appConfig.bunny.STORAGE_BASE_URL}/${urlParts}`,
      {
        method: "DELETE",
        headers: {
          "AccessKey": ACCESS_KEYS.storageAccessKey,
        },
      }
    );

    return deleteResponse.ok;
  } catch (error) {
    console.error("Error deleting from Bunny CDN:", error);
    return false;
  }
}

export async function getFileType(mimeType: string) {
  if (mimeType.startsWith('image/')) return 'IMAGE';
  if (mimeType.startsWith('video/')) return 'VIDEO';
  if (mimeType.includes('pdf')) return 'PDF'; // Specific for PDF
  if (
    mimeType.includes('application/msword') || // .doc
    mimeType.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document') // .docx
  )
    return 'DOCS'; // Specific for Word documents
  if (
    mimeType.includes('application/vnd.ms-excel') || // .xls
    mimeType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') // .xlsx
  )
    return 'XLSX'; // Specific for Excel spreadsheets
  if (
    mimeType.includes('application/vnd.ms-powerpoint') || // .ppt
    mimeType.includes('application/vnd.openxmlformats-officedocument.presentationml.presentation') // .pptx
  )
    return 'PPT'; // Specific for PowerPoint presentations
  if (
    mimeType.includes('text/') ||
    mimeType.includes('application/vnd.openxmlformats') || // Catch-all for other Office XML formats
    mimeType.includes('application/vnd.ms-') || // Catch-all for other Microsoft Office formats
    mimeType.includes('document') // Generic document check
  )
    return 'DOCUMENT'; // General document type for anything not specifically caught
  return 'OTHER';
}

export const getVideoProcessingStatus = withErrorHandling(
  async (videoId: string) => {
    const processingInfo = await apiFetch<BunnyVideoResponse>(
      `${VIDEO_STREAM_BASE_URL}/${BUNNY_LIBRARY_ID}/videos/${videoId}`,
      { bunnyType: "stream" }
    );

    return {
      isProcessed: processingInfo.status === 4,
      encodingProgress: processingInfo.encodeProgress || 0,
      status: processingInfo.status,
    };
  }
);

export async function uploadToBunnyCDN(
  formData: FormData,
  folder: string = "cms"
): Promise<UploadResult> {
  try {
    const file = formData.get("file") as File;
    
    if (!file) {
      return { success: false, error: "No file provided" };
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp", "image/gif"];
    if (!allowedTypes.includes(file.type)) {
      return { 
        success: false, 
        error: "Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed." 
      };
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return { 
        success: false, 
        error: "File size too large. Maximum size is 5MB." 
      };
    }

    const { uploadUrl, cdnUrl, accessKey} = await generateFileUploadUrl(file.name, folder)

    // Upload to Bunny CDN
    const uploadResponse = await fetch(
      uploadUrl,
      {
        method: "PUT",
        headers: {
          "AccessKey": accessKey,
          "Content-Type": file.type,
        },
        body: file,
      }
    );

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      console.error("Bunny CDN upload error:", errorText);
      return { 
        success: false, 
        error: `Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}` 
      };
    }

    return { success: true, url: cdnUrl };
  } catch (error) {
    console.error("Error uploading to Bunny CDN:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Upload failed" 
    };
  }
}