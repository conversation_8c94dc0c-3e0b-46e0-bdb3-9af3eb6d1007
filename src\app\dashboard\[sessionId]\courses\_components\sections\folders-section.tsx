"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Edit, Eye, Folder, Plus, Trash2, Upload } from "lucide-react";
import { CustomSheet } from "@/components/shared/CustomSheet";
import { Label } from "@/components/ui/label";
import { Fragment, useState } from "react";
import {
  CourseResourceFolderWithResources,
  createCourseResourceFolder,
  deleteCourseResource,
  deleteCourseResourceFolder,
  updateCourseResourceFolder,
} from "@/lib/server/action/courses/resources";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { formatBytes } from "@/lib/utils";
import { Separator } from "@/components/ui/separator";
import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import CustomDialog from "@/components/shared/CustomDialog";
import { CourseResourceUpload } from "../CourseResourceUpload";

export default function CourseFolders({
  courseId,
  folders,
}: {
  courseId: string;
  folders: CourseResourceFolderWithResources[];
}) {
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [folderName, setFolderName] = useState("");

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      setIsCreatingFolder(true);
      const res = await createCourseResourceFolder(courseId, {
        name: folderName.toLocaleUpperCase(),
      });
      if (res.success) {
        toast.success(res.message);
      } else {
        toast.error(res.error);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsCreatingFolder(false);
    }
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Folders</h3>
        <CustomSheet
          title="Create Folder"
          trigger={
            <Button variant="outline" size="sm">
              <Plus className="w-4 h-4" />
              New Folder
            </Button>
          }
        >
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="folder-name">Folder Name</Label>
              <Input
                placeholder="Folder name"
                className="w-full"
                value={folderName}
                onChange={(e) => setFolderName(e.target.value)}
                required
              />
            </div>
            <Button className="w-full">
              {isCreatingFolder ? "Creating..." : "Create"}
            </Button>
          </form>
        </CustomSheet>
      </div>
      {folders.length === 0 ? (
        <p className="text-sm text-muted-foreground">
          No folders found. Create a new folder.
        </p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {folders.map((folder) => (
            <Fragment key={folder.id}>
              <FolderCard folder={folder} courseId={courseId} />
            </Fragment>
          ))}
        </div>
      )}
    </div>
  );
}

function FolderCard({
  folder,
  courseId,
}: {
  folder: CourseResourceFolderWithResources;
  courseId: string;
}) {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Card
        className="hover:shadow-md transition-shadow cursor-pointer py-0"
        onClick={() => setOpen(true)}
      >
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Folder className="w-8 h-8 text-primary" />
              <div>
                <h4 className="font-medium">
                  {folder.name.toLocaleUpperCase()}
                </h4>
                <p className="text-sm text-gray-500">
                  {folder.resources.length} files
                </p>
              </div>
            </div>
            <Badge variant="secondary">{folder.resources.length}</Badge>
          </div>
        </CardContent>
      </Card>

      <FolderDialog
        open={open}
        setOpen={setOpen}
        folder={folder}
        courseId={courseId}
      />
    </>
  );
}

function FolderDialog({
  open,
  setOpen,
  folder,
  courseId,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  folder: CourseResourceFolderWithResources;
  courseId: string;
}) {
  const [isUpdatingFolder, setIsUpdatingFolder] = useState(false);
  const [folderName, setFolderName] = useState(folder.name);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      setIsUpdatingFolder(true);
      await updateCourseResourceFolder(folder.id, {
        name: folderName,
      });
      toast.success("Folder updated");
    } catch (error) {
      console.log(error);
      toast.error("Failed to update folder");
    } finally {
      setIsUpdatingFolder(false);
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-2xl max-h-[85vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>{folder.name}</DialogTitle>
          <DialogDescription>Manage uploaded files.</DialogDescription>
        </DialogHeader>

        <div className="flex-1 min-h-0">
          {folder.resources.length === 0 ? (
            <div className="flex items-center justify-center h-32">
              <p className="text-sm text-muted-foreground">No file found.</p>
            </div>
          ) : (
            <ScrollArea className="h-[400px]">
              <div className="space-y-3 p-1">
                {folder.resources.map((resource) => (
                  <div
                    key={resource.id}
                    className="border flex justify-between items-center p-4 rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium">{resource.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatBytes(resource.fileSize!)}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Uploaded By:{" "}
                        <span className="text-black">
                          {resource.uploadedBy}
                        </span>
                      </p>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-8 w-8 bg-transparent"
                        onClick={() => window.open(resource.fileUrl!, "_blank")}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="destructive"
                        size="icon"
                        className="h-8 w-8"
                        onClick={async () => {
                          const res = await deleteCourseResource(resource.id);
                          if (res.success) {
                            toast.success(res.message);
                          } else {
                            toast.error(res.error);
                          }
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>

        <Separator className="flex-shrink-0" />

        <DialogFooter className="flex-shrink-0 gap-2 flex">
          <CustomDialog
            title="Upload Files"
            trigger={
              <Button size="sm">
                <Upload className="w-4 h-4" />
                Upload
              </Button>
            }
          >
            <CourseResourceUpload folderId={folder.id} courseId={courseId} />
          </CustomDialog>

          <CustomSheet
            title="Edit Folder"
            trigger={
              <Button variant="outline" size="icon">
                <Edit />
              </Button>
            }
          >
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="folder-name">Folder Name</Label>
                <Input
                  id="folder-name"
                  placeholder="Folder name"
                  className="w-full"
                  value={folderName}
                  onChange={(e) => setFolderName(e.target.value)}
                  required
                />
              </div>
              <Button className="w-full" disabled={isUpdatingFolder}>
                {isUpdatingFolder ? "Updating..." : "Update"}
              </Button>
            </form>
          </CustomSheet>

          <CustomAlertDialog
            title="Delete Folder"
            description="This action will remove all files in this folder. Are you sure you want to delete this folder?"
            trigger={
              <Button variant="destructive" size="icon">
                <Trash2 />
              </Button>
            }
            onConfirm={async () => {
              setOpen(false);
              const res = await deleteCourseResourceFolder(folder.id);
              if (res.success) {
                toast.success(res.message);
              } else {
                toast.error(res.error);
              }
            }}
          />
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
