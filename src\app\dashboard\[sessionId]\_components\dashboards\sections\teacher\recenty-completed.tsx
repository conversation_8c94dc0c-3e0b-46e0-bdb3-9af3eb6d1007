import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { getInitialGradeByStudentId } from "@/lib/server/action/students/reports/reports.list";

type RecentlyCompletedProps = {
  recentlyCompleted?: {
    id: string;
    studentName: string;
    school: string;
    course: string;
    grade: number;
    studentId: string;
  }[];
};

export default function RecentlyCompleted({
  recentlyCompleted,
}: RecentlyCompletedProps) {
  return (
    <div>
      <Card className="py-0">
        <CardHeader className="bg-gray-100 py-2">
          <CardTitle className="text-center font-semibold">
            RECENTLY COMPLETED
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold">Student name</TableHead>
                <TableHead className="font-semibold">School</TableHead>
                <TableHead className="font-semibold">Course</TableHead>
                <TableHead className="font-semibold text-center">
                  Grade
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {!recentlyCompleted ? (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : recentlyCompleted.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    No upcoming schedules found.
                  </TableCell>
                </TableRow>
              ) : (
                recentlyCompleted.map(async (student) => {
                  const iG =
                    (await getInitialGradeByStudentId(student.studentId)) || 0;
                  return (
                    <TableRow key={student.id}>
                      <TableCell>{student.studentName}</TableCell>
                      <TableCell>{student.school}</TableCell>
                      <TableCell>{student.course}</TableCell>
                      <TableCell className="text-center">
                        {Math.round(iG) || "-"}
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
