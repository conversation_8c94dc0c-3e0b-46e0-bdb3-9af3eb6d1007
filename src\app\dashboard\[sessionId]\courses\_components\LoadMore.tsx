"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";

export default function LoadMore() {
  const [displayCount, setDisplayCount] = useState(6);
  const filteredCourses = [];

  const handleLoadMore = () => {
    setDisplayCount((prev) => Math.min(prev + 6, filteredCourses.length));
  };

  return (
    <div className="flex justify-center pt-6">
      <Button onClick={handleLoadMore} variant="outline" size="lg">
        Load More Courses ({filteredCourses.length - displayCount} remaining)
      </Button>
    </div>
  );
}
