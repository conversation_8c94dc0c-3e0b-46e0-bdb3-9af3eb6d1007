import SigninForm from "@/components/form/SigninForm";
import { auth } from "../../../../auth";
import { redirect } from "next/navigation";
import { checkUser } from "@/lib/server/action/users/user.action";
import { getLoginData } from "@/lib/server/action/frontend/frontend.action";
import { checkStudentLogin } from "@/lib/server/action/students";

export default async function SignInPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const { type } = await searchParams;
  const loginType = type?.toLocaleUpperCase();
  const formType = loginType === "EP" ? "ep" : "lc";

  const session = await auth();
  const [checkSessionUser, checkStudent] = await Promise.all([
    checkUser(session?.user.id as string),
    checkStudentLogin(
      session?.user.profileId as string,
      session?.user.courseId as string
    ),
  ]);

  const loginItems = await getLoginData();

  if (
    session &&
    (session.user.role === "ADMIN" || session.user.role === "TEACHER") &&
    checkSessionUser
  ) {
    redirect("/dashboard");
  } else if (session && session.user.role === "STUDENT" && checkStudent) {
    redirect("/students/dashboard");
  }

  return (
    <div>
      <SigninForm type={formType} loginItems={loginItems} />
    </div>
  );
}
