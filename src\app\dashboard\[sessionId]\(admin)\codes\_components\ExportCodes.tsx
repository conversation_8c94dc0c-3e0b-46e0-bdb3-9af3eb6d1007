"use client";

import { Button } from "@/components/ui/button";
import { exportToExcel } from "@/lib/ExelHandler";
import { formatDate } from "@/lib/formatDate";
import { getAllCodes } from "@/lib/server/action/codes";
import { Download, Loader } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { toast } from "sonner";

const ExportCodes = () => {
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    try {
      setLoading(true);
      const codeData = await getAllCodes();
      console.log(codeData);

      const data = codeData.map((code) => ({
        "First Name": code.user?.firstName || "",
        "Last Name": code.user?.lastName || "",
        Email: code.user?.email || "",
        "Exam Code": code.code || "",
        "Exam Code Status": code.status || "",
        "Exam Code Creation Date": code.createdAt
          ? formatDate(code.createdAt)
          : "",
        "Updated At": code.updatedAt ? formatDate(code.updatedAt) : "",
      }));

      exportToExcel({ data, fileName: `codes_table.xlsx` });
      toast("Code export successful");
    } catch (error) {
      console.error("Export error:", error);
      toast("Something went wrong!");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center gap-4">
      <Image src={"/images/excel.png"} alt="Excel" width={90} height={90} />
      <p className="text-sm font-medium text-gray-500">code_table.xlsx</p>
      <Button
        disabled={loading}
        onClick={handleClick}
        className="disabled:opacity-50"
      >
        {loading ? (
          <>
            <Loader /> Exporting to excel{" "}
          </>
        ) : (
          <>
            <Download /> Export to excel
          </>
        )}
      </Button>
    </div>
  );
};

export default ExportCodes;
