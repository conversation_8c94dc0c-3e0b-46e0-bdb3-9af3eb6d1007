"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { Button } from "../ui/button";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import { FormInputField } from "../form-element/input-field";
import { createAdmin, updateAdmin } from "@/lib/server/action/admins";
import {
  adminSchema,
  TAdminForm,
} from "@/lib/server/action/admins/admins.schema";

const AdminForm = ({
  adminId,
  adminData,
}: {
  adminId?: string;
  adminData?: Omit<TAdminForm, "password">;
}) => {
  const form = useForm<TAdminForm>({
    resolver: zodResolver(adminSchema),
    defaultValues: adminData ?? {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      phone: "",
    },
    mode: "onChange",
  });

  const onSubmit = async (values: TAdminForm) => {
    const res = adminData
      ? await updateAdmin(adminId as string, values)
      : await createAdmin(values);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid gap-6 md:grid-cols-2">
          <FormInputField
            control={form.control}
            name="firstName"
            label="First Name"
            placeholder="John"
          />
          <FormInputField
            control={form.control}
            name="lastName"
            label="Last Name"
            placeholder="Doe"
          />
          <FormInputField
            control={form.control}
            name="email"
            label="Email"
            type="email"
            placeholder="<EMAIL>"
          />
          {!adminData && (
            <FormInputField
              control={form.control}
              name="password"
              label="Password"
              type="password"
              placeholder="*******"
            />
          )}
          <FormInputField
            control={form.control}
            name="phone"
            label="Phone"
            placeholder="(*************"
          />
        </div>
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader /> {adminData ? "Updating Admin" : "Creating Admin"}
              </>
            ) : (
              <>{adminData ? "Update Admin" : "Create Admin"}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default AdminForm;
