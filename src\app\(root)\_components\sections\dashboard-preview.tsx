import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";

const DashboardPreview = ({ heroImage }: { heroImage: string }) => {
  return (
    <section className="max-w-7xl mx-auto py-16">
      <Card className="w-full">
        <CardContent>
          <Image
            src={heroImage || "/images/placeholder.svg"}
            alt="Dashboard Preview"
            width={2016}
            height={1210}
            className="w-full rounded-md"
            placeholder="blur"
            blurDataURL="/images/placeholder-blur.jpg"
          />
        </CardContent>
      </Card>
    </section>
  );
};

export default DashboardPreview;
