'use server'

import { appConfig } from "@/config/app";
import prisma from "@/lib/prisma";
import { Prisma, UserStatus } from "@prisma/client";

export type TeacherWithUserAndSchool = Prisma.TeacherProfileGetPayload<{
  include: {
    user: {
      select: {
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        status: true,
        fileUrl: true,
        courseAssignments: {
          select: {
            role: true,
            course: {
              select: {
                title: true,
              }
            }
          }
        }
      }
    };
    school: {
      select: {
        name: true,
        id: true
      }
    };
  };
}>;

export async function getTeachers({
  sessionId,
  page,
  search,
  sortby,
  status = "APPROVED",
}: {
  sessionId: string;
  page: number;
  search?: string;
  sortby?: string;
  status?: UserStatus;
}): Promise<{
  teachers: TeacherWithUserAndSchool[]; // Corrected type here
  total: number;
}> {
  const whereClause: Prisma.TeacherProfileWhereInput = {};
  const orderByClause: Prisma.TeacherProfileOrderByWithRelationInput[] = [];

  whereClause.user = { status };
  whereClause.sessionId = sessionId;

  if (search) {
    whereClause.OR = [
      { user: { firstName: { contains: search } } },
      { user: { lastName: { contains: search } } },
      { user: { email: { contains: search } } },
      { school: { name: { contains: search } } },
    ];
  }

  if (sortby) {
    switch (sortby) {
      case "name":
        orderByClause.push({ user: { firstName: "asc" } });
        break;
      case "email":
        orderByClause.push({ user: { email: "asc" } });
        break;
      case "status":
        orderByClause.push({ user: { status: "asc" } });
        break;
      case "school":
        orderByClause.push({ school: { name: "asc" } });
        break;
      default:
        orderByClause.push({ user: { firstName: "asc" } });
        break;
    }
  } else {
    orderByClause.push({ user: { firstName: "asc" } });
  }

  const queryOptions: Prisma.TeacherProfileFindManyArgs = {
    include: {
      user: {
        select: {
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          status: true,
          fileUrl: true,
          courseAssignments: {
            select: {
              role: true,
              course: { select: { title: true } }
            }
          }
        }
      },
      school: {
        select: {
          name: true,
          id: true
        }
      }
    },
    where: whereClause,
    orderBy: orderByClause.length > 0 ? orderByClause : [{ user: { firstName: "asc" } }],
    skip: (page - 1) * appConfig.ITEMS_PER_PAGE,
    take: appConfig.ITEMS_PER_PAGE,
  };

  try {
    const teachers = await prisma.teacherProfile.findMany(queryOptions) as TeacherWithUserAndSchool[];

    const total = await prisma.teacherProfile.count({
      where: whereClause,
    });

    return { teachers, total };
  } catch (error) {
    console.error("Error fetching teachers:", error);
    throw new Error("Failed to fetch teachers.");
  }
}

export async function getTeacher(teacherId: string) {
  try {
    const teacher = await prisma.teacherProfile.findUnique({
      where: { id: teacherId },
      include: {
        user: true,
        school: true,
      },
    });

    return teacher;
  } catch (error) {
    console.error("Error fetching teacher:", error);
    throw new Error("Failed to fetch teacher.");
  }
}

export async function getTeacherOptions(sessionId: string) {
  try {
    const users = await prisma.user.findMany({
      where: {
        OR: [
          { teacherProfile: { sessionId }, status: "APPROVED" },
          { role: 'ADMIN' }
        ]
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      },
    });
    
    return users.map(user => ({
      id: user.id,
      name: `${user.firstName} ${user.lastName}`,
      email: user.email,
    }));
  } catch (error) {
    console.log(error)
    return []
  }
}

// Teacher Dashboard
export async function getTeachersDashboardData (teacherId: string) {
  try {
    const [totalAssignedCourses, totalCompletedCourses, totalStudents, upcomingMeetings, courseProgress, recentlyCompletedCourse] = await Promise.all([
      prisma.course.count({
        where: { teacherAssignments: { some: { user: { teacherProfile: { id: teacherId } } } } }
      }),
      prisma.studentProgress.count({
        where: { course: { teacherAssignments: { some: { user: { teacherProfile: { id: teacherId} } } } } }
      }),
      prisma.enrollment.count({
        where: { course: { teacherAssignments: { some: { user: { teacherProfile: { id: teacherId } } } } } }
      }),
      prisma.meeting.findMany({
        where: { course: { teacherAssignments: { some: { user: { teacherProfile: { id: teacherId } } } } }, scheduledAt: { gt: new Date() } },
        select: {
          id: true,
          title: true,
          description: true,
          scheduledAt: true,
          course: {
            select: {
              title: true
            }
          }
        }
      }),
      prisma.course.findMany({
        where: { teacherAssignments: { some: { user: { teacherProfile: { id: teacherId } } } } },
        select: {
          id: true,
          title: true,
          _count: {
            select: {
              enrollments: true,
              progress: {
                where: { progress: 100 }
              }
            }
          }
        }
      }),
      prisma.studentProgress.findMany({
        where: { course: { teacherAssignments: { some: { user: { teacherProfile: { id: teacherId } } } } }, progress: 100 },
        select: {
          id: true,
          score: true,
          course: {
            select: {
              title: true,
            }
          },
          student: {
            select: {
              id: true,
              user: {
                select: {
                  firstName: true,
                  lastName: true
                }
              },
              school: {
                select: {
                  name: true
                }
              }
            }
          }
        }
      })
    ])
    
    return {
      count: {
        totalAssignedCourses,
        totalCompletedCourses,
        totalStudents
      },
      upcomingMeetings: upcomingMeetings.map(meeting => ({
        id: meeting.id,
        name: meeting.title,
        description: meeting.description || '',
        course: meeting.course.title,
        dateTime: meeting.scheduledAt
      })),
      courseProgress: courseProgress ? courseProgress.map(course => ({
        id: course.id,
        courseName: course.title,
        students: course._count.enrollments,
        completed: course._count.progress
      })) : [],
      recentlyCompletedCourse: recentlyCompletedCourse ? recentlyCompletedCourse.map(course => ({
        id: course.id,
        studentId: course.student.id,
        course: course.course.title,
        studentName: course.student.user.firstName + " " + course.student.user.lastName,
        school: course.student.school.name,
        grade: course.score
      })) : []
    }
  } catch (error) {
    console.log(error)
    return null
  }
}