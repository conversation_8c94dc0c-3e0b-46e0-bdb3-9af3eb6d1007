"use client";

import { FormInputField } from "@/components/form-element/input-field";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { assignScore } from "@/lib/server/action/students/activities/activities.action";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, X } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

type StudentResponse = {
  id: string;
  response: string;
  submittedAt: Date;
  status: string;
  score: number | null;
};

export default function ActivityModal({
  response,
}: {
  response: StudentResponse;
}) {
  const [isOpen, setIsOpen] = useState(false);

  const form = useForm<{ score: number }>({
    resolver: zodResolver(
      z.object({
        score: z.coerce.number().min(0).max(100),
      })
    ),
    defaultValues: {
      score: response.score ?? 0,
    },
    mode: "onChange",
  });

  const onSubmit = async (values: { score: number }) => {
    const res = await assignScore(response.id, values.score);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <>
      <Button size="icon" variant="ghost" onClick={() => setIsOpen(true)}>
        <Eye />
      </Button>
      {isOpen ? (
        <div className="flex justify-center items-center bg-black/50 fixed inset-0 z-50">
          <div className="relative w-full max-h-[70%] max-w-7xl bg-white p-6 rounded-lg overflow-y-auto space-y-6">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsOpen(false)}
              className="absolute top-2 right-2"
            >
              <X />
            </Button>
            <div className="flex border-2 p-6 rounded-lg mt-6">
              {response.response.split("\n\n").map((paragraph, index) => (
                <p key={index} className="text-sm mb-4 leading-relaxed">
                  {paragraph}
                </p>
              ))}
              {/* <p className="text-sm wrap-normal">{response.response}</p> */}
            </div>
            <div className="flex border-2 px-6 py-4 rounded-lg w-full">
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4 w-full"
                >
                  <FormInputField
                    control={form.control}
                    name="score"
                    label="Score"
                    type="number"
                  />
                  <div className="flex justify-end gap-2">
                    <Button
                      type="submit"
                      disabled={form.formState.isSubmitting}
                    >
                      {form.formState.isSubmitting ? "Assigning..." : "Assign"}
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </div>
      ) : null}
    </>
  );
}
