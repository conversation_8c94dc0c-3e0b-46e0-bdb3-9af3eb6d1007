'use server'

import prisma from "@/lib/prisma"

export type Question = {
  id: string;
  type: "multiple-choice" | "true-false";
  question: string;
  options?: string[];
  correctAnswer: string;
};

export async function getQuestions(assessmentId: string) {
  try {
    const questions = await prisma.question.findMany({
      where: { assessmentId },
      select: {
        id: true,
        question: true,
        questionType: true,
        options: true,
        correctAnswer: true,
        order: true
      },
      orderBy: { order: 'asc' }
    })

    return questions.map(question => ({
      id: question.id,
      type: question.questionType as "multiple-choice" | "true-false",
      question: question.question,
      options: JSON.parse(question.options as string),
      correctAnswer: question.correctAnswer,
      order: question.order,
    }))
  } catch (error) {
    console.log(error)
    return []
  }
}