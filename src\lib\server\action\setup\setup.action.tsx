"use server";

import prisma from "@/lib/prisma";
import bcrypt from "bcryptjs";
import { revalidatePath } from "next/cache";

interface AdminData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
}

interface SessionData {
  name: string;
  startDate: Date | undefined;
  endDate: Date | undefined;
  description: string;
}

export async function setupAction({
  adminData,
  sessionData,
}: {
  adminData: AdminData;
  sessionData: SessionData;
}) {
  try {
    const user = await prisma.user.findFirst({});
    const session = await prisma.session.findFirst({});
    if (user || session) {
      return { success: false, error: "Setup already completed" };
    }

    const hashedPassword = await bcrypt.hash(adminData.password!, 10);

    await prisma.adminProfile.create({
      data: {
        password: hashedPassword,
        isFirst: true,
        user: {
          create: {
            firstName: adminData.firstName,
            lastName: adminData.lastName,
            email: adminData.email,
            phone: adminData.phone,
            role: "ADMIN" as const,
            status: "APPROVED" as const,
          },
        },
      },
    });

    const oneYearFromNow = new Date();
    oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);

    await prisma.session.create({
      data: {
        name: sessionData.name,
        description: sessionData.description,
        startDate: sessionData.startDate ? sessionData.startDate : new Date(),
        endDate: sessionData.endDate ? sessionData.endDate : oneYearFromNow,
        isActive: true,
      },
    });

    revalidatePath("/");

    return {
      success: true,
      message: "Setup completed successfully",
    };
  } catch (error) {
    console.log(error);
    return { success: false, error: "Failed to create session" };
  }
}
