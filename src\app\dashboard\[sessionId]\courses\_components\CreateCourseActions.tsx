"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Eye, Save } from "lucide-react";

export default function CreateCourseActions() {
  return (
    <div className="flex gap-2">
      <Button
        variant="outline"
        // onClick={() => handleSubmit("draft")}
      >
        <Save className="w-4 h-4 mr-2" />
        Save as Draft
      </Button>
      <Button
      // onClick={() => handleSubmit("published")}
      >
        <Eye className="w-4 h-4 mr-2" />
        Publish Course
      </Button>
    </div>
  );
}
