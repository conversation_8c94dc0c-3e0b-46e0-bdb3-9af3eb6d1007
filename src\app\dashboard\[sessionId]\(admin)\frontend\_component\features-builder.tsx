"use client";

import { useCallback, useEffect, useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Save, Loader2, AlertTriangle } from "lucide-react";
import {
  CMSData,
  updateCMSData,
} from "@/lib/server/action/frontend/frontend.action";
import { toast } from "sonner";
import {
  deleteFromBunnyCDN,
  uploadToBunnyCDN,
} from "@/lib/server/action/bunny/bunny.action";
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import { Alert, AlertDescription } from "@/components/ui/alert";
import CoreFeatures from "./sections/core-features";
import DesignElement from "./sections/design-element";

export default function FeaturesBuilder({ cmsData }: { cmsData: CMSData }) {
  const [data, setData] = useState<CMSData | null>(cmsData);
  const [isSaving, setIsSaving] = useState(false);
  const [uploadingStates, setUploadingStates] = useState<{
    [key: string]: boolean;
  }>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const markAsChanged = () => {
    if (!hasUnsavedChanges) {
      setHasUnsavedChanges(true);
    }
  };

  const markAsSaved = () => {
    setHasUnsavedChanges(false);
  };

  // Use the unsaved changes hook
  const { resetUnsavedChanges } = useUnsavedChanges({
    hasUnsavedChanges,
    message:
      "You have unsaved changes to your CMS content. Are you sure you want to leave without saving?",
  });

  const handleSave = useCallback(async () => {
    if (!data) return;

    setIsSaving(true);
    try {
      await updateCMSData(data);
      markAsSaved();
      resetUnsavedChanges();
      toast.success("Features updated successfully!");
    } catch (error) {
      console.error("Error saving changes:", error);
      toast.error("Failed to save changes. Please try again.");
    } finally {
      setIsSaving(false);
    }
  }, [data, resetUnsavedChanges]);

  // Add keyboard shortcut for saving (Ctrl+S / Cmd+S)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === "s") {
        e.preventDefault();
        if (hasUnsavedChanges && !isSaving) {
          handleSave();
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [hasUnsavedChanges, handleSave, isSaving]);

  // File input refs for features and design elements
  const featureFileInputRefs = useRef<{
    [key: number]: HTMLInputElement | null;
  }>({});
  const designFileInputRefs = useRef<{
    [key: number]: HTMLInputElement | null;
  }>({});

  const updateFeature = (
    index: number,
    field: string,
    value: string | string[]
  ) => {
    if (!data) return;

    const newData = { ...data };
    newData.features.items[index] = {
      ...newData.features.items[index],
      [field]: value,
    };
    setData(newData);
    markAsChanged();
  };

  const updateDesignElement = (
    index: number,
    field: string,
    value: string | string[]
  ) => {
    if (!data) return;

    const newData = { ...data };
    newData.about.designElements[index] = {
      ...newData.about.designElements[index],
      [field]: value,
    };
    setData(newData);
    markAsChanged();
  };

  const handleImageUpload = async (
    file: File,
    index: number,
    type: "feature" | "design"
  ) => {
    const uploadKey = `${type}-${index}`;
    setUploadingStates((prev) => ({ ...prev, [uploadKey]: true }));

    try {
      const formData = new FormData();
      formData.append("file", file);

      const result = await uploadToBunnyCDN(formData, "cms/images");

      if (result.success && result.url) {
        if (type === "feature") {
          updateFeature(index, "image", result.url);
        } else {
          updateDesignElement(index, "image", result.url);
        }

        toast.success("Image uploaded successfully!");
      } else {
        toast.error(result.error || "Failed to upload image");
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("An error occurred while uploading the image");
    } finally {
      setUploadingStates((prev) => ({ ...prev, [uploadKey]: false }));
    }
  };

  const triggerFileInput = (index: number, type: "feature" | "design") => {
    if (type === "feature") {
      featureFileInputRefs.current[index]?.click();
    } else {
      designFileInputRefs.current[index]?.click();
    }
  };

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    index: number,
    type: "feature" | "design"
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      handleImageUpload(file, index, type);
    }
    // Reset the input value so the same file can be selected again
    event.target.value = "";
  };

  const replaceImage = async (
    index: number,
    type: "feature" | "design",
    oldImageUrl: string
  ) => {
    // Delete old image if it's from Bunny CDN
    if (
      oldImageUrl &&
      oldImageUrl.includes(process.env.NEXT_PUBLIC_BUNNY_CDN_URL || "")
    ) {
      try {
        await deleteFromBunnyCDN(oldImageUrl);
      } catch (error) {
        console.error("Failed to delete old image:", error);
      }
    }

    // Trigger file input for new image
    triggerFileInput(index, type);
  };

  if (!data) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">
            Failed to load features data
          </h2>
          <p className="text-muted-foreground mb-4">
            There was an error loading the features management data.
          </p>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="border-b pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold tracking-tight">
              Features Management
            </h1>
            {hasUnsavedChanges && (
              <span className="text-sm text-orange-600 font-medium">
                • Unsaved changes
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {hasUnsavedChanges && (
              <span className="text-xs text-gray-500">
                Press Ctrl+S to save
              </span>
            )}
            <Button
              onClick={handleSave}
              disabled={isSaving}
              size="sm"
              className={`flex items-center space-x-1 ${
                hasUnsavedChanges ? "bg-orange-600 hover:bg-orange-700" : ""
              }`}
            >
              {isSaving ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              <span>
                {isSaving
                  ? "Saving..."
                  : hasUnsavedChanges
                  ? "Save Changes *"
                  : "Save Changes"}
              </span>
            </Button>
          </div>
        </div>
      </div>

      {hasUnsavedChanges && (
        <Alert className="mt-4 border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            You have unsaved changes. Don&apos;t forget to save your work before
            leaving the page.
          </AlertDescription>
        </Alert>
      )}

      <CoreFeatures
        data={data}
        setData={setData}
        markAsChanged={markAsChanged}
        uploadingStates={uploadingStates}
        setUploadingStates={setUploadingStates}
        handleFileChange={handleFileChange}
        triggerFileInput={triggerFileInput}
        replaceImage={replaceImage}
        featureFileInputRefs={featureFileInputRefs}
      />

      <DesignElement
        data={data}
        setData={setData}
        markAsChanged={markAsChanged}
        uploadingStates={uploadingStates}
        setUploadingStates={setUploadingStates}
        handleFileChange={handleFileChange}
        triggerFileInput={triggerFileInput}
        replaceImage={replaceImage}
        updateDesignElement={updateDesignElement}
        designFileInputRefs={designFileInputRefs}
      />
    </div>
  );
}
