"use client";

import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import CustomDialog from "@/components/shared/CustomDialog";
import { CustomDropdown } from "@/components/shared/Dropdown";
import { Button } from "@/components/ui/button";
import {
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  approveStudent,
  rejectStudent,
  StudentWithUserAndSchool,
} from "@/lib/server/action/students";
import { ColumnDef } from "@tanstack/react-table";
import { EllipsisVertical } from "lucide-react";
import { toast } from "sonner";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import ViewStudent from "../../../_components/view/ViewStudent";

export const columns: ColumnDef<StudentWithUserAndSchool>[] = [
  {
    accessorKey: "name",
    header: () => <div className="ml-2">Name</div>,
    cell: ({ row }) => {
      const image = row.original.user.fileUrl as string;
      return (
        <div className="ml-2 flex gap-3 items-center">
          <Avatar>
            {image && <AvatarImage src={image} alt="user photo" />}
            <AvatarFallback>{`${row.original.user.firstName[0].toLocaleUpperCase()}${row.original.user.lastName[0].toLocaleUpperCase()}`}</AvatarFallback>
          </Avatar>
          <div className="font-medium truncate">{`${row.original.user.firstName} ${row.original.user.lastName}`}</div>
        </div>
      );
    },
  },
  {
    accessorKey: "user.email",
    header: "Email",
    cell: ({ row }) => row.original.user.email,
  },
  {
    accessorKey: "user.phone",
    header: "Phone",
    cell: ({ row }) => row.original.user.phone,
  },
  {
    accessorKey: "school.name",
    header: "School",
    cell: ({ row }) => row.original.school.name,
  },
  {
    accessorKey: "program.name",
    header: "Program",
    cell: ({ row }) => row.original.program.name,
  },
  {
    accessorKey: "course.name",
    header: "Course",
    cell: ({ row }) => row.original.enrollment?.course.title,
  },
  {
    accessorKey: "user.status",
    header: "Status",
    cell: ({ row }) => (
      <div
        className={`capitalize ${
          row.original.user.status === "APPROVED"
            ? "text-green-600"
            : row.original.user.status === "PENDING"
            ? "text-amber-600"
            : "text-red-600"
        }`}
      >
        {row.original.user.status.toLowerCase()}
      </div>
    ),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const student = row.original;

      return (
        <CustomDropdown
          trigger={
            <Button size="icon" variant="ghost">
              <EllipsisVertical />
            </Button>
          }
        >
          <DropdownMenuItem asChild>
            <CustomDialog
              title="Approve Student"
              description={`Do you want to approve ${student.user.firstName} ${student.user.lastName}?`}
              asChild={false}
              trigger="Approve"
              footer
              onConfirm={async () => {
                const res = await approveStudent(student.id);
                if (res.success) {
                  toast.success(res.message);
                } else {
                  toast.error(res.error);
                }
              }}
            />
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <CustomDialog asChild={false} trigger="View" title="User Details">
              <ViewStudent
                userPhoto={student.user.fileUrl}
                firstName={student.user.firstName}
                lastName={student.user.lastName}
                status={student.user.status}
                email={student.user.email}
                school={student.school.name}
                course={student.enrollment?.course.title || ""}
                program={student.program.name}
              />
            </CustomDialog>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <CustomAlertDialog
              asChild={false}
              trigger="Reject"
              onConfirm={async () => {
                const res = await rejectStudent(student.id);
                toast(res.message);
              }}
            />
          </DropdownMenuItem>
        </CustomDropdown>
      );
    },
  },
];
