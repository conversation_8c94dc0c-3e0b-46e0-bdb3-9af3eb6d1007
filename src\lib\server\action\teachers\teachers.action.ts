'use server'

import prisma from '@/lib/prisma';
import { teacherSchema, TTeacherForm, TUpdateTeacherForm } from './teacher.schema';
import { revalidatePath } from 'next/cache';
import { deleteFromBunny } from '../bunny/bunny.action';
import { approvedMail, rejectedMail, welcomeMail } from '@/lib/emails/welcome';


export async function createTeacher (unsafeData: TTeacherForm, sessionId: string, isAdmin: boolean) {
  try {
    if (!unsafeData || !sessionId) return { success: false, message: "Please provide all required fields" };

    const {success, data, error } = teacherSchema.safeParse(unsafeData);
    if (!success) {
      const errorMessage = error.errors.map((error) => error.message).join(",")
      return { success: false, message: errorMessage };
    }

    const [existingUser, siteName] = await Promise.all([
      prisma.user.findUnique({
        where: { email: data.email },
      }),
      prisma.cMSConfig.findFirst({
        select: { siteName: true },
      })
    ]);

    if (existingUser) {
      return { success: false, message: "User with this email already exists" };
    }

    const teacher = await prisma.teacherProfile.create({
      data: {
        user: {
          create: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            phone: data.phone,
            role: "TEACHER" as const,
            status: isAdmin ? "APPROVED" as const : "PENDING" as const,
          },
        },
        school: { connect: { id: data.school } },
        session: { connect: { id: sessionId } },
      },
      include: {
        user: true,
        school: true,
      },
    });

    welcomeMail(data.email, data.firstName, siteName?.siteName || 'Complexus Pathways')

    if (isAdmin) return approveTeacher(teacher.id)

    revalidatePath('/dashboard/teachers')
    revalidatePath('/dashboard/codes')

    return { success: true, message: 'Teacher created' };
  } catch (error) {
    console.error("Error creating teacher:", error);
    return { success: false, message: "Failed to create teacher" };
  }
};

export async function approveTeacher(teacherId: string) {
  try {
    if (!teacherId) return { success: false, message: "Please provide all required fields" };

    const [teacher, siteName] = await Promise.all([
      prisma.teacherProfile.findUnique({
        where: { id: teacherId },
        select: {
          userId: true,
          sessionId: true,
          user: {
            select: {
              email: true,
              firstName: true,
            }
          }
        },
      }),
      prisma.cMSConfig.findFirst({
        select: { siteName: true },
      })
    ]);

    if (!teacher) {
      return { success: false, message: "Teacher not found" };
    }

    const loginCode = `code-${Date.now().toString(36)}${Math.random().toString(36).slice(2, 8)}`

    await prisma.$transaction([
      prisma.teacherProfile.update({
        where: { id: teacherId },
        data: {
          user: {
            update: {
              status: "APPROVED" as const,
            },
          },
          loginCode,
        },
      }),
      prisma.code.create({
        data: {
          user: { connect: { id: teacher.userId } },
          code: loginCode,
          type: "TEACHER" as const,
          session: { connect: { id: teacher.sessionId } },
        },
      }),
    ])

    approvedMail({
      email: teacher.user.email,
      firstName: teacher.user.firstName,
      loginCode,
      link: `${process.env.NEXT_PUBLIC_APP_URL}/sign-in?type=lc`,
      siteName: siteName?.siteName || 'Complexus Pathways',
    })

    revalidatePath('/dashboard/teachers')

    return { success: true, message: 'Teacher approved' };
  } catch (error) {
    console.error("Error approving teacher:", error);
    return { success: false, message: "Failed to approve teacher" };
  }
};

export async function updateTeacher (teacherId: string, unsafeData: Partial<TUpdateTeacherForm>) {
  try {
    if (!unsafeData) return { success: false, message: "Please provide all required fields" };

    const {success, data, error } = teacherSchema.safeParse(unsafeData);
    if (!success) {
      const errorMessage = error.errors.map((error) => error.message).join(",")
      return { success: false, message: errorMessage };
    }

    await prisma.teacherProfile.update({
      where: { id: teacherId },
      data: {
        user: {
          update: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            phone: data.phone,
          },
        },
        school: { connect: { id: data.school } },
      },
      include: {
        user: true,
        school: true,
      },
    });

    revalidatePath('/dashboard/teachers')

    return { success: true, message: 'Teacher updated' };
  } catch (error) {
    console.error("Error updating teacher:", error);
    return { success: false, error: "Failed to update teacher" };
  }
};

export async function rejectTeacher (teacherId: string) {
  try {
    const [teacher, siteName] = await Promise.all([
      prisma.teacherProfile.findUnique({
        where: { id: teacherId },
        select: {
          userId: true,
          user: {
            select: {
              email: true,
              firstName: true,
            }
          }
        }
      }),
      prisma.cMSConfig.findFirst({
        select: { siteName: true },
      })
    ])
    
    if (!teacher) {
      return { success: false, error: "Teacher not found" };
    }

    await prisma.teacherProfile.delete({
      where: { id: teacherId },
    });

    rejectedMail({
      email: teacher.user.email,
      firstName: teacher.user.firstName,
      siteName: siteName?.siteName || 'Complexus Pathways',
    })

    revalidatePath('/dashboard/teachers')

    return { success: true, message: 'Teacher approval rejected' };
  } catch (error) {
    console.error("Error rejecting teacher:", error);
    return { success: false, error: "Failed to reject teacher" };
  }
};


export async function deleteTeacher (teacherId: string) {
  try {
    const teacher = await prisma.teacherProfile.delete({
      where: { id: teacherId },
    });
    
    const user = await prisma.user.delete({
      where: { id: teacher.userId },
      select: { fileId: true }
    });

    if (user?.fileId) {
      await deleteFromBunny(user.fileId)
    }

    revalidatePath('/dashboard/teachers')

    return { success: true };
  } catch (error) {
    console.error("Error deleting teacher:", error);
    return { success: false, error: "Failed to delete teacher" };
  }
};
