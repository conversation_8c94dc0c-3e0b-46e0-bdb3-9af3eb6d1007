import { z } from "zod";

export const userSchema = z.object({
  firstName: z.string().min(1, { message: "Please provide your first name" }),
  lastName: z.string().min(1, { message: "Please provide your last name" }),
  email: z
    .string({ required_error: "Email field is required" })
    .email({ message: "Please provide a valid email" }),
  phone: z.string().min(1, { message: "Please provide your phone number" }),
});

export const changeUserPasswordSchema = z.object({
  email: z
    .string({ required_error: "Email field is required" })
    .email({ message: "Please provide a valid email" }),
  password: z
    .string()
    .min(8, { message: "Your password must have a minimum of 8 characters" }),
    confirmPassword: z
    .string()
    .min(8, { message: "Your password must have a minimum of 8 characters" }),
}).refine(
  (values) => {
    return values.password === values.confirmPassword;
  },
  {
    message: "Passwords must match!",
    path: ["confirmPassword"],
  }
);

export const updateUserSchema = z.object({
  firstName: z.string().min(1, { message: "Please provide your first name" }),
  lastName: z.string().min(1, { message: "Please provide your last name" }),
  email: z
    .string({ required_error: "Email field is required" })
    .email({ message: "Please provide a valid email" }),
  phone: z.string().min(1, { message: "Please provide your phone number" }),
});

//Types
export type User = z.infer<typeof userSchema>;
export type TUpdateUserForm = z.infer<typeof updateUserSchema>
export type TChangePasswordForm = z.infer<typeof changeUserPasswordSchema>;
