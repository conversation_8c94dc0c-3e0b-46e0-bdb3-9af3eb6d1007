import { Card, CardContent } from "@/components/ui/card";

export default function CourseStatusCard({
  total = 0,
  draft = 0,
  published = 0,
  students = 0,
}: {
  total: number;
  draft: number;
  published: number;
  students: number;
}) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
      <Card className="py-0">
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-blue-600">{total}</div>
          <div className="text-sm text-gray-600">Total Courses</div>
        </CardContent>
      </Card>
      <Card className="py-0">
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-green-600">{published}</div>
          <div className="text-sm text-gray-600">Published</div>
        </CardContent>
      </Card>
      <Card className="py-0">
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-yellow-600">{draft}</div>
          <div className="text-sm text-gray-600">Drafts</div>
        </CardContent>
      </Card>
      <Card className="py-0">
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-purple-600">{students}</div>
          <div className="text-sm text-gray-600">Total Students</div>
        </CardContent>
      </Card>
    </div>
  );
}
