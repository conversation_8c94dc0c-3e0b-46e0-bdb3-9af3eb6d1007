'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache";
import { TProgramForm } from "./program.schema";


export async function createProgram(sessionId: string, data: TProgramForm) {
  try {
    await prisma.program.create({
      data: {
        name: data.name,
        description: data.description,
        order: await getNextProgramOrder(),
        session: { connect: { id: sessionId } },
      },
    })
    
    revalidatePath('/admin/programs')

    return { success: true, message: 'Program created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create program' }
  }
}

export async function updateProgram(programId: string, data: TProgramForm) {
  try {
    await prisma.program.update({
      where: { id: programId },
      data: {
        name: data.name,
        description: data.description,
      },
    })

    revalidatePath('/admin/programs')

    return { success: true, message: 'Program updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update program' }
  }
}

export async function deleteProgram(programId: string) {
  try {
    await prisma.program.delete({
      where: { id: programId }
    })
    
    revalidatePath('/admin/programs')
    revalidatePath('/admin/courses')
    
    return { success: true }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete program' }
  }
}

async function getNextProgramOrder(): Promise<number> {
  const lastProgram = await prisma.program.findFirst({
    orderBy: { order: 'desc' },
    select: { order: true },
  });
  return lastProgram && typeof lastProgram.order === 'number' ? lastProgram.order + 1 : 1;
}

