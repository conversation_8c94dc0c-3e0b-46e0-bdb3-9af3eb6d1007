'use server'

import prisma from "@/lib/prisma"

// export async function getAssessments() {
//   try {
//     const assessments = await prisma.assessment.findMany({
//       include: {
//         questions: true,
//         course: true,
//         module: true
//       },
//       orderBy: { order: 'asc' }
//     })
    
//     return assessments
//   } catch (error) {
//     console.log(error)
//     return []
//   }
// }

export async function getAssessment(moduleId: string) {
  try {
    const assessment = await prisma.assessment.findUnique({
      where: { moduleId },
      select: {
        id: true,
        passingScore: true,
        maxStudentQuestions: true,
        instructions: true,
        _count: {
          select: {
            questions: true
          }
        },
        module: {
          select: {
            title: true
          }
        }
      }
    })
    
    return assessment
  } catch (error) {
    console.log(error)
    return null
  }
}