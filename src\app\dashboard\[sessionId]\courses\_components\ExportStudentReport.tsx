"use client";

import { But<PERSON> } from "@/components/ui/button";
import { getStudentsReport } from "@/lib/server/action/students/reports/reports.list";
import { Download, Loader } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import * as XLSX from "xlsx-js-style";

type StudentsReport = {
  courseName: string;
  studentName: string;
  modules: {
    name: string;
    attempts: number;
    score: number;
  }[];
  totalAssessmentScore: number;
  totalAssessmentPercentage: number;
  activityTest: {
    passScore: number;
    name: string;
    score: number;
  }[];
  totalActivityScore: number;
  totalActivityPercentage: number;
}[];

export default function ExportStudentReport({
  courseId,
  siteName,
}: {
  courseId: string;
  siteName: string;
}) {
  const [loading, setLoading] = useState(false);
  const [report, setReport] = useState<StudentsReport | null>(null);
  const [preparing, setPreparing] = useState(false);

  useEffect(() => {
    async function fetchReport() {
      setPreparing(true);
      try {
        const report = await getStudentsReport(courseId);
        setReport(report);
      } catch (error) {
        console.log(error);
      } finally {
        setPreparing(false);
      }
    }
    fetchReport();
  }, [courseId]);

  if (preparing) {
    return (
      <div className="flex flex-col justify-center items-center gap-4">
        <p className="text-sm font-medium text-gray-500">Preparing Report</p>
        <Loader className="mr-2 h-4 w-4 animate-spin" />
      </div>
    );
  }

  const exportToExcel = (report: StudentsReport) => {
    setLoading(true);
    try {
      // 1. Define the custom header rows
      const customHeaderRows = [
        [`${siteName} - ${report[0].courseName}`],
        [`Generated on: ${new Date().toLocaleString()}`],
        [`Total Student: ${report.length}`],
        [], // An empty row for spacing between custom header and data header
      ];

      // 2. Define the data headers for the report
      const dataHeaders = [
        "Student Name",
        ...Array.from(
          { length: report[0].modules.length },
          (_, i) => `M${i + 1} (100%)`
        ),
        "Total Attempts",
        ...Array.from(
          { length: report[0].activityTest.length },
          (_, i) => `A${i + 1} (${report[0].activityTest[i].passScore})`
        ),
        "Modules grade",
        "Activity score",
        "Initial Grade",
      ] as string[];

      // 3. Prepare the data for the report table
      const reportDataRows = report.map((result) => [
        result.studentName,
        ...result.modules.map((module) => module.score),
        result.modules.reduce((sum, module) => sum + module.attempts, 0),
        ...result.activityTest.map((activity) => activity.score),
        result.totalAssessmentPercentage.toFixed(0),
        result.totalActivityPercentage.toFixed(0),
        Math.round(
          (result.totalAssessmentPercentage + result.totalActivityPercentage) /
            2
        ),
      ]);

      // 4. Combine all parts into a single array of arrays (AoA)
      const fullData = [...customHeaderRows, dataHeaders, ...reportDataRows];

      // 5. Create worksheet from the combined AoA
      const ws = XLSX.utils.aoa_to_sheet(fullData);

      // --- Styling and Merges ---

      // Calculate the number of rows in customHeaderRows
      const numCustomHeaderRows = customHeaderRows.length;
      const numDataHeaderCols = dataHeaders.length; // Number of columns in your data headers (e.g., 6)

      // Define merge cells for the title, date, and stats
      // Adjust `e.c` to match the number of data columns, which is `numDataHeaderCols - 1` (0-indexed)
      const merge = [
        { s: { r: 0, c: 0 }, e: { r: 0, c: numDataHeaderCols - 1 } }, // Merges A1 to last data column for main title
        { s: { r: 1, c: 0 }, e: { r: 1, c: numDataHeaderCols - 1 } }, // Merges A2 to last data column for date
        { s: { r: 2, c: 0 }, e: { r: 2, c: numDataHeaderCols - 1 } }, // Merges A3 to last data column for total report
      ];
      ws["!merges"] = merge;

      // Add basic styling to the header rows (now at their correct positions)
      const titleCell = ws["A1"];
      const dateCell = ws["A2"];
      const statsCell = ws["A3"];

      if (titleCell) {
        titleCell.s = {
          font: { bold: true, sz: 16 },
          alignment: { horizontal: "center", vertical: "center" },
        };
      }

      if (dateCell) {
        dateCell.s = {
          font: { italic: true, sz: 12 },
          alignment: { horizontal: "center", vertical: "center" },
        };
      }

      if (statsCell) {
        statsCell.s = {
          font: { bold: true, sz: 12 },
          alignment: { horizontal: "center", vertical: "center" },
        };
      }

      // Style the actual report headers (now starting after custom headers)
      const dataHeaderRowIndex = numCustomHeaderRows; // This is the 0-indexed row for your data headers (which is row 5 in Excel)

      dataHeaders.forEach((_header, colIndex) => {
        const cellAddress = XLSX.utils.encode_cell({
          r: dataHeaderRowIndex,
          c: colIndex,
        });
        if (ws[cellAddress]) {
          ws[cellAddress].s = {
            font: { bold: true, color: { rgb: "FFFFFF" } }, // White text
            fill: { fgColor: { rgb: "4F81BD" } }, // A shade of blue background
            alignment: {
              horizontal: "center",
              vertical: "center",
              wrapText: true,
            }, // Center align text
            border: {
              // Add borders
              top: { style: "thin", color: { rgb: "000000" } },
              bottom: { style: "thin", color: { rgb: "000000" } },
              left: { style: "thin", color: { rgb: "000000" } },
              right: { style: "thin", color: { rgb: "000000" } },
            },
          };
        }
      });

      // Set column widths
      // The `wch` values should match your desired widths for each of the `dataHeaders` columns.
      ws["!cols"] = [
        { wch: 50 }, // Student Name
        ...Array(report[0].modules.length).fill({ wch: 6 }), // Module Scores
        { wch: 15 }, // Modules Attempts
        ...Array(report[0].activityTest.length).fill({ wch: 6 }), // Activity Scores
        { wch: 15 }, // Modules Initial Grade
        { wch: 15 }, // Attempts Initial Grade
        { wch: 15 }, // Total Grade
      ];

      // Create workbook and append sheet
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Report");

      // Write the workbook to a file
      XLSX.writeFile(wb, `${report[0].courseName}_report.xlsx`);
    } catch (error) {
      console.error("Export error:", error);
    } finally {
      setLoading(false);
    }
  };

  if (!report || report.length === 0) {
    return (
      <div className="flex flex-col justify-center items-center gap-4">
        <p className="text-sm font-medium text-gray-500">
          No report found for this course
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-center items-center gap-4">
      <Image src={"/images/excel.png"} alt="Excel" width={90} height={90} />
      <p className="text-sm font-medium text-gray-500">
        {report[0].courseName}_report.xlsx
      </p>
      <Button
        disabled={loading}
        onClick={() => exportToExcel(report)}
        className="disabled:opacity-50"
      >
        {loading ? (
          <>
            <Loader className="mr-2 h-4 w-4 animate-spin" /> Exporting to Excel
          </>
        ) : (
          <>
            <Download className="mr-2 h-4 w-4" /> Export to Excel
          </>
        )}
      </Button>
    </div>
  );
}
