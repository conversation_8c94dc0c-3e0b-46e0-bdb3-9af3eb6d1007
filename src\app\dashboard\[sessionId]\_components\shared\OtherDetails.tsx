import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

type BadgeVariant = "destructive" | "default" | "secondary";

const OtherDetails = ({
  name,
  value,
  textClass,
  badgeText,
  badgeVariant,
}: {
  name: string;
  value: string | number;
  textClass?: string;
  badgeText?: string;
  badgeVariant?: BadgeVariant;
}) => {
  const badgeColor = (variant: BadgeVariant) => {
    switch (variant) {
      case "secondary":
        return "bg-secondary text-secondary-foreground";
      case "destructive":
        return "bg-destructive text-destructive-foreground";
      default:
        return "bg-primary text-primary-foreground";
    }
  };

  return (
    <div className="grid grid-cols-4 gap-x-3">
      <span className="col-span-1 font-medium text-sm whitespace-nowrap">
        {name}:
      </span>
      <div className="col-span-3 flex items-center gap-3">
        <span
          className={cn(
            "font-medium text-sm text-muted-foreground whitespace-nowrap",
            textClass
          )}
        >
          {value}
        </span>
        {badgeText ? (
          <Badge className={cn(badgeColor(badgeVariant || "default"))}>
            {badgeText}
          </Badge>
        ) : null}
      </div>
    </div>
  );
};

export default OtherDetails;
