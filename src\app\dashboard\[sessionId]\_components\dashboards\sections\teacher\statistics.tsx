import { Card, CardContent } from "@/components/ui/card";
import { BookOpen, CheckCircle, GraduationCap } from "lucide-react";

type StatisticsProps = {
  count?: {
    totalAssignedCourses: number;
    totalCompletedCourses: number;
    totalStudents: number;
  };
};

const Icons = {
  totalAssignedCourses: BookOpen,
  totalCompletedCourses: CheckCircle,
  totalStudents: GraduationCap,
};

export default function Statistics({ count }: StatisticsProps) {
  return (
    <section className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <StatisticsCard
        title="Assigned courses"
        value={count?.totalAssignedCourses || 0}
        icon="totalAssignedCourses"
      />
      <StatisticsCard
        title="Completed courses"
        value={count?.totalCompletedCourses || 0}
        icon="totalCompletedCourses"
      />
      <StatisticsCard
        title="Total students"
        value={count?.totalStudents || 0}
        icon="totalStudents"
      />
    </section>
  );
}

function StatisticsCard({
  title,
  value,
  icon,
}: {
  title: string;
  value: number;
  icon: keyof typeof Icons;
}) {
  const Icon = Icons[icon];
  return (
    <Card className="text-center">
      <CardContent>
        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Icon className="w-6 h-6 text-gray-600" />
        </div>
        <div className="text-3xl font-bold mb-2">{value.toLocaleString()}</div>
        <div className="text-sm text-gray-600">{title}</div>
      </CardContent>
    </Card>
  );
}
