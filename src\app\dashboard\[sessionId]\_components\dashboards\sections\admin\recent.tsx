import { AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatTimeAgo } from "@/lib/formatDate";
import { Avatar, AvatarFallback } from "@radix-ui/react-avatar";
import { Loader2 } from "lucide-react";
import Link from "next/link";

type RecentProps = {
  recentStudents?: {
    id: string;
    name: string;
    program: string;
    avatar: string | null;
    createdAt: Date;
  }[];
  recentTeachers?: {
    id: string;
    name: string;
    program: string;
    avatar: string | null;
    createdAt: Date;
  }[];
  sessionId: string;
};

export default function Recent({
  recentStudents,
  recentTeachers,
  sessionId,
}: RecentProps) {
  return (
    <section className="grid lg:grid-cols-2 gap-6 mt-6">
      <RecentCard
        title="Recent Students"
        link={`/dashboard/${sessionId}/students`}
        recentData={recentStudents}
      />
      <RecentCard
        title="Recent Teachers"
        link={`/dashboard/${sessionId}/teachers`}
        recentData={recentTeachers}
      />
    </section>
  );
}

function RecentCard({
  title,
  link,
  recentData,
}: {
  title: string;
  link: string;

  recentData?: {
    id: string;
    name: string;
    program: string;
    avatar: string | null;
    createdAt: Date;
  }[];
}) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          <Button variant="link" className="p-0 h-auto text-sm" asChild>
            <Link href={link}>View all</Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {!recentData ? (
          <div className="flex justify-center items-center py-12 w-full">
            <Loader2 className="animate-spin" />
          </div>
        ) : recentData.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No recent data found.</p>
          </div>
        ) : (
          <>
            {recentData.map((data) => (
              <div
                key={data.id}
                className="flex items-center justify-between gap-3"
              >
                <div className="flex items-center gap-3">
                  <Avatar className="w-8 h-8">
                    {data.avatar && (
                      <AvatarImage src={data.avatar} alt="user photo" />
                    )}
                    <AvatarFallback>
                      {data.name
                        .split(" ")
                        .filter(Boolean)
                        .filter((_, i, arr) => i === 0 || i === arr.length - 1)
                        .map((word) => word[0])
                        .join("")
                        .toLocaleUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium text-sm">{data.name}</div>
                    <div className="text-xs text-gray-500">{data.program}</div>
                  </div>
                </div>
                <p className="text-sm text-gray-500">
                  {formatTimeAgo(data.createdAt)}
                </p>
              </div>
            ))}
          </>
        )}
      </CardContent>
    </Card>
  );
}
