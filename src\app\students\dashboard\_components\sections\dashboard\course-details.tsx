import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users } from "lucide-react";
import Image from "next/image";

type CourseDetailsProps = {
  courseData?: {
    title: string;
    description: string;
    program: string;
    teacher: string;
    image: string | null;
    totalStudents: number;
  } | null;
};

export default function CourseDetails({ courseData }: CourseDetailsProps) {
  return (
    <Card className="col-span-3 gap-0 py-4">
      <CardHeader>
        <CardTitle className="text-lg">My course</CardTitle>
      </CardHeader>
      <CardContent>
        {!courseData ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No recent courses found.</p>
          </div>
        ) : (
          <>
            <div className="flex gap-4">
              <div className="w-48 h-auto relative">
                <Image
                  src={courseData.image || "/images/placeholder.svg"}
                  alt="Course"
                  fill
                  className="object-cover size-full rounded-lg"
                />
              </div>
              <div className="w-full flex flex-col justify-between">
                <div>
                  <Badge className="bg-green-500">{courseData.program}</Badge>
                  <p className="text-sm text-muted-foreground line-clamp-4 mt-2">
                    {courseData.description}
                  </p>
                </div>

                <div className="flex flex-col md:flex-row gap-2 md:items-center md:justify-between border-t pt-2">
                  <div className="flex items-center gap-2">
                    <Avatar className="w-6 h-6">
                      <AvatarFallback>
                        {courseData.teacher
                          .split(" ")
                          .filter(Boolean)
                          .filter(
                            (_, i, arr) => i === 0 || i === arr.length - 1
                          )
                          .map((word) => word[0])
                          .join("")
                          .toLocaleUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm">{courseData.teacher}</span>
                  </div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Users className="w-4 h-4" />
                    <span>
                      {courseData.totalStudents.toLocaleString() || 0} students
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
