"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { DropdownMenuCheckboxItemProps } from "@radix-ui/react-dropdown-menu";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Filter } from "lucide-react";

type Checked = DropdownMenuCheckboxItemProps["checked"];

type Props = {
  filterItems: {
    name: string;
    value: string;
  }[];
};

export function TableFilter({ filterItems }: Props) {
  const router = useRouter();
  const [selected, setSelected] = React.useState<string | null>(null);

  const handleCheckedChange = (value: string, checked: Checked) => {
    const newSelected = checked ? value : null;
    setSelected(newSelected);

    const params = new URLSearchParams(window.location.search);
    params.set("sortby", newSelected ?? "");
    router.push(`${window.location.pathname}?${params}`);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Filter />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-48">
        <DropdownMenuLabel>Sort By:</DropdownMenuLabel>
        {filterItems.map((item) => (
          <React.Fragment key={item.value}>
            <DropdownMenuSeparator />
            <DropdownMenuCheckboxItem
              checked={selected === item.value}
              onCheckedChange={(checked) =>
                handleCheckedChange(item.value, checked)
              }
            >
              {item.name}
            </DropdownMenuCheckboxItem>
          </React.Fragment>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
