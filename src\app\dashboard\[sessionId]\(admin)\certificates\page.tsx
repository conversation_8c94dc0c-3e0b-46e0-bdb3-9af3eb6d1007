import PageWrapper from "../../_components/layout/PageWrapper";
import { Suspense } from "react";
import Loading from "../../_components/Loading";
import { getTemplates } from "@/lib/server/action/certificates/templates";
import ImageCard from "./_components/ImageCard";

const breadcrumbItems = [
  { label: "Home", href: "/dashboard" },
  { label: "Certificates" },
];

async function SuspendedComponent({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) {
  const { sessionId } = await params;
  const { templates, total } = await getTemplates(sessionId);
  if (templates.length === 0) {
    return <div>No templates found</div>;
  }

  return (
    <div className="flex flex-col bg-muted p-4 rounded-lg">
      <p className="ml-auto text-sm font-medium px-4">Total: {total}</p>
      <ul className="flex flex-wrap justify-center gap-6 py-6 px-2">
        {templates.map((template, index) => (
          <ImageCard
            key={template.id}
            fileId={template.id}
            imageUrl={template.previewUrl}
            index={index}
          />
        ))}
      </ul>
    </div>
  );
}

export default function CertificatesPage({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) {
  return (
    <PageWrapper pgTitle="Manage Certificate" breadcrumbItems={breadcrumbItems}>
      <Suspense fallback={<Loading />}>
        <SuspendedComponent params={params} />
      </Suspense>
    </PageWrapper>
  );
}
