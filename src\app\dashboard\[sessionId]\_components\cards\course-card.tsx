"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { deleteCourse } from "@/lib/server/action/courses";
import { toast } from "sonner";

interface CourseCardProps {
  course: {
    id: string;
    title: string;
    description: string | null;
    fileUrl: string | null;
    isActive: boolean;
    program: {
      name: string;
    };
    _count: {
      enrollments: number;
      modules: number;
    };
  };
}

export default function CourseCard({ course }: CourseCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this course?")) return;

    setIsDeleting(true);
    const result = await deleteCourse(course.id);

    if (result.success) {
      toast.success("Course deleted successfully");
    } else {
      toast.error(result.error || "Failed to delete course");
    }
    setIsDeleting(false);
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {course.fileUrl && (
        <Image
          src={course.fileUrl}
          alt={course.title}
          className="w-full h-48 object-cover"
        />
      )}

      <div className="p-6">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-xl font-semibold">{course.title}</h3>
          <span
            className={`px-2 py-1 text-xs rounded ${
              course.isActive
                ? "bg-green-100 text-green-800"
                : "bg-red-100 text-red-800"
            }`}
          >
            {course.isActive ? "Active" : "Inactive"}
          </span>
        </div>

        <p className="text-sm text-gray-500 mb-2">
          Program: {course.program.name}
        </p>

        {course.description && (
          <p className="text-gray-700 mb-4 line-clamp-2">
            {course.description}
          </p>
        )}

        <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
          <span>{course._count.modules} modules</span>
          <span>{course._count.enrollments} students</span>
        </div>

        <div className="flex gap-2">
          <Link
            href={`/admin/courses/${course.id}`}
            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded text-center hover:bg-blue-700 transition-colors"
          >
            Manage
          </Link>
          <button
            onClick={handleDelete}
            disabled={isDeleting}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </button>
        </div>
      </div>
    </div>
  );
}
