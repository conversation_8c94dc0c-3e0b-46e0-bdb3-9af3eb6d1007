import { endMeeting } from "@/lib/server/action/courses/virtual-classrooms";
import { zoomService } from "@/lib/zoom";
import crypto from 'crypto';

export async function POST(req: Request) {
  const rawBody = await req.text();
  const signature = req.headers.get('x-zm-signature') || '';
  const timestamp = req.headers.get('x-zm-request-timestamp') || '';
  const zoomWebhookSecret = process.env.ZOOM_WEBHOOK_SECRET!;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let body: any;
  try {
    body = JSON.parse(rawBody);
  } catch {
    return new Response('Invalid JSON', { status: 400 });
  }

  if (body?.event === 'endpoint.url_validation') {
    const plainToken = body?.payload?.plainToken;

    const encryptedToken = crypto
      .createHmac('sha256', zoomWebhookSecret)
      .update(plainToken)
      .digest('hex');

    return new Response(JSON.stringify({
      plainToken,
      encryptedToken,
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const isValid = zoomService.verifyWebhook(rawBody, signature, timestamp);
  if (!isValid) {
    return new Response('Invalid signature', { status: 401 });
  }

  if (body?.event === 'meeting.ended') {
    const meetingId = body.payload?.object?.id;
    if (meetingId) {
      await endMeeting(meetingId);
    }
  }

  return new Response('ok');
}
