"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { getTemplates } from "@/lib/server/action/certificates/templates";
import Image from "next/image";
import { assignCertificateTemplate } from "@/lib/server/action/courses";
import { toast } from "sonner";
import { CheckCircle } from "lucide-react";

export default function CertificateTemplateSelector({
  courseId,
  templateData,
}: {
  courseId?: string;
  templateData: Awaited<ReturnType<typeof getTemplates>>;
}) {
  const templates = templateData.templates;
  const initialTemplate =
    templates.find(
      (t) => t.courses.find((c) => c.courseId === courseId)?.templateId
    ) ?? null;

  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  useEffect(() => {
    setSelectedTemplate(initialTemplate?.id ?? null);
  }, [initialTemplate]);

  const handleClick = async (templateId: string) => {
    const res = await assignCertificateTemplate(courseId as string, templateId);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  if (!templates.length) {
    return (
      <div className="space-y-6">
        <h1 className="text-xl font-semibold text-gray-900 mb-6">
          Select certificate template
        </h1>
        <p className="text-sm text-gray-600">
          No templates available. Please add a template in the certificates
          section.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-xl font-semibold text-gray-900 mb-6">
        Select certificate template
      </h1>

      <ul className="flex flex-wrap justify-center gap-6 py-6 px-2">
        {templates.map((template, index) => (
          <li key={template.id}>
            <div
              className="relative border-2 border-dashed border-gray-300 rounded-lg p-2 text-center space-y-2 cursor-pointer hover:shadow"
              onClick={() => setSelectedTemplate(template.id)}
            >
              {selectedTemplate === template.id && (
                <div className="absolute top-2 right-2 z-20">
                  <CheckCircle className="w-6 h-6 text-green-500" />
                </div>
              )}
              <Image
                src={template.previewUrl || "/images/placeholder.svg"}
                alt={`certificate template ${index + 1}`}
                width={240}
                height={208}
                className="object-contain rounded-md"
                placeholder="blur"
                blurDataURL="/images/placeholder-blur.jpg"
              />
              <p className="text-sm text-gray-600">Template {index + 1}</p>
            </div>
          </li>
        ))}
      </ul>

      <div className="flex justify-end gap-3">
        <Button
          className="px-6"
          disabled={!selectedTemplate}
          onClick={() => handleClick(selectedTemplate as string)}
        >
          Save
        </Button>
      </div>
    </div>
  );
}
