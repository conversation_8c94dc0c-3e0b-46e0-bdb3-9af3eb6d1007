'use server'

import { appConfig } from "@/config/app"
import prisma from "@/lib/prisma"

export async function getStudentActivities({ courseId, studentId, page }: {courseId: string, studentId: string, page: number }) {
  try {
    const activities = await prisma.activity.findMany({
      where: { courseId },
      orderBy: { createdAt: 'desc' },
      include: {
        responses: {
          where: { studentId },
          select: {
            id: true,
            submittedAt: true,
            status: true,
            score: true,
            isGraded: true
          },
        }
      },
      skip: (page - 1) * appConfig.ITEMS_PER_PAGE,
      take: appConfig.ITEMS_PER_PAGE
    })
    
    const total = await prisma.activity.count();

    return { activities, total }
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch student activities.")
  }
}

export async function getStudentActivity (activityId: string, studentId: string) {
  try {
    const activity = await prisma.activity.findUnique({
      where: { id: activityId },
      include: {
        responses: {
          where: { studentId },
          select: {
            id: true,
            response: true,
            submittedAt: true,
            status: true,
            score: true
          },
        },
        course: {
          select: {
            program: {
              select: {
                name: true
              }
            }
          }
        }
      }
    })

    if (!activity) return null
    
    return {
      question: activity.question,
      program: activity.course.program.name,
      response: {
        id: activity.responses[0]?.id,
        response: activity.responses[0]?.response || '',
        submittedAt: activity.responses[0]?.submittedAt,
        status: activity.responses[0]?.status,
        score: activity.responses[0]?.score || 0
      }
    }
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch student activity.")
  }
}