import { CustomSheet } from "@/components/shared/CustomSheet";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Users,
  Eye,
  CalendarIcon,
  Trash,
  PhoneOffIcon,
  Copy,
} from "lucide-react";
import CreateMeetingForm from "../../forms/create-meeting-form";
import { Meeting } from "@/lib/server/action/courses/virtual-classrooms";
import { toast } from "sonner";
import { toDateTimeLocalString, userTimezone } from "@/lib/formatDate";

interface MeetingCardProps {
  meeting: Meeting;
  userId: string;
  onJoin: (meetingId: string) => void;
  onStart: (meetingId: string) => void;
  onEnd: (meetingId: string) => void;
  onDelete: (meetingId: string) => void;
}

export default function MeetingCard({
  meeting,
  userId,
  onJoin,
  onStart,
  onEnd,
  onDelete,
}: MeetingCardProps) {
  const isCreator = meeting.creator.id === userId;
  const now = new Date();
  const startTime = new Date(meeting.scheduledAt);
  const endTime = new Date(
    meeting.scheduledAt.getTime() + meeting.duration * 60 * 1000
  );
  const canJoin =
    now >= new Date(startTime.getTime() - 10 * 60 * 1000) && now <= endTime;
  const creatorName =
    meeting.creator.firstName + " " + meeting.creator.lastName;

  const RenderCopyButton = ({
    value,
    size,
  }: {
    value: string;
    size: number;
  }) => (
    <button
      onClick={() => {
        navigator.clipboard.writeText(value);
        toast.success("Copied to clipboard");
      }}
      className="cursor-pointer"
    >
      <Copy size={size} className="text-muted-foreground" />
    </button>
  );

  return (
    <Card className="hover:shadow-lg transition-shadow h-fit">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div>
              <CardTitle className="text-lg">{meeting.title}</CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge
                  variant={
                    canJoin
                      ? "default"
                      : startTime > now
                      ? "secondary"
                      : "destructive"
                  }
                >
                  {canJoin ? "Live" : startTime > now ? "Scheduled" : "Ended"}
                </Badge>
                <p className="flex items-center text-sm text-gray-500">
                  <CalendarIcon className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" />
                  {userTimezone(meeting.scheduledAt, "PPP, p")} -{" "}
                  {userTimezone(meeting.endedAt, "p")}
                </p>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Created by: {isCreator ? "You" : creatorName}
              </p>
              {canJoin ? (
                <div>
                  <div className="flex gap-2 mt-4">
                    <p className="text-xs">
                      Join URL:{" "}
                      <span className="text-primary">
                        {meeting.zoomJoinUrl}
                      </span>
                    </p>
                    <RenderCopyButton value={meeting.zoomJoinUrl!} size={16} />
                  </div>
                  <p className="text-xs flex items-center gap-2 mt-2">
                    ZoomId:{" "}
                    <span className="text-primary">
                      {meeting.zoomMeetingId}
                    </span>
                    <RenderCopyButton
                      value={meeting.zoomMeetingId!}
                      size={13}
                    />
                  </p>
                  <p className="text-xs flex items-center gap-2">
                    Zoom Password:{" "}
                    <span className="text-primary">{meeting.zoomPassword}</span>
                    <RenderCopyButton value={meeting.zoomPassword!} size={13} />
                  </p>
                </div>
              ) : null}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-gray-600 text-sm">{meeting.description}</p>

        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Users className="w-4 h-4" />
          <span>{meeting._count.participants} participants</span>
        </div>

        <div className="flex gap-2">
          {canJoin && (
            <>
              {isCreator ? (
                <Button onClick={() => onStart(meeting.id)} className="flex-1">
                  <Users className="w-4 h-4 mr-1" />
                  Start
                </Button>
              ) : (
                <Button onClick={() => onJoin(meeting.id)} className="flex-1">
                  <Users className="w-4 h-4 mr-1" />
                  Join
                </Button>
              )}
              <Button
                variant="destructive"
                onClick={() => onEnd(meeting.id)}
                className="flex-1"
              >
                <PhoneOffIcon className="w-4 h-4 mr-1" />
                End
              </Button>
            </>
          )}
          {isCreator && !canJoin && (
            <>
              <CustomSheet
                title="Edit Meeting"
                trigger={
                  <Button variant="outline" className="flex-1">
                    <Eye className="w-4 h-4 mr-1" />
                    Edit
                  </Button>
                }
              >
                <CreateMeetingForm
                  meeting={{
                    title: meeting.title,
                    description: meeting.description || "",
                    scheduledAt: toDateTimeLocalString(meeting.scheduledAt),
                    duration: meeting.duration,
                    maxParticipants: meeting.maxParticipants,
                    waitingRoomEnabled: meeting.waitingRoomEnabled,
                    recordingEnabled: meeting.recordingEnabled,
                    chatEnabled: meeting.chatEnabled,
                    zoomEmail: meeting.zoomEmail,
                  }}
                  meetingId={meeting.id}
                  courseId={meeting.courseId}
                  userEmail={meeting.zoomEmail}
                />
              </CustomSheet>
              <Button
                variant="destructive"
                className="flex-1"
                onClick={() => onDelete(meeting.id)}
              >
                <Trash className="w-4 h-4 mr-1" />
                Delete
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
