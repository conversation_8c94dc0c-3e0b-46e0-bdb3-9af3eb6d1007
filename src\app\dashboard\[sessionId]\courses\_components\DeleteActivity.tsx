"use client";

import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { Button } from "@/components/ui/button";
import { deleteActivity } from "@/lib/server/action/courses/activities";
import { Trash2 } from "lucide-react";
import { toast } from "sonner";

export default function DeleteActivity({ activityId }: { activityId: string }) {
  return (
    <CustomAlertDialog
      trigger={
        <Button variant="destructive" size="sm">
          <Trash2 />
        </Button>
      }
      title="Delete Activity"
      description="Are you sure you want to delete this activity?"
      onConfirm={async () => {
        const res = await deleteActivity(activityId as string);
        if (res.success) {
          toast.success("Activity deleted");
        } else {
          toast.error("Failed to delete activity");
        }
      }}
    />
  );
}
