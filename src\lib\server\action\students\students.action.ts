"use server"

import prisma from '@/lib/prisma'
import { revalidatePath } from 'next/cache'
import { studentSchema, TStudentForm, TUpdateStudentForm } from './students.schema'
import { approvedMail, rejectedMail, welcomeMail } from '@/lib/emails/welcome';


export async function createStudent (unsafeData: TStudentForm, sessionId: string, isAdmin: boolean) {
  try {
    if (!unsafeData || !sessionId) return { success: false, message: "Please provide all required fields" };

    const {success, data, error } = studentSchema.safeParse(unsafeData);
    if (!success) {
      const errorMessage = error.errors.map((error) => error.message).join(",")
      console.log(errorMessage)
      return { success: false, message: errorMessage };
    }

    const [existingUser, siteName] = await Promise.all([
      prisma.user.findUnique({ where: { email: data.email } }),
      prisma.cMSConfig.findFirst({ select: { siteName: true } })
    ])

    if (existingUser) {
      return { success: false, message: "User with this email already exists" };
    }

    const student = await prisma.studentProfile.create({
      data: {
        user: {
          create: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            phone: data.phone,
            role: "STUDENT" as const,
          },
        },
        studentId: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
        school: { connect: { id: data.school } },
        program: { connect: { id: data.program } },
        courseId: data.course,
        session: { connect: { id: sessionId } },
      },
    });

    welcomeMail(data.email, data.firstName, siteName?.siteName || 'Complexus Pathways')

    if (isAdmin) return approveStudent(student.id)

    revalidatePath('/dashboard/students')

    return { success: true, message: 'Student created' };
  } catch (error) {
    console.error("Error creating student:", error);
    return { success: false, message: "Failed to create student" };
  }
}

export async function approveStudent (studentId: string) {
  try {
    if (!studentId) return { success: false, message: "Please provide all required fields" };

    const [student, siteName] = await Promise.all([prisma.studentProfile.findUnique({
        where: { id: studentId },
        select: {
          courseId: true,
          userId: true,
          schoolId: true,
          programId: true,
          sessionId: true,
          user: {
            select: {
              email: true,
              firstName: true,
            }
          }
        }
      }),
      prisma.cMSConfig.findFirst({
        select: { siteName: true },
      })
    ])

    if (!student) {
      return { success: false, error: "Student not found" };
    }

    const loginCode = `code-${Date.now().toString(36)}${Math.random().toString(36).slice(2, 8)}`

    await prisma.$transaction([
      prisma.studentProfile.update({
        where: { id: studentId },
        data: {
          user: {
            update: {
              status: "APPROVED" as const,
            },
          },
          loginCode,
        },
      }),
      prisma.code.create({
        data: {
          user: { connect: { id: student.userId } },
          code: loginCode,
          type: "STUDENT" as const,
          session: { connect: { id: student.sessionId } },
        },
      }),
      prisma.enrollment.create({
        data: {
          student: { connect: { id: studentId } },
          course: { connect: { id: student.courseId } },
          isActive: true,
        },
      }),
    ]);

    approvedMail({
      email: student.user.email,
      firstName: student.user.firstName,
      loginCode,
      link: `${process.env.NEXT_PUBLIC_APP_URL}/sign-in?type=lc`,
      siteName: siteName?.siteName || 'Complexus Pathways',
    })

    revalidatePath('/dashboard/students')
    revalidatePath('/dashboard/codes')

    return { success: true, message: 'Student approved' };
  } catch (error) {
    console.error("Error approving student:", error);
    return { success: false, error: "Failed to approve student" };
  }
};

export async function rejectStudent (studentId: string) {
  try {
    if (!studentId) return { success: false, message: "Please provide all required fields" };

    const [student, siteName] = await Promise.all([
      prisma.studentProfile.findUnique({
        where: { id: studentId },
        select: {
          userId: true,
          user: {
            select: {
              email: true,
              firstName: true,
            }
          }
        }
      }),
      prisma.cMSConfig.findFirst({
        select: { siteName: true },
      })
    ])

    if (!student) {
      return { success: false, error: "Student not found" };
    }

    await prisma.studentProfile.delete({
      where: { id: studentId },
    });

    rejectedMail({
      email: student.user.email,
      firstName: student.user.firstName,
      siteName: siteName?.siteName || 'Complexus Pathways',
    })

    revalidatePath('/dashboard/students')

    return { success: true, message: 'Student approval rejected' };
  } catch (error) {
    console.error("Error rejecting student:", error);
    return { success: false, error: "Failed to reject student" };
  }
};

export async function updateStudent (studentId: string, enrollmentId: string, unsafeData: Partial<TUpdateStudentForm>) {
  try {
    if (!unsafeData || !enrollmentId || !studentId) return { success: false, message: "Please provide all required fields" };

    const {success, data, error } = studentSchema.safeParse(unsafeData);
    if (!success) {
      const errorMessage = error.errors.map((error) => error.message).join(",")
      console.log(errorMessage)
      return { success: false, message: errorMessage };
    }

    await prisma.studentProfile.update({
      where: { id: studentId },
      data: {
        user: {
          update: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            phone: data.phone,
          },
        },
        school: { connect: { id: data.school } },
        program: { connect: { id: data.program } },
      },
    });

    if (data.course) {
      await prisma.enrollment.update({
        where: { id: enrollmentId },
        data: {
          course: { connect: { id: data.course } },
        },
      });
    }

    revalidatePath('/dashboard/students')

    return { success: true, message: 'Student updated' };
  } catch (error) {
    console.error("Error updating student:", error);
    return { success: false, error: "Failed to update student" };
  }
}

export async function deleteStudent (studentId: string) {
  try {
    if (!studentId) return { success: false, message: "Please provide all required fields" };

    const student = await prisma.studentProfile.delete({
      where: { id: studentId },
    });
    await prisma.user.delete({
      where: { id: student.userId },
    });

    revalidatePath('/dashboard/students')

    return { success: true };
  } catch (error) {
    console.error("Error deleting student:", error);
    return { success: false, error: "Failed to delete student" };
  }
}

