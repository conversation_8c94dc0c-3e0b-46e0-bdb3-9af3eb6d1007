import { cn } from "@/lib/utils";
import { BreadcrumbResponsive } from "../breadcrumb";

type Items = {
  href?: string;
  label: string;
}[];

type Props = {
  pgTitle: string;
  pgDescription?: string;
  pgHeading?: string;
  breadcrumbItems: Items;
  headerButton?: React.ReactNode;
  headerSearch?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
};

const PageWrapper = ({
  pgTitle,
  pgDescription,
  pgHeading,
  breadcrumbItems,
  headerButton,
  headerSearch,
  children,
  className,
}: Props) => {
  return (
    <div>
      <div className="flex justify-between items-start gap-4">
        <div className=" space-y-1">
          <span className="text-xl font-semibold tracking-tight">
            {pgTitle}
          </span>
          <BreadcrumbResponsive items={breadcrumbItems} />
        </div>
        <>{headerButton}</>
      </div>
      <div
        className={cn(
          "bg-background text-foreground rounded-xl py-6",
          className
        )}
      >
        <div className="flex flex-col md:flex-row md:justify-between items-start gap-4">
          <div>
            {pgHeading ? (
              <p className="text-xl font-semibold">{pgHeading}</p>
            ) : null}
            {pgDescription ? (
              <p className="text-xs font-medium text-muted-foreground">
                {pgDescription}
              </p>
            ) : null}
          </div>
          {headerSearch}
        </div>
        {children}
      </div>
    </div>
  );
};

export default PageWrapper;
