"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Loader } from "lucide-react";
import { FormInputField } from "@/components/form-element/input-field";
import { FormTextareaField } from "@/components/form-element/text-area";
import {
  moduleSchema,
  TModuleForm,
} from "@/lib/server/action/courses/modules/modules.schema";
import {
  createModule,
  updateModule,
} from "@/lib/server/action/courses/modules";

export default function ModuleForm({
  moduleData,
  courseId,
  moduleId,
}: {
  moduleData?: TModuleForm;
  courseId?: string;
  moduleId?: string;
}) {
  const form = useForm<TModuleForm>({
    resolver: zodResolver(moduleSchema),
    defaultValues: moduleData ?? {
      title: "",
      description: "",
    },
    mode: "onChange",
  });

  const onSubmit = async (values: TModuleForm) => {
    const res = moduleData
      ? await updateModule(moduleId as string, values)
      : await createModule(courseId as string, values);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
        <div className="flex flex-col gap-y-5 mt-5">
          <FormInputField
            control={form.control}
            name="title"
            label="Title"
            placeholder="Enter module title"
          />
          <FormTextareaField
            control={form.control}
            name="description"
            label="Description"
            placeholder="Enter module description"
          />
        </div>
        <div className="flex justify-end gap-2 pt-4">
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader /> {moduleData ? "Updating Module" : "Creating Module"}
              </>
            ) : (
              <>{moduleData ? "Update Module" : "Create Module"}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
