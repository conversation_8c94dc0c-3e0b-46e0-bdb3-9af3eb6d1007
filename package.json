{"name": "mapsa-eapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:reset": "prisma migrate reset --force", "db:push": "prisma db push", "db:migrate-dev": "prisma migrate dev", "db:migrate-deploy": "prisma migrate deploy", "db:studio": "prisma studio"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.514.0", "next": "15.3.3", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.4.6", "next-video": "^2.2.0", "nodemailer": "^6.10.1", "pdf-lib": "^1.17.1", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-player": "^2.16.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "xlsx-js-style": "^1.2.0", "zod": "^3.25.63"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "prisma": "^6.10.1", "tailwindcss": "^4", "tsx": "^4.20.1", "tw-animate-css": "^1.3.4", "typescript": "^5"}}