import CustomDialog from "@/components/shared/CustomDialog";
import Logo from "@/components/shared/logo";
import { Button } from "@/components/ui/button";
import { User } from "next-auth";
import Image from "next/image";
import Link from "next/link";

const navItems = [
  { label: "Home", href: "/" },
  { label: "About", href: "/about" },
];

export default function Header({
  user,
  checkSessionUser,
}: {
  user?: User;
  checkSessionUser: boolean;
}) {
  return (
    <header className="w-full border-b bg-white">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Logo />

          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                {item.label}
              </Link>
            ))}
          </nav>

          <div className="flex items-center gap-3">
            {user && checkSessionUser ? (
              <Button asChild>
                <Link
                  href={`${
                    user.role === "ADMIN" ? "/dashboard" : "/students/dashboard"
                  }`}
                >
                  Dashboard
                </Link>
              </Button>
            ) : (
              <>
                <Button asChild variant="outline">
                  <Link href="/sign-in?type=lc">Login</Link>
                </Button>

                <CustomDialog
                  title="Sign Up"
                  trigger={<Button>Sign Up</Button>}
                >
                  <div className="flex gap-4">
                    <Link
                      href="/onboarding?type=teacher"
                      className="p-2 flex flex-col justify-center items-center rounded-lg overflow-hidden hover:shadow border"
                    >
                      <Image
                        src="/images/teacher.png"
                        alt="Sign Up"
                        width={200}
                        height={200}
                        className="rounded-lg object-contain"
                      />
                      <p className="text-sm mt-1">Teacher</p>
                    </Link>
                    <Link
                      href="/onboarding?type=student"
                      className="p-2 flex flex-col justify-center items-center rounded-lg overflow-hidden hover:shadow border"
                    >
                      <Image
                        src="/images/student.png"
                        alt="Sign Up"
                        width={200}
                        height={200}
                        className="rounded-lg object-contain"
                      />
                      <p className="text-sm mt-1">Student</p>
                    </Link>
                  </div>
                </CustomDialog>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
