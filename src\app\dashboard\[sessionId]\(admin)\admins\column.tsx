"use client";

import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import CustomDialog from "@/components/shared/CustomDialog";
import { CustomDropdown } from "@/components/shared/Dropdown";
import { Button } from "@/components/ui/button";
import {
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { AdminWithUser, deleteAdmin } from "@/lib/server/action/admins";
import { ColumnDef } from "@tanstack/react-table";
import { EllipsisVertical } from "lucide-react";
import { toast } from "sonner";
import ViewAdmin from "../../_components/view/ViewAdmin";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import AdminForm from "@/components/form/AdminForm";
import { formatTimeAgo } from "@/lib/formatDate";

export const columns: ColumnDef<AdminWithUser>[] = [
  {
    accessorKey: "name",
    header: () => <div className="ml-2">Name</div>,
    cell: ({ row }) => {
      const image = row.original.user.fileUrl as string;
      return (
        <div className="ml-2 flex gap-3 items-center">
          <Avatar>
            {image && <AvatarImage src={image} alt="user photo" />}
            <AvatarFallback>{`${row.original.user.firstName[0].toLocaleUpperCase()}${row.original.user.lastName[0].toLocaleUpperCase()}`}</AvatarFallback>
          </Avatar>
          <div className="font-medium truncate">{`${row.original.user.firstName} ${row.original.user.lastName}`}</div>
        </div>
      );
    },
  },
  {
    accessorKey: "user.email",
    header: "Email",
    cell: ({ row }) => row.original.user.email,
  },
  {
    accessorKey: "user.phone",
    header: "Phone",
    cell: ({ row }) => row.original.user.phone,
  },
  {
    accessorKey: "user.createdAt",
    header: "Created",
    cell: ({ row }) => <div>{formatTimeAgo(row.original.user.createdAt)} </div>,
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const admin = row.original;

      return (
        <CustomDropdown
          trigger={
            <Button size="icon" variant="ghost">
              <EllipsisVertical />
            </Button>
          }
        >
          <DropdownMenuItem asChild>
            <CustomDialog asChild={false} trigger="View" title="User Details">
              <ViewAdmin
                firstName={admin.user.firstName}
                lastName={admin.user.lastName}
                email={admin.user.email}
                phone={admin.user.phone || ""}
                adminPhotoUrl={admin.user.fileUrl}
              />
            </CustomDialog>
          </DropdownMenuItem>
          {!admin.isFirst ? (
            <>
              <DropdownMenuItem asChild>
                <CustomDialog
                  title="Update Admin"
                  asChild={false}
                  trigger="Edit"
                >
                  <AdminForm
                    adminData={{
                      firstName: admin.user.firstName,
                      lastName: admin.user.lastName,
                      email: admin.user.email,
                      phone: admin.user.phone || "",
                    }}
                    adminId={admin.id}
                  />
                </CustomDialog>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <CustomAlertDialog
                  asChild={false}
                  trigger="Delete"
                  onConfirm={async () => {
                    const res = await deleteAdmin(admin.id);
                    if (res.success) {
                      toast.success("Admin deleted");
                    } else {
                      toast.error("Failed to delete admin");
                    }
                  }}
                />
              </DropdownMenuItem>
            </>
          ) : null}
        </CustomDropdown>
      );
    },
  },
];
