/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { appConfig } from "@/config/app";
import {
  deleteFromBunnyCDN,
  uploadToBunnyCDN,
} from "@/lib/server/action/bunny/bunny.action";
import { ImageIcon, Loader2, Upload } from "lucide-react";
import Image from "next/image";
import { useRef, useState } from "react";
import { toast } from "sonner";

export default function ImageUploadSection({
  imageType,
  currentImageUrl,
  title,
  description,
  width = 120,
  height = 120,
  updateNestedData,
}: {
  imageType: "hero" | "siteLogo" | "footerLogo" | "brandLogo";
  currentImageUrl: string;
  title: string;
  description: string;
  width?: number;
  height?: number;
  updateNestedData: (path: string[], value: any) => void;
}) {
  const [uploadingStates, setUploadingStates] = useState<{
    [key: string]: boolean;
  }>({});

  // File input refs
  const heroImageInputRef = useRef<HTMLInputElement | null>(null);
  const siteLogoInputRef = useRef<HTMLInputElement | null>(null);
  const footerLogoInputRef = useRef<HTMLInputElement | null>(null);
  const brandLogoInputRef = useRef<HTMLInputElement | null>(null);

  const handleImageUpload = async (
    file: File,
    imageType: "hero" | "siteLogo" | "footerLogo" | "brandLogo"
  ) => {
    setUploadingStates((prev) => ({ ...prev, [imageType]: true }));

    try {
      const formData = new FormData();
      formData.append("file", file);

      const result = await uploadToBunnyCDN(formData, "cms/images");

      if (result.success && result.url) {
        // Update the appropriate image field
        switch (imageType) {
          case "hero":
            updateNestedData(["hero", "image"], result.url);
            break;
          case "siteLogo":
            updateNestedData(["site", "logo"], result.url);
            break;
          case "footerLogo":
            updateNestedData(["site", "footerLogo"], result.url);
            break;
          case "brandLogo":
            updateNestedData(["about", "brandIdentity", "logo"], result.url);
            break;
        }

        toast.success("Image uploaded successfully!");
      } else {
        toast.error(result.error || "Failed to upload image");
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("An error occurred while uploading the image");
    } finally {
      setUploadingStates((prev) => ({ ...prev, [imageType]: false }));
    }
  };

  const triggerFileInput = (
    imageType: "hero" | "siteLogo" | "footerLogo" | "brandLogo"
  ) => {
    switch (imageType) {
      case "hero":
        heroImageInputRef.current?.click();
        break;
      case "siteLogo":
        siteLogoInputRef.current?.click();
        break;
      case "footerLogo":
        footerLogoInputRef.current?.click();
        break;
      case "brandLogo":
        brandLogoInputRef.current?.click();
        break;
    }
  };

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    imageType: "hero" | "siteLogo" | "footerLogo" | "brandLogo"
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      handleImageUpload(file, imageType);
    }
    // Reset the input value so the same file can be selected again
    event.target.value = "";
  };

  const replaceImage = async (
    imageType: "hero" | "siteLogo" | "footerLogo" | "brandLogo",
    oldImageUrl: string
  ) => {
    // Delete old image if it's from Bunny CDN
    if (oldImageUrl && oldImageUrl.includes(appConfig.bunny.CDN_URL)) {
      try {
        await deleteFromBunnyCDN(oldImageUrl);
      } catch (error) {
        console.error("Failed to delete old image:", error);
      }
    }

    // Trigger file input for new image
    triggerFileInput(imageType);
  };

  return (
    <section className="space-y-2">
      <Label>{title}</Label>
      <p className="text-sm text-gray-600">{description}</p>
      <div className="flex items-center space-x-4">
        <div className="relative">
          <Image
            src={currentImageUrl || "/images/placeholder.svg"}
            alt={title}
            width={width}
            height={height}
            className="rounded-lg border object-cover"
            unoptimized={currentImageUrl?.includes("placeholder")}
          />
          {uploadingStates[imageType] && (
            <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
              <Loader2 className="w-6 h-6 text-white animate-spin" />
            </div>
          )}
        </div>

        <div className="space-y-2 flex-1">
          <input
            type="file"
            ref={
              imageType === "hero"
                ? heroImageInputRef
                : imageType === "siteLogo"
                ? siteLogoInputRef
                : imageType === "footerLogo"
                ? footerLogoInputRef
                : brandLogoInputRef
            }
            onChange={(e) => handleFileChange(e, imageType)}
            accept="image/*"
            className="hidden"
          />

          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => triggerFileInput(imageType)}
              disabled={uploadingStates[imageType]}
              className="flex items-center space-x-2"
            >
              {uploadingStates[imageType] ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Upload className="w-4 h-4" />
              )}
              <span>
                {uploadingStates[imageType] ? "Uploading..." : "Upload"}
              </span>
            </Button>

            {currentImageUrl && !currentImageUrl.includes("placeholder") && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => replaceImage(imageType, currentImageUrl)}
                disabled={uploadingStates[imageType]}
                className="flex items-center space-x-2"
              >
                <ImageIcon className="w-4 h-4" />
                <span>Replace</span>
              </Button>
            )}
          </div>

          <Input
            placeholder="Or enter image URL"
            value={currentImageUrl}
            onChange={(e) => {
              switch (imageType) {
                case "hero":
                  updateNestedData(["hero", "image"], e.target.value);
                  break;
                case "siteLogo":
                  updateNestedData(["site", "logo"], e.target.value);
                  break;
                case "footerLogo":
                  updateNestedData(["site", "footerLogo"], e.target.value);
                  break;
                case "brandLogo":
                  updateNestedData(
                    ["about", "brandIdentity", "logo"],
                    e.target.value
                  );
                  break;
              }
            }}
            className="text-xs"
          />
        </div>
      </div>
    </section>
  );
}
