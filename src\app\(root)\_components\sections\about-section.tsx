import Image from "next/image";

type AboutSectionProps = {
  title: string;
  description: string;
  brandIdentity: {
    title: string;
    logo: string;
    description: string;
  };
  designElements: {
    title: string;
    details: string[];
    image: string;
  }[];
};

export function AboutSection({
  title,
  description,
  brandIdentity,
  designElements,
}: AboutSectionProps) {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">{title}</h2>

        <div className="prose max-w-4xl mb-16">
          {description.split("\n\n").map((paragraph, index) => (
            <p key={index} className="text-gray-600 mb-4 leading-relaxed">
              {paragraph}
            </p>
          ))}
        </div>

        {/* Brand Identity */}
        <div className="text-center mb-16">
          <h3 className="text-xl font-semibold mb-4">{brandIdentity.title}</h3>
          <div className="flex justify-center mb-4">
            <div className="w-32 h-32 relative">
              <Image
                src={brandIdentity.logo || "/images/placeholder.svg"}
                alt="Brand Logo"
                width={128}
                height={128}
                className="object-contain rounded-lg"
              />
            </div>
          </div>
          <p className="text-gray-600">{brandIdentity.description}</p>
        </div>

        {/* Design Elements */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {designElements.map((element, index) => (
            <div key={index} className="text-center flex flex-col items-center">
              <div className="flex justify-center w-20 h-20 mb-4">
                <Image
                  src={element.image || "/placeholder.svg?height=80&width=80"}
                  alt={element.title}
                  width={80}
                  height={80}
                  className="object-contain rounded-lg"
                />
              </div>
              <h4 className="font-semibold mb-4">{element.title}</h4>
              <ul className="space-y-2">
                {element.details.map((detail, detailIndex) => (
                  <li
                    key={detailIndex}
                    className="flex items-start justify-start space-x-2 text-sm text-gray-600"
                  >
                    <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                    <span>{detail}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
