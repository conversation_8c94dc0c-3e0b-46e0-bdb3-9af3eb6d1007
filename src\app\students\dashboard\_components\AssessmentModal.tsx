"use client";

import { useEffect, useState, useCallback, useMemo } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { XCircle, Award, Loader2, X } from "lucide-react";
import Link from "next/link";
import {
  completeAssessment,
  getAssessmentForAttempt,
  submitAnswer,
} from "@/lib/server/action/students/modules/assessments";

type QuizQuestion = {
  id: string;
  question: string;
  options: string[];
  correctAnswer: string;
  questionType: "multiple-choice" | "true-false";
  order: number;
  points?: number;
};

interface QuizModalProps {
  attemptId: string;
  assessmentId: string;
  title: string;
  isOpen: boolean;
  onClose: () => void;
  moduleId: string;
  passingScore: number;
  maxStudentQuestions: number;
  instructions: string | null;
  questionOrder: number[];
}

export function QuizModal({
  assessmentId,
  attemptId,
  title,
  isOpen,
  onClose,
  moduleId,
  passingScore,
  maxStudentQuestions,
  instructions,
  questionOrder,
}: QuizModalProps) {
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<(string | null)[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Memoized calculations
  const totalQuestions = questions.length;
  const progress = useMemo(
    () =>
      totalQuestions > 0 ? ((currentQuestion + 1) / totalQuestions) * 100 : 0,
    [currentQuestion, totalQuestions]
  );

  const isAnswered =
    selectedAnswers[currentQuestion] !== null &&
    selectedAnswers[currentQuestion] !== undefined;
  const allQuestionsAnswered = selectedAnswers.every(
    (answer) => answer !== null && answer !== undefined
  );

  console.log(selectedAnswers);

  // Fetch questions effect
  useEffect(() => {
    if (!isOpen || !assessmentId || !attemptId) return;

    async function fetchQuestions() {
      try {
        setIsLoading(true);
        setError(null);

        const attemptData = await getAssessmentForAttempt(
          attemptId,
          maxStudentQuestions,
          questionOrder
        );
        if (!attemptData) {
          throw new Error("Assessment not found");
        }

        const { assessment, responses } = attemptData;

        console.log(assessment.questions, responses);

        setQuestions(assessment.questions);
        setSelectedAnswers(
          responses.map((r) => {
            const answer = assessment.questions.find(
              (q) => q.id === r.questionId
            )?.correctAnswer;
            return typeof answer === "string" ? answer : null;
          })
        );
      } catch (err) {
        console.error("Failed to fetch questions:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load questions"
        );
      } finally {
        setIsLoading(false);
      }
    }

    fetchQuestions();
  }, [assessmentId, isOpen, attemptId, maxStudentQuestions, questionOrder]);

  // Reset quiz when closed/opened
  useEffect(() => {
    if (!isOpen) {
      setCurrentQuestion(0);
      setSelectedAnswers([]);
      setShowResults(false);
      setError(null);
    }
  }, [isOpen]);

  const handleAnswerSelect = useCallback(
    async (answer: string) => {
      setSelectedAnswers((prev) => {
        const newAnswers = [...prev];
        newAnswers[currentQuestion] = answer;
        return newAnswers;
      });

      await submitAnswer({
        attemptId: attemptId,
        questionId: questions[currentQuestion].id,
        response: answer,
      });
    },
    [currentQuestion, questions, attemptId]
  );

  const handleNext = useCallback(async () => {
    if (currentQuestion < totalQuestions - 1) {
      setCurrentQuestion((prev) => prev + 1);
    } else {
      setIsSubmitting(true);
      await completeAssessment({ attemptId });
      setIsSubmitting(false);
      setShowResults(true);
    }
  }, [currentQuestion, totalQuestions, attemptId]);

  const handlePrevious = useCallback(() => {
    if (currentQuestion > 0) {
      setCurrentQuestion((prev) => prev - 1);
    }
  }, [currentQuestion]);

  const calculateScore = useCallback(() => {
    if (!questions.length || !selectedAnswers.length) {
      return {
        percentage: 0,
        correct: 0,
        total: questions.length,
        points: 0,
        totalPoints: 0,
      };
    }

    let correct = 0;
    let totalPoints = 0;
    let earnedPoints = 0;

    selectedAnswers.forEach((answer, index) => {
      const question = questions[index];
      if (!question) return;

      const questionPoints = question.points || 1;
      totalPoints += questionPoints;

      if (answer !== null && answer !== undefined) {
        const selectedOption = answer;
        if (selectedOption === question.correctAnswer) {
          correct++;
          earnedPoints += questionPoints;
        }
      }
    });

    return {
      percentage: Math.round((correct / totalQuestions) * 100),
      correct,
      total: totalQuestions,
      points: earnedPoints,
      totalPoints,
    };
  }, [questions, selectedAnswers, totalQuestions]);

  const resetQuiz = useCallback(() => {
    setCurrentQuestion(0);
    setSelectedAnswers(new Array(questions.length).fill(null));
    setShowResults(false);
  }, [questions.length]);

  // Loading state
  if (isLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              Loading Quiz...
            </DialogTitle>
          </DialogHeader>
          <div className="py-8 text-center text-gray-600">
            Please wait while we load your quiz questions.
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Error state
  if (error) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <XCircle className="h-5 w-5" />
              Error Loading Quiz
            </DialogTitle>
          </DialogHeader>
          <div className="py-8 text-center">
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Results state
  if (showResults) {
    const score = calculateScore();
    const passed = score.percentage >= passingScore;

    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Quiz Results - {title}</DialogTitle>
          </DialogHeader>

          <div className="text-center space-y-6">
            <div
              className={`mx-auto w-20 h-20 rounded-full flex items-center justify-center ${
                passed ? "bg-green-100" : "bg-red-100"
              }`}
            >
              {passed ? (
                <Award className="h-10 w-10 text-green-600" />
              ) : (
                <XCircle className="h-10 w-10 text-red-600" />
              )}
            </div>

            <div>
              <h3 className="text-2xl font-bold mb-2">
                {passed ? "Congratulations!" : "Keep Learning!"}
              </h3>
              <p className="text-gray-600">
                {passed
                  ? "You have successfully completed the quiz."
                  : `You need ${passingScore}% to pass. Review the material and try again.`}
              </p>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg space-y-2">
              <div className="text-4xl font-bold mb-2">{score.percentage}%</div>
              <div className="text-gray-600">
                {score.correct} out of {score.total} correct
              </div>
              {score.totalPoints > score.total && (
                <div className="text-sm text-gray-500">
                  {score.points} out of {score.totalPoints} points
                </div>
              )}
            </div>

            <div className="space-y-3">
              <div className="flex gap-3 justify-center">
                <Button onClick={resetQuiz} variant="outline">
                  Retake Quiz
                </Button>
                <Button variant="outline" asChild>
                  <Link
                    href={`/students/dashboard/modules/${moduleId}/review?attemptId=${attemptId}`}
                  >
                    View Detailed Results
                  </Link>
                </Button>
              </div>
              <Button onClick={onClose}>
                {passed ? "Continue Learning" : "Review Module"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Quiz question state
  const currentQ = questions[currentQuestion];
  if (!currentQ) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center transition fade-in-0 fade-out-0 bg-black/50">
      <div className="w-full max-w-4xl bg-white p-6 rounded-lg overflow-hidden relative">
        <div className="flex flex-col gap-2 text-center sm:text-left">
          <h2 className="text-lg leading-none font-semibold">Quiz: {title}</h2>
          {instructions && (
            <p className="text-sm text-muted-foreground">{instructions}</p>
          )}
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>
              Question {currentQuestion + 1} of {totalQuestions}
            </span>
            <span>
              {
                selectedAnswers.filter((a) => a !== null && a !== undefined)
                  .length
              }{" "}
              of {totalQuestions} answered
            </span>
          </div>
        </div>

        <div className="absolute top-2 right-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="cursor-pointer"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-6">
          <Progress value={progress} className="h-2" />

          <Card className="py-0">
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">{currentQ.question}</h3>

              {currentQ.questionType === "multiple-choice" && (
                <div className="space-y-3">
                  {currentQ.options.map((option, index) => {
                    const optionLetter = ["A", "B", "C", "D"][index];

                    return (
                      <button
                        key={index}
                        onClick={() => handleAnswerSelect(optionLetter)}
                        className={`w-full p-4 text-left rounded-lg border-2 transition-all duration-200 ${
                          selectedAnswers[currentQuestion] === optionLetter
                            ? "border-blue-500 bg-blue-50 shadow-sm"
                            : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <div
                            className={`w-4 h-4 rounded-full border-2 transition-colors ${
                              selectedAnswers[currentQuestion] === optionLetter
                                ? "border-blue-500 bg-blue-500"
                                : "border-gray-300"
                            }`}
                          >
                            {selectedAnswers[currentQuestion] ===
                              optionLetter && (
                              <div className="w-full h-full rounded-full bg-white scale-50"></div>
                            )}
                          </div>
                          <span className="flex-1">{option}</span>
                        </div>
                      </button>
                    );
                  })}
                </div>
              )}
              {currentQ.questionType === "true-false" && (
                <div className="space-y-3">
                  {["True", "False"].map((option) => (
                    <button
                      key={option}
                      onClick={() =>
                        handleAnswerSelect(option.toLocaleLowerCase())
                      }
                      className={`w-full p-4 text-left rounded-lg border-2 transition-all duration-200 ${
                        selectedAnswers[currentQuestion] ===
                        option.toLocaleLowerCase()
                          ? "border-blue-500 bg-blue-50 shadow-sm"
                          : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className={`w-4 h-4 rounded-full border-2 transition-colors ${
                            selectedAnswers[currentQuestion] ===
                            option.toLocaleLowerCase()
                              ? "border-blue-500 bg-blue-500"
                              : "border-gray-300"
                          }`}
                        >
                          {selectedAnswers[currentQuestion] ===
                            option.toLocaleLowerCase() && (
                            <div className="w-full h-full rounded-full bg-white scale-50"></div>
                          )}
                        </div>
                        <span className="flex-1">{option}</span>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          <div className="flex justify-between items-center">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentQuestion === 0}
            >
              Previous
            </Button>

            <div className="flex gap-2">
              {/* Question navigation dots */}
              <div className="flex gap-1">
                {questions
                  .slice(0, Math.min(10, totalQuestions))
                  .map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentQuestion(index)}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentQuestion
                          ? "bg-blue-500"
                          : selectedAnswers[index] !== null &&
                            selectedAnswers[index] !== undefined
                          ? "bg-green-500"
                          : "bg-gray-300"
                      }`}
                    />
                  ))}
                {totalQuestions > 10 && (
                  <span className="text-gray-400">...</span>
                )}
              </div>
            </div>

            <Button
              onClick={handleNext}
              disabled={!isAnswered}
              className={
                currentQuestion === totalQuestions - 1 && allQuestionsAnswered
                  ? "bg-green-600 hover:bg-green-700"
                  : ""
              }
            >
              {currentQuestion === totalQuestions - 1 ? (
                isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Submitting..
                  </>
                ) : (
                  "Finish Quiz"
                )
              ) : (
                "Next"
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
