import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

type CourseProgressReportProps = {
  courseProgress?: {
    id: string;
    courseName: string;
    students: number;
    completed: number;
  }[];
};

export default function CourseProgressReport({
  courseProgress,
}: CourseProgressReportProps) {
  return (
    <section className="mb-8">
      <Card className="py-0">
        <CardHeader className="bg-gray-100 py-2">
          <CardTitle className="text-center font-semibold">
            COURSE PROGRESS REPORT
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold">Course name</TableHead>
                <TableHead className="font-semibold text-center">
                  Student/s
                </TableHead>
                <TableHead className="font-semibold text-center">
                  Completed
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {!courseProgress ? (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : courseProgress.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    No upcoming schedules found.
                  </TableCell>
                </TableRow>
              ) : (
                <>
                  {courseProgress.map((course) => (
                    <TableRow key={course.id}>
                      <TableCell>{course.courseName}</TableCell>
                      <TableCell className="text-center">
                        {course.students}
                      </TableCell>
                      <TableCell className="text-center">
                        {course.completed}
                      </TableCell>
                    </TableRow>
                  ))}
                </>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </section>
  );
}
