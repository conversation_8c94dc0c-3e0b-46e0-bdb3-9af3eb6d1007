"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { Button } from "../ui/button";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import { FormInputField } from "../form-element/input-field";
import {
  schoolSchema,
  TSchool,
} from "@/lib/server/action/schools/schools.schema";
import { FormTextareaField } from "../form-element/text-area";
import { createSchool, updateSchool } from "@/lib/server/action/schools";
import { useParams } from "next/navigation";

export default function SchoolForm({
  schoolId,
  schoolData,
}: {
  schoolId?: string;
  schoolData?: TSchool;
}) {
  // get session id from url
  const params = useParams<{ sessionId: string }>();
  if (!params.sessionId) throw new Error("Session ID is required");

  const form = useForm<TSchool>({
    resolver: zodResolver(schoolSchema),
    defaultValues: schoolData ?? {
      name: "",
      description: "",
    },
    mode: "onChange",
  });

  const onSubmit = async (values: TSchool) => {
    const res = schoolData
      ? await updateSchool(schoolId as string, values)
      : await createSchool(params.sessionId, values);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormInputField
          control={form.control}
          name="name"
          label="Name"
          placeholder="Enter school name"
        />
        <FormTextareaField
          control={form.control}
          name="description"
          label="Description"
          placeholder="Enter school description"
        />
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader /> {schoolData ? "Updating School" : "Update School"}
              </>
            ) : (
              <>{schoolData ? "Update School" : "Create School"}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
