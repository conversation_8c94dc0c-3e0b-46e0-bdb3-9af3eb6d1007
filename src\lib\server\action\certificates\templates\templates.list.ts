'use server'

import prisma from "@/lib/prisma";

export async function getTemplates( sessionId: string ) {
  try {
    const templates = await prisma.certificateTemplate.findMany({
      where: { sessionId },
      orderBy: { name: 'asc' },
      include: {
        courses: true
      }
    });

    const total = await prisma.certificateTemplate.count();

    return { templates, total };
  } catch (error) {
    console.error("Error fetching templates:", error);
    throw new Error("Failed to fetch templates.");
  }
}

export async function getTemplate(templateId: string) {
  try {
    const template = await prisma.certificateTemplate.findUnique({
      where: { id: templateId },
    });

    return template;
  } catch (error) {
    console.error("Error fetching template:", error);
    throw new Error("Failed to fetch template.");
  }
}