"use server"

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"
import { TActivityForm } from "./activities.schema"

export async function createActivity(courseId : string, data: TActivityForm) {
  try {
    await prisma.activity.create({
      data: {
        ...data,
        course: { connect: { id: courseId } }
      },
    })

    revalidatePath(`/admin/courses/${courseId}/activity`)

    return { success: true, message: 'Activity created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create activity' }
  }
}

export async function updateActivity(activityId: string, data: Partial<TActivityForm>) {
  try {
    const activity = await prisma.activity.update({
      where: { id: activityId },
      data,
      select: { courseId: true }
    })

    revalidatePath(`/admin/courses/${activity.courseId}/activity`)

    return { success: true, message: 'Activity updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update activity' }
  }
}

export async function deleteActivity(activityId: string) {
  try {
    const activity = await prisma.activity.delete({
      where: { id: activityId },
      select: { courseId: true }
    })

    revalidatePath(`/admin/courses/${activity.courseId}/activity`)

    return { success: true, message: 'Activity deleted' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete activity' }
  }
}
