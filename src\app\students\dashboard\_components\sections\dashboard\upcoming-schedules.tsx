"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { userTimezone } from "@/lib/formatDate";

type UpcomingSchedulesProps = {
  upcomingSchedules?: {
    id: string;
    title: string;
    description: string;
    startTime: Date;
  }[];
};

export default function UpcomingSchedules({
  upcomingSchedules,
}: UpcomingSchedulesProps) {
  return (
    <Card className="py-0 col-span-2">
      <CardHeader className="bg-gray-100 py-2">
        <CardTitle className="text-center font-semibold">
          UPCOMING SCHEDULES
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50">
              <TableHead className="font-semibold">Name</TableHead>
              <TableHead className="font-semibold">Description</TableHead>
              <TableHead className="font-semibold">Date and time</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!upcomingSchedules ? (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  Loading...
                </TableCell>
              </TableRow>
            ) : upcomingSchedules.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  No upcoming schedules found.
                </TableCell>
              </TableRow>
            ) : (
              upcomingSchedules.map((schedule) => (
                <TableRow key={schedule.id}>
                  <TableCell>{schedule.title}</TableCell>
                  <TableCell>{schedule.description}</TableCell>
                  <TableCell>
                    {userTimezone(schedule.startTime, "PPP p")}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
