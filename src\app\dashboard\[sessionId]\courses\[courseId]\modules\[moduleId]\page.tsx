import Loading from "@/app/dashboard/[sessionId]/_components/Loading";
import { ModuleHeader } from "../../../_components/sections/module-header-section";
import { Suspense } from "react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import LessonTab from "../../../_components/tabs/modules/lesson-tab";
import AssessmentTab from "../../../_components/tabs/modules/assessment-tab";
import { getModule } from "@/lib/server/action/courses/modules";
import { notFound } from "next/navigation";

async function SuspendedComponent({
  params,
}: {
  params: Promise<{ sessionId: string; courseId: string; moduleId: string }>;
}) {
  const { sessionId, courseId, moduleId } = await params;
  const moduleData = await getModule(moduleId);
  if (!moduleData) {
    notFound();
  }

  return (
    <div className="space-y-4">
      <ModuleHeader
        moduleData={moduleData}
        courseId={courseId}
        sessionId={sessionId}
      />

      <Tabs defaultValue="lesson" className="w-full">
        <TabsList className="grid w-full grid-cols-2 max-w-md">
          <TabsTrigger value="lesson">Lesson</TabsTrigger>
          <TabsTrigger value="assessment">Assessment</TabsTrigger>
        </TabsList>

        <TabsContent value="lesson" className="mt-2">
          <LessonTab moduleId={moduleId} />
        </TabsContent>

        <TabsContent value="assessment" className="mt-2">
          <AssessmentTab moduleId={moduleId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default function ModulePage({
  params,
}: {
  params: Promise<{ sessionId: string; courseId: string; moduleId: string }>;
}) {
  return (
    <>
      <Suspense fallback={<Loading />}>
        {SuspendedComponent({ params })}
      </Suspense>
    </>
  );
}
