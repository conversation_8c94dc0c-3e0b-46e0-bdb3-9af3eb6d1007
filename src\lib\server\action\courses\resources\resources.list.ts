'use server'

import prisma from "@/lib/prisma"
import { Prisma } from "@prisma/client";

export type CourseResourceFolderWithResources = Prisma.CourseResourceFolderGetPayload<{
  include: {
    resources: true
  }
}>;

export async function getCourseResourceFolders(courseId: string) {
  try {
    const folders = await prisma.courseResourceFolder.findMany({
      where: { courseId },
      orderBy: { name: 'asc' },
      include: {
        resources: true
      }
    })
    
    return folders
  } catch (error) {
    console.log(error)
    return []
  }
}

export async function getCourseRecourseFolderOptions (courseId: string) {
  try {
    const folders = await prisma.courseResourceFolder.findMany({
      where: { courseId },
      select: {
        id: true,
        name: true
      },
      orderBy: { name: 'asc' }
    })
    
    return folders.map(folder => ({ label: folder.name, value: folder.id }))
  } catch (error) {
    console.log(error)
    return []
  }
}

export async function getCourseResources(courseId: string) {
  try {
    const resources = await prisma.courseResource.findMany({
      where: { courseId },
      orderBy: { createdAt: 'desc' },
      take: 5
    })
    
    return resources
  } catch (error) {
    console.log(error)
    return []
  }
}

export async function getResource(resourceId: string) {
  try {
    const resource = await prisma.courseResource.findUnique({
      where: { id: resourceId },
      include: {
        course: true,
      }
    })
    
    return resource
  } catch (error) {
    console.log(error)
    return null
  }
}