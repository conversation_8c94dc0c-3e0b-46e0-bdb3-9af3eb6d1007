"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import Logo from "@/components/shared/logo";
import Link from "next/link";

export default function StudentHeader() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between gap-4">
          <Logo />

          {/* Right Side Actions */}
          <div className="items-center gap-4 ml-auto">
            {/* Profile Dropdown */}
            <Button
              variant="ghost"
              className="hidden md:flex relative w-full h-11 px-0 rounded-full"
              asChild
            >
              <Link href="/students/dashboard">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={"/images/avatar.jpg"} alt="user name" />
                  <AvatarFallback className="bg-blue-600 text-white">
                    JD
                  </AvatarFallback>
                </Avatar>
                <div className="hidden md:flex flex-col pr-3 text-start">
                  <p className="text-sm font-medium">{"John Doe"}</p>
                  <p className="text-xs text-gray-500">{"<EMAIL>"}</p>
                </div>
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}
