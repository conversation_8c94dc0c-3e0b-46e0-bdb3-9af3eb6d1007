"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Trash2, Plus } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { appConfig } from "@/config/app";
import { uploadFileToBunny } from "@/lib/bunny";
import {
  FileValidationResult,
  validateImageFile,
  validatePdfFile,
} from "@/lib/file-validators";
import { formatBytes } from "@/lib/utils";
import { Loader } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { createTemplate } from "@/lib/server/action/certificates/templates";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { generateFileUploadUrl } from "@/lib/server/action/bunny/bunny.action";

interface Field {
  name: string;
  x: number;
  y: number;
  position: "left" | "center" | "right";
}

interface FormData {
  name: string;
  fields: Field[];
  pdfId: string;
  imageId: string;
  pdfUrl: string;
  imageUrl: string;
}

export default function UploadCertTemplatePage() {
  const [isUploading, setIsUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: "",
    fields: [{ name: "", x: 0, y: 0, position: "left" }],
    pdfUrl: "",
    pdfId: "",
    imageUrl: "",
    imageId: "",
  });
  const [uploadState, setUploadState] = useState({
    progress: 0,
    isUploading: false,
    uploadedBytes: 0,
    totalSize: 0,
  });

  const addField = () => {
    setFormData((prev) => ({
      ...prev,
      fields: [...prev.fields, { name: "", x: 0, y: 0, position: "left" }],
    }));
  };

  const removeField = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      fields: prev.fields.filter((_, i) => i !== index),
    }));
  };

  const updateField = (index: number, field: Partial<Field>) => {
    setFormData((prev) => ({
      ...prev,
      fields: prev.fields.map((f, i) => (i === index ? { ...f, ...field } : f)),
    }));
  };

  const updateUploadProgress = (
    percentage: number,
    loaded: number,
    total: number
  ) => {
    setUploadState({
      progress: Math.round(percentage),
      isUploading: true,
      uploadedBytes: loaded,
      totalSize: total,
    });
  };

  const processFile = (
    selectedFile: File,
    type: "pdf" | "image"
  ): FileValidationResult => {
    // Validate file
    const validation =
      type === "pdf"
        ? validatePdfFile(selectedFile, appConfig.MAX_DOCUMENT_SIZE_MB)
        : validateImageFile(selectedFile, appConfig.MAX_IMAGE_SIZE_MB);
    return validation;
  };

  const handleFileUpload = async (file: File, type: "pdf" | "image") => {
    const validation = processFile(file, type);
    if (!validation.valid) {
      toast.error(validation.error || "Invalid file");
      return;
    }

    const formData = new FormData();
    formData.append("file", file);

    if (type === "pdf") {
      try {
        setIsUploading(true);
        const { fileId, uploadUrl, accessKey, cdnUrl } =
          await generateFileUploadUrl(file.name, "certificates");

        await uploadFileToBunny(
          file,
          uploadUrl,
          accessKey,
          updateUploadProgress
        );
        setFormData((prev) => ({
          ...prev,
          pdfId: fileId,
          pdfUrl: cdnUrl,
        }));
      } catch (error) {
        console.error(error);
        toast.error("An error occurred while uploading the file");
      } finally {
        setIsUploading(false);
      }
    } else {
      try {
        setIsUploading(true);
        const { fileId, uploadUrl, accessKey, cdnUrl } =
          await generateFileUploadUrl(file.name, "certificates");

        await uploadFileToBunny(
          file,
          uploadUrl,
          accessKey,
          updateUploadProgress
        );
        setFormData((prev) => ({
          ...prev,
          imageId: fileId,
          imageUrl: cdnUrl,
        }));
      } catch (error) {
        console.error(error);
        toast.error("An error occurred while uploading the file");
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);
      const res = await createTemplate({
        fileId: formData.pdfId,
        fileUrl: formData.pdfUrl,
        previewId: formData.imageId,
        previewUrl: formData.imageUrl,
        name: formData.name,
        fields: formData.fields,
      });

      if (res.success) {
        toast.success("File uploaded successfully");
        setFormData({
          name: "",
          fields: [{ name: "", x: 0, y: 0, position: "left" }],
          pdfUrl: "",
          pdfId: "",
          imageUrl: "",
          imageId: "",
        });
      } else {
        console.error(res.error);
        toast.error(res.error);
      }
    } catch (error) {
      console.error(error);
      toast.error("An error occurred while uploading the file");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Form Builder</CardTitle>
          <CardDescription>
            Create a form with dynamic fields and file uploads
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Name Field */}
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="Enter name"
                required
              />
            </div>

            {/* Dynamic Fields Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-semibold">Fields</Label>
                <Button
                  type="button"
                  onClick={addField}
                  size="sm"
                  variant="outline"
                  className="flex items-center gap-2 bg-transparent"
                >
                  <Plus className="h-4 w-4" />
                  Add Field
                </Button>
              </div>

              <div className="space-y-4">
                {formData.fields.map((field, index) => (
                  <Card key={index} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 justify-center items-start">
                      <div className="space-y-2">
                        <Label htmlFor={`field-name-${index}`}>
                          Field Name
                        </Label>
                        <Input
                          id={`field-name-${index}`}
                          value={field.name}
                          onChange={(e) =>
                            updateField(index, { name: e.target.value })
                          }
                          placeholder="Field name"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`field-x-${index}`}>X Position</Label>
                        <Input
                          id={`field-x-${index}`}
                          type="number"
                          value={field.x}
                          onChange={(e) =>
                            updateField(index, { x: Number(e.target.value) })
                          }
                          placeholder="X coordinate"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`field-y-${index}`}>Y Position</Label>
                        <Input
                          id={`field-y-${index}`}
                          type="number"
                          value={field.y}
                          onChange={(e) =>
                            updateField(index, { y: Number(e.target.value) })
                          }
                          placeholder="Y coordinate"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`field-position-${index}`}>
                          Position
                        </Label>
                        <Select
                          value={field.position}
                          onValueChange={(value) =>
                            updateField(index, {
                              position: value as "left" | "center" | "right",
                            })
                          }
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select position" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="left">Left</SelectItem>
                            <SelectItem value="center">Center</SelectItem>
                            <SelectItem value="right">Right</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <Button
                        type="button"
                        onClick={() => removeField(index)}
                        size="sm"
                        variant="destructive"
                        disabled={formData.fields.length === 1}
                        className="flex items-center gap-2"
                      >
                        <Trash2 className="h-4 w-4" />
                        Delete
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            {/* File Uploads */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* PDF Upload */}
              <div className="space-y-2">
                <Label htmlFor="pdf-upload">PDF Upload</Label>
                <Input
                  id="pdf-upload"
                  type="file"
                  accept="application/pdf"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileUpload(file, "pdf");
                  }}
                  required
                />
                {formData.pdfUrl && (
                  <p className="text-sm text-muted-foreground">
                    PDF uploaded: {formData.pdfId}
                  </p>
                )}
              </div>

              {/* Image Upload */}
              <div className="space-y-2">
                <Label htmlFor="image-upload">Image Upload</Label>
                <Input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileUpload(file, "image");
                  }}
                  required
                />
                {formData.imageUrl && (
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      Image uploaded: {formData.imageId}
                    </p>
                    <Image
                      src={formData.imageUrl || "/images/placeholder.svg"}
                      alt="Uploaded preview"
                      width={128}
                      height={128}
                      className="w-32 h-32 object-cover rounded-md border"
                    />
                  </div>
                )}
              </div>

              {isUploading && (
                <div className="space-y-2 mt-4">
                  <div className="flex justify-between text-sm">
                    <span>Uploading File...</span>
                    <span>{uploadState.progress}%</span>
                  </div>
                  <Progress value={uploadState.progress} className="w-full" />
                  <div className="text-xs text-muted-foreground">
                    {formatBytes(uploadState.uploadedBytes)} /{" "}
                    {formatBytes(uploadState.totalSize)}
                  </div>
                </div>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button type="submit" size="lg" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader /> Submit
                  </>
                ) : (
                  "Submit"
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Payload Preview */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Current Payload Preview</CardTitle>
          <CardDescription>This shows what will be submitted</CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="bg-muted p-4 rounded-md text-sm overflow-auto">
            {JSON.stringify(
              {
                name: formData.name,
                fields: formData.fields,
                pdfId: formData.pdfId,
                pdfUrl: formData.pdfUrl,
                imageId: formData.imageId,
                imageUrl: formData.imageUrl,
              },
              null,
              2
            )}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
}
