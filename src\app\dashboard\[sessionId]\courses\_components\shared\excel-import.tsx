/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import type React from "react";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import { FileSpreadsheet, Upload, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import * as XLSX from "xlsx-js-style";
// import { ImportQuestions } from "@/lib/server/subject/subject.action";
import { toast } from "sonner";
import {
  importQuestions,
  Question,
} from "@/lib/server/action/courses/modules/assessments/questions";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

export function ExcelImportDialog({ assessmentId }: { assessmentId: string }) {
  const [open, setOpen] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [preview, setPreview] = useState<any[] | null>(null);
  const [importProgress, setImportProgress] = useState(0);
  const [importing, setImporting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deletePreviousQuestions, setDeletePreviousQuestions] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (selectedFile: File | null) => {
    setError(null);
    setPreview(null);
    setFile(selectedFile);

    if (selectedFile) {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: "array" });

          // Assuming you want to read the first sheet
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];

          const jsonData: any[] = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
            range: 4,
            defval: "",
          });

          // If you want to use the first row as headers for the table, you can map it like this
          const headerRow = jsonData[0];
          const dataRows = jsonData.slice(1);

          const previewData = dataRows.map((row: any) => {
            const rowData: { [key: string]: string } = {};
            headerRow.forEach((header: string, index: number) => {
              rowData[header] = row[index] || "";
            });
            return rowData;
          });

          // Set the preview data
          setPreview(previewData);
        } catch (err: any) {
          setError(`Error parsing Excel file: ${err.message}`);
          console.error("Error parsing Excel file:", err);
        }
      };

      reader.onerror = (error) => {
        setError("Error reading the file.");
        console.error("Error reading the file:", error);
      };

      reader.readAsArrayBuffer(selectedFile);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];
      if (
        droppedFile.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        droppedFile.type === "application/vnd.ms-excel"
      ) {
        handleFileChange(droppedFile);
      } else {
        setError("Please upload an Excel file (.xlsx or .xls)");
      }
    }
  };

  const handleImport = async () => {
    if (!file) return;

    setImporting(true);
    setImportProgress(0);

    try {
      const questions = preview?.map((q) => ({
        question: q.Question,
        options: [q.A, q.B, q.C, q.D].filter((option) => option.trim()),
        correctAnswer: q.Correct,
        type:
          (!q.A && !q.B && !q.C && !q.D) ||
          q.correctAnswer === "true" ||
          q.correctAnswer === "false"
            ? "true-false"
            : "multiple-choice",
        assessmentId,
      })) as Question[] | undefined;

      // 2.  Simulate import progress
      const interval = setInterval(() => {
        setImportProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval);
            setTimeout(() => {
              setImporting(false);
              setOpen(false);
              // Reset state after closing
              setTimeout(() => {
                setFile(null);
                setPreview(null);
                setImportProgress(0);
              }, 300);
            }, 500);
            return 100;
          }
          return prev + 10;
        });
      }, 200);

      const res = await importQuestions(
        assessmentId,
        questions as Question[],
        deletePreviousQuestions
      );
      console.log(questions);

      if (res.success) {
        toast.success(res.message);
      } else {
        toast.error(res.message || "Failed to import questions");
      }
    } catch (error: any) {
      setError(`Import failed: ${error.message}`);
      console.error("Import failed:", error.message);
    } finally {
      setImporting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="sm">
          <FileSpreadsheet className="mr-1 h-4 w-4" />
          Import
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>Import Excel Document</DialogTitle>
          <DialogDescription>
            Upload an Excel file to import its data into your system.
          </DialogDescription>
        </DialogHeader>

        {!file ? (
          <div
            className={`mt-4 border-2 border-dashed rounded-lg p-10 text-center ${
              isDragging
                ? "border-primary bg-primary/5"
                : "border-muted-foreground/20"
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <FileSpreadsheet className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-semibold">
              Drag & Drop your Excel file here
            </h3>
            <p className="mt-2 text-sm text-muted-foreground">
              or click the button below to browse files
            </p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="mr-2 h-4 w-4" />
              Select File
            </Button>
            <Input
              ref={fileInputRef}
              type="file"
              accept=".xlsx,.xls"
              className="hidden"
              onChange={(e) => {
                if (e.target.files && e.target.files.length > 0) {
                  handleFileChange(e.target.files[0]);
                }
              }}
            />
            {error && (
              <Alert variant="destructive" className="mt-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </div>
        ) : (
          <div className="mt-4 overflow-x-auto">
            <div className="rounded-md border mt-4">
              <div className="max-h-[300px] overflow-auto">
                {preview ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {Object.keys(preview[0]).map((key) => (
                          <TableHead key={key}>{key}</TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {preview.map((row, index) => (
                        <TableRow key={index}>
                          {Object.values(row).map((cell, cellIndex) => (
                            <TableCell key={cellIndex}>
                              {cell as string}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="flex items-center justify-center p-8">
                    <div className="text-center">
                      <FileSpreadsheet className="mx-auto h-8 w-8 text-muted-foreground animate-pulse" />
                      <p className="mt-2 text-sm text-muted-foreground">
                        Loading preview...
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="mt-2 text-sm text-muted-foreground">
              Showing preview of file:{" "}
              <span className="font-medium">{file.name}</span> (
              {(file.size / 1024).toFixed(2)} KB)
            </div>
          </div>
        )}

        {importing && (
          <div className="mt-4 space-y-2">
            <div className="flex justify-between text-sm">
              <span>Importing data...</span>
              <span>{importProgress}%</span>
            </div>
            <Progress value={importProgress} />
          </div>
        )}

        <DialogFooter className="mt-4">
          <div className="flex justify-between items-center space-x-2 w-full">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="deletePreviousQuestions"
                checked={deletePreviousQuestions}
                onCheckedChange={(checked) =>
                  setDeletePreviousQuestions(checked as boolean)
                }
              />
              <Label htmlFor="deletePreviousQuestions">
                Delete previous questions
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setFile(null);
                  setPreview(null);
                }}
                disabled={importing || !file}
              >
                Clear
              </Button>
              <Button
                onClick={handleImport}
                disabled={importing || !file || !preview}
              >
                {importing ? "Importing..." : "Import Data"}
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
