"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Question } from "@/lib/server/action/courses/modules/assessments/questions";
import { Download, Loader } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import * as XLSX from "xlsx-js-style";

export default function ExportQuestions({
  moduleTitle,
  questions,
  siteName,
}: {
  moduleTitle: string;
  questions: Question[];
  siteName: string;
}) {
  const [loading, setLoading] = useState(false);

  const exportToExcel = (questions: Question[]) => {
    setLoading(true);
    try {
      // 1. Define the custom header rows
      const customHeaderRows = [
        [`${siteName} - ${moduleTitle}`],
        [`Generated on: ${new Date().toLocaleString()}`],
        [`Total Questions: ${questions.length}`],
        [], // An empty row for spacing between custom header and data header
      ];

      // 2. Define the data headers for the questions
      const dataHeaders = ["Question", "A", "B", "C", "D", "Correct"];

      // 3. Prepare the data for the questions table
      const questionDataRows = questions.map((question) => [
        question.question,
        question.options ? question.options[0] : "",
        question.options ? question.options[1] : "",
        question.options ? question.options[2] : "",
        question.options ? question.options[3] : "",
        question.correctAnswer,
        // (question.type === "true-false" ? "TF" : "MCQ"), // Uncomment if you want this column
      ]);

      // 4. Combine all parts into a single array of arrays (AoA)
      const fullData = [...customHeaderRows, dataHeaders, ...questionDataRows];

      // 5. Create worksheet from the combined AoA
      const ws = XLSX.utils.aoa_to_sheet(fullData);

      // --- Styling and Merges ---

      // Calculate the number of rows in customHeaderRows
      const numCustomHeaderRows = customHeaderRows.length;
      const numDataHeaderCols = dataHeaders.length; // Number of columns in your data headers (e.g., 6)

      // Define merge cells for the title, date, and stats
      // Adjust `e.c` to match the number of data columns, which is `numDataHeaderCols - 1` (0-indexed)
      const merge = [
        { s: { r: 0, c: 0 }, e: { r: 0, c: numDataHeaderCols - 1 } }, // Merges A1 to last data column for main title
        { s: { r: 1, c: 0 }, e: { r: 1, c: numDataHeaderCols - 1 } }, // Merges A2 to last data column for date
        { s: { r: 2, c: 0 }, e: { r: 2, c: numDataHeaderCols - 1 } }, // Merges A3 to last data column for total questions
      ];
      ws["!merges"] = merge;

      // Add basic styling to the header rows (now at their correct positions)
      const titleCell = ws["A1"];
      const dateCell = ws["A2"];
      const statsCell = ws["A3"];

      if (titleCell) {
        titleCell.s = {
          font: { bold: true, sz: 16 },
          alignment: { horizontal: "center", vertical: "center" },
        };
      }

      if (dateCell) {
        dateCell.s = {
          font: { italic: true, sz: 12 },
          alignment: { horizontal: "center", vertical: "center" },
        };
      }

      if (statsCell) {
        statsCell.s = {
          font: { bold: true, sz: 12 },
          alignment: { horizontal: "center", vertical: "center" },
        };
      }

      // Style the actual question headers (now starting after custom headers)
      const dataHeaderRowIndex = numCustomHeaderRows; // This is the 0-indexed row for your data headers (which is row 5 in Excel)

      dataHeaders.forEach((_header, colIndex) => {
        const cellAddress = XLSX.utils.encode_cell({
          r: dataHeaderRowIndex,
          c: colIndex,
        });
        if (ws[cellAddress]) {
          ws[cellAddress].s = {
            font: { bold: true, color: { rgb: "FFFFFF" } }, // White text
            fill: { fgColor: { rgb: "4F81BD" } }, // A shade of blue background
            alignment: {
              horizontal: "center",
              vertical: "center",
              wrapText: true,
            }, // Center align text
            border: {
              // Add borders
              top: { style: "thin", color: { rgb: "000000" } },
              bottom: { style: "thin", color: { rgb: "000000" } },
              left: { style: "thin", color: { rgb: "000000" } },
              right: { style: "thin", color: { rgb: "000000" } },
            },
          };
        }
      });

      // Set column widths
      // The `wch` values should match your desired widths for each of the `dataHeaders` columns.
      ws["!cols"] = [
        { wch: 50 }, // Question
        { wch: 20 }, // A
        { wch: 20 }, // B
        { wch: 20 }, // C
        { wch: 20 }, // D
        { wch: 15 }, // Correct
      ];

      // Create workbook and append sheet
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Questions");

      // Write the workbook to a file
      XLSX.writeFile(wb, `${moduleTitle}_questions.xlsx`);
    } catch (error) {
      console.error("Export error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center gap-4">
      <Image src={"/images/excel.png"} alt="Excel" width={90} height={90} />
      <p className="text-sm font-medium text-gray-500">
        {moduleTitle}_questions.xlsx
      </p>
      <Button
        disabled={loading}
        onClick={() => exportToExcel(questions)}
        className="disabled:opacity-50"
      >
        {loading ? (
          <>
            <Loader className="mr-2 h-4 w-4 animate-spin" /> Exporting to Excel
          </>
        ) : (
          <>
            <Download className="mr-2 h-4 w-4" /> Export to Excel
          </>
        )}
      </Button>
    </div>
  );
}
