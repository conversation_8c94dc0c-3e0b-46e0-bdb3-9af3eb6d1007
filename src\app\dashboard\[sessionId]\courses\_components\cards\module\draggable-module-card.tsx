"use client";

// import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Edit, Eye, GripVertical, Trash2 } from "lucide-react";
import {
  changeModuleOrder,
  deleteModule,
  ModulesWithDetails,
} from "@/lib/server/action/courses/modules";
import { toast } from "sonner";
import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { CustomSheet } from "@/components/shared/CustomSheet";
import ModuleForm from "../../forms/ModuleForm";
import { cn } from "@/lib/utils";
import { startTransition, useEffect, useOptimistic, useState } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

type OptimisticAction =
  | {
      type: "reorder";
      modules: ModulesWithDetails[];
    }
  | {
      type: "reset";
      modules: ModulesWithDetails[];
    };

export default function DraggableModuleCard({
  courseId,
  sessionId,
  initialModules,
}: {
  courseId: string;
  sessionId: string;
  initialModules: ModulesWithDetails[];
}) {
  const [modules, setModules] = useState(initialModules);

  const [optimisticModules, updateOptimisticModules] = useOptimistic(
    modules,
    (prevModules: ModulesWithDetails[], action: OptimisticAction) => {
      switch (action.type) {
        case "reorder":
          return action.modules;
        case "reset":
          return action.modules;
        default:
          return prevModules;
      }
    }
  );

  useEffect(() => {
    setModules(initialModules);
  }, [initialModules]);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = optimisticModules.findIndex(
        (module) => module.id === active.id
      );
      const newIndex = optimisticModules.findIndex(
        (module) => module.id === over.id
      );

      const reorderedModules = arrayMove(optimisticModules, oldIndex, newIndex);

      // Update order field for each module
      const updatedModules = reorderedModules.map((module, index) => ({
        ...module,
        order: index + 1,
      }));

      // Optimistically update UI
      startTransition(() => {
        updateOptimisticModules({ type: "reorder", modules: updatedModules });
      });

      console.log(initialModules, updatedModules, optimisticModules);

      try {
        await changeModuleOrder(updatedModules);
        setModules(updatedModules);
      } catch (error) {
        startTransition(() => {
          updateOptimisticModules({ type: "reset", modules: modules });
        });
        toast.error("Failed to update module order.");
        console.error(error);
      }
    }
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <SortableContext
        items={optimisticModules}
        strategy={verticalListSortingStrategy}
      >
        <div className="space-y-4">
          {optimisticModules.map((module) => (
            <ModuleCard
              key={module.id}
              module={module}
              courseId={courseId}
              sessionId={sessionId}
            />
          ))}
        </div>
      </SortableContext>
    </DndContext>
  );
}

const ModuleCard = ({
  module,
  courseId,
  sessionId,
}: {
  module: ModulesWithDetails;
  courseId: string;
  sessionId: string;
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: module.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={cn(
        "relative hover:shadow-md transition-shadow duration-200 py-4",
        isDragging ? "opacity-50 shadow-lg" : ""
      )}
    >
      <CardContent>
        <div className="absolute top-1 right-1 z-10">
          <Button
            {...attributes}
            {...listeners}
            size="icon"
            variant="ghost"
            className="cursor-grab"
          >
            <GripVertical />
          </Button>
        </div>
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-lg font-semibold">{module.title}</h3>
              {/* <Badge className={getStatusColor(module.status)}>
                    {module.status}
                  </Badge> */}
            </div>
            <p className="text-gray-600 text-sm mb-3">{module.description}</p>
            <div className="flex flex-wrap gap-4 text-sm text-gray-500">
              <span>{module.questions} questions</span>
              {/* <span>{module.timeLimit} minutes</span> */}
              <span>{module.totalPoints} points</span>
              <span>{module.attempts} attempts</span>
              <span>Created: {module.created}</span>
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link
                href={`/dashboard/${sessionId}/courses/${courseId}/modules/${module.id}`}
              >
                <Eye className="h-4 w-4 mr-1" />
                View
              </Link>
            </Button>
            <CustomSheet
              title="Edit Module"
              trigger={
                <Button variant="outline" size="sm">
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
              }
            >
              <ModuleForm
                courseId={courseId}
                moduleId={module.id}
                moduleData={{
                  title: module.title,
                  description: module.description || "",
                }}
              />
            </CustomSheet>
            <CustomAlertDialog
              trigger={
                <Button
                  variant="outline"
                  size="sm"
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              }
              title="Delete Module"
              description="Are you sure you want to delete this module?"
              onConfirm={async () => {
                const res = await deleteModule(module.id);
                if (res.success) {
                  toast.success("Module deleted");
                } else {
                  toast.error("Failed to delete module");
                }
              }}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
