"use client";

import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { Button } from "@/components/ui/button";
import { deleteAssessment } from "@/lib/server/action/courses/modules/assessments";
import { Trash2 } from "lucide-react";
import { toast } from "sonner";

export default function DeleteAssessment({
  assessmentId,
}: {
  assessmentId: string;
}) {
  return (
    <CustomAlertDialog
      trigger={
        <Button variant="destructive" size="sm">
          <Trash2 className="h-4 w-4 mr-1" />
          Delete
        </Button>
      }
      title="Delete Assessment"
      description="This action will remove assessment and question data for this module. Are you sure you want to delete this assessment?"
      onConfirm={async () => {
        const res = await deleteAssessment(assessmentId as string);
        if (res.success) {
          toast.success("Assessment deleted");
        } else {
          toast.error("Failed to delete assessment");
        }
      }}
    />
  );
}
