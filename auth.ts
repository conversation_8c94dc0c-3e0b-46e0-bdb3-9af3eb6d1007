/* eslint-disable @typescript-eslint/no-unused-vars */
import { getActiveSession } from "@/lib/server/action/sessions"
import { authenticateAdmin, authenticateLoginCode } from "@/lib/server/action/users/user.action"
import { UserRole } from "@prisma/client"
import NextAuth, { User } from "next-auth"
import Credentials from "next-auth/providers/credentials"
import { ZodError } from "zod"
 
export const { handlers, signIn, signOut, auth } = NextAuth({
  session: {
    strategy: 'jwt',
  },
  providers: [
    Credentials({
      name: "Credentials",
      credentials: {
        type: { label: "Type", type: "text" },
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        loginCode: { label: "Login Code", type: "password" },
      },
      authorize: async (credentials, req) => {
        try {
          if (
            !credentials?.type
            || (credentials.type === 'ep' && (!credentials.email || !credentials.password))
            || (credentials.type === 'lc' && !credentials.loginCode)
          ) {
            return null
          }

          let user: User | null = null

          if (credentials.type === 'ep' && credentials.email && credentials.password) {
            const admin = await authenticateAdmin(credentials.email.toString(), credentials.password.toString())

            if (!admin) {
              return null
            }

            user = admin
          } else if (credentials.type === 'lc' && credentials.loginCode) {
            const userData = await authenticateLoginCode(credentials.loginCode.toString())

            if (!userData) {
              return null
            }

            user = userData
          } else {
            return null
          }

          return user
        } catch (error) {
          if (error instanceof ZodError) {
            return null
          }
          return null
        }
      },
    })
  ],
  pages: {
    signIn: '/sign-in'
  },
  callbacks: {
    jwt: async ({ token, user }) => {
      if (user) {
        token.id = user.id
        token.firstName = user.firstName
        token.lastName = user.lastName
        token.email = user.email
        token.role = user.role
        token.profileId = user.profileId
        token.courseId = user.courseId
        token.isFirst = user.isFirst
        token.fileUrl = user.fileUrl
      }
      return token
    },
    session: async ({ session, token }) => {
      if (token) {
        session.user.id = token.id as string
        session.user.firstName = token.firstName as string
        session.user.lastName = token.lastName as string
        session.user.email = token.email as string
        session.user.role = token.role as UserRole
        session.user.profileId = token.profileId as string
        session.user.courseId = token.courseId as string | undefined
        session.user.isFirst = token.isFirst as boolean | undefined
        session.user.fileUrl = token.fileUrl as string | null
      }
      return session
    },
  },
})

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      role: UserRole;
      profileId: string;
      courseId?: string;
      isFirst?: boolean;
      fileUrl: string | null;
    }
  }
  interface User {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    role: UserRole;
    profileId: string;
    courseId?: string;
    isFirst?: boolean | null;
    fileUrl: string | null;
  }
  interface JWT {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    role: UserRole;
    profileId: string;
    courseId?: string;
    isFirst?: boolean;
    fileUrl: string | null;
  }
}