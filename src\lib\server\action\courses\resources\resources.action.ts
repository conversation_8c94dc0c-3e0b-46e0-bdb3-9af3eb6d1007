'use server'

import prisma from "@/lib/prisma"
import { ResourceType } from "@prisma/client";
import { revalidatePath } from "next/cache"
import { deleteFromBunny, deleteVideo } from "../../bunny/bunny.action";
import { auth } from "@/lib/server/auth";

type TResource = {
  name: string;
  type: ResourceType;
  fileId: string;
  fileUrl: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
}

export async function createCourseResourceFolder (courseId: string, data: { name: string }) {
  try {
    await prisma.courseResourceFolder.create({
      data: {
        ...data,
        course: { connect: { id: courseId } }
      }
    })

    revalidatePath(`/admin/courses/${courseId}/files`)
    
    return { success: true, message: 'Folder created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create folder' }
  }
}

export async function updateCourseResourceFolder (folderId: string, data: { name: string }) {
  try {
    await prisma.courseResourceFolder.update({
      where: { id: folderId },
      data
    })

    revalidatePath(`/admin/courses/${folderId}/files`)
    
    return { success: true, message: 'Folder updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update folder' }
  }
}

export async function deleteCourseResourceFolder (folderId: string) {
  try {
    const folder = await prisma.courseResourceFolder.findUnique({
      where: { id: folderId },
      select: { courseId: true }
    })
    
    if (!folder) {
      return { success: false, error: 'Folder not found' }
    }

    const resources = await prisma.courseResource.findMany({
      where: { folderId },
      select: { fileId: true, type: true }
    })
    
    for (const resource of resources) {
      if (resource.fileId) {
        if (resource.type === 'VIDEO') {
          await deleteVideo(resource.fileId)
        } else {
          await deleteFromBunny(resource.fileId)
        }
      }
    }

    await prisma.courseResourceFolder.delete({
      where: { id: folderId }
    })

    revalidatePath(`/admin/courses/${folder.courseId}/files`)
    
    return { success: true, message: 'Folder deleted' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete folder' }
  }
}

export async function uploadCourseResource (courseId: string, folderId: string, data: TResource) {
  const session = await auth()
  if (!session || !session.user) {
    return { success: false, error: 'Unauthorized' }
  }

  try {
    await prisma.courseResource.create({
      data: {
        ...data,
        uploadedBy: session.user.firstName + ' ' + session.user.lastName,
        course: { connect: { id: courseId } },
        folder: { connect: { id: folderId } }
      }
    })

    revalidatePath(`/admin/courses/${courseId}/files`)
    
    return { success: true, message: 'Resource uploaded' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create resource' }
  }
}

export async function updateCourseResource (resourceId: string, data: Partial<TResource>) {
  try {
    await prisma.courseResource.update({
      where: { id: resourceId },
      data
    })

    revalidatePath(`/admin/courses/${resourceId}/files`)
    
    return { success: true, message: 'Resource updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update resource' }
  }
}

export async function deleteCourseResource (resourceId: string) {
  try {
    const resource = await prisma.courseResource.findUnique({
      where: { id: resourceId },
      select: { fileId: true, type: true }
    })
    
    if (!resource) {
      return { success: false, error: 'Resource not found' }
    }

    if (resource.fileId) {
      if (resource.type === 'VIDEO') {
        await deleteVideo(resource.fileId)
      } else {
        await deleteFromBunny(resource.fileId)
      }
    }

    await prisma.courseResource.delete({
      where: { id: resourceId }
    })

    revalidatePath(`/admin/courses/${resourceId}/files`)
    
    return { success: true, message: 'Resource deleted' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete resource' }
  }
}