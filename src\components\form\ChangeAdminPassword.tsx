"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { Button } from "../ui/button";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import { FormInputField } from "../form-element/input-field";
import {
  adminPasswordSchema,
  TAdminPassword,
} from "@/lib/server/action/admins/admins.schema";
import { updateAdminPassword } from "@/lib/server/action/admins";

const ChangeAdminPasswordForm = ({ adminId }: { adminId: string }) => {
  const form = useForm<TAdminPassword>({
    resolver: zodResolver(adminPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
    mode: "onChange",
  });

  const onSubmit = async (values: TAdminPassword) => {
    const res = await updateAdminPassword(adminId, values);
    if (res.success) {
      toast.success("Password updated");
    } else {
      toast.error("Failed to update password");
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormInputField
          control={form.control}
          name="password"
          label="Password"
          type="password"
        />
        <FormInputField
          control={form.control}
          name="confirmPassword"
          label="Confirm Password"
          type="password"
        />
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader /> Updating Password
              </>
            ) : (
              <>Update Password</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default ChangeAdminPasswordForm;
