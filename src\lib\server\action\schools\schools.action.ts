'use server'

import prisma from "@/lib/prisma";
import { TSchool } from "./schools.schema";
import { revalidatePath } from "next/cache";

export async function createSchool (sessionId: string, data: TSchool) {
  try {
    const existingSchool = await prisma.school.findUnique({
      where: { name: data.name },
    });

    if (existingSchool) {
      return { success: false, error: "School with this name already exists" };
    }

    const school = await prisma.school.create({
      data: {
        name: data.name,
        description: data.description,
        session: { connect: { id: sessionId } },
      },
    });

    revalidatePath('/dashboard/schools')

    return { success: true, message: 'School created', school };
  } catch (error) {
    console.error("Error creating school:", error);
    return { success: false, error: "Failed to create school" };
  }
};

export async function updateSchool (schoolId: string, data: TSchool) {
  try {
    console.log(schoolId);
    
    const school = await prisma.school.update({
      where: { id: schoolId },
      data,
    });

    revalidatePath('/dashboard/schools')

    return { success: true, message: 'School updated', school };
  } catch (error) {
    console.error("Error updating school:", error);
    return { success: false, error: "Failed to update school" };
  }
};

export async function deleteSchool (schoolId: string) {
  try {
    await prisma.school.delete({
      where: { id: schoolId },
    });

    revalidatePath('/dashboard/schools')

    return { success: true };
  } catch (error) {
    console.error("Error deleting school:", error);
    return { success: false, error: "Failed to delete school" };
  }
};
