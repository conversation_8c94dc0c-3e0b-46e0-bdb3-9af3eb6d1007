"use client";

import { useEffect, useState } from "react";
import { FileUpload } from "./shared/file-upload";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  generateFileUploadUrl,
  getFileType,
} from "@/lib/server/action/bunny/bunny.action";
import { uploadFileToBunny } from "@/lib/bunny";
import { uploadCourseResource } from "@/lib/server/action/courses/resources";
import { Progress } from "@/components/ui/progress";
import { formatBytes } from "@/lib/utils";

export function CourseResourceUpload({
  courseId,
  folderOptions,
  folderId,
}: {
  courseId: string;
  folderOptions?: { label: string; value: string }[];
  folderId?: string;
}) {
  const [dragActive, setDragActive] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  // State to store the names of the files
  const [fileNames, setFileNames] = useState<string[]>([]);
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [uploadState, setUploadState] = useState({
    progress: 0,
    isUploading: false,
    uploadedBytes: 0,
    totalSize: 0,
  });

  const MAX_FILE_SIZE = 1024 * 1024 * 1024; // 1GB

  useEffect(() => {
    if (files.length > 0) {
      setFileNames(files.map((file) => file.name));
    }
  }, [files]);

  const handleFiles = (files: File[]) => {
    setFiles(files);
  };

  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  };

  const updateUploadProgress = (
    percentage: number,
    loaded: number,
    total: number
  ) => {
    setUploadState({
      progress: Math.round(percentage),
      isUploading: true,
      uploadedBytes: loaded,
      totalSize: total,
    });
  };

  const handleUploadCourseResource = async (folderId: string, file: File) => {
    if (file.size > MAX_FILE_SIZE) {
      toast.error("File size exceeds the limit of 1GB");
      return { success: false, error: "File size exceeds the limit of 1GB" };
    }

    const { fileId, uploadUrl, accessKey, cdnUrl } =
      await generateFileUploadUrl(file.name);

    await uploadFileToBunny(file, uploadUrl, accessKey, updateUploadProgress);

    const res = await uploadCourseResource(courseId, folderId, {
      name: file.name,
      fileId,
      fileUrl: cdnUrl,
      type: await getFileType(file.type),
      originalName: file.name,
      fileSize: file.size,
      mimeType: file.type,
    });

    return res;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    setUploadState((prev) => ({
      ...prev,
      isUploading: true,
      progress: 0,
      uploadedBytes: 0,
      totalSize: 0,
    }));

    if ((!selectedFolder && !folderId) || !files.length) {
      toast.error("Please select a folder and files to upload");
      return;
    }

    const folderIdToUse = folderId || selectedFolder!;

    try {
      for (const file of files) {
        const result = await handleUploadCourseResource(folderIdToUse, file);

        if (result.success) {
          toast.success(
            "message" in result && result.message
              ? result.message
              : "File uploaded"
          );
        } else {
          toast.error(result.error);
        }
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Failed to upload file");
    } finally {
      setUploadState((prev) => ({
        ...prev,
        isUploading: false,
      }));
    }
  };

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit} className="space-y-4">
        {folderOptions ? (
          <Select onValueChange={setSelectedFolder}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a folder" />
            </SelectTrigger>
            <SelectContent>
              {folderOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        ) : null}
        <div>
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              dragActive
                ? "border-blue-400 bg-blue-50"
                : "border-gray-300 hover:border-gray-400"
            } ${
              uploadState.isUploading ? "opacity-50 pointer-events-none" : ""
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <FileUpload
              onSuccess={handleFiles}
              accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.mp4,.mov,.avi"
              multiple={true}
            >
              <div className="space-y-2">
                <div className="text-gray-600">
                  {uploadState.isUploading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                      <span>Uploading...</span>
                    </div>
                  ) : (
                    <>
                      <p className="text-lg">
                        Drop files here or click to browse
                      </p>
                      <p className="text-sm text-gray-400">
                        Supports documents, images, and videos
                      </p>
                    </>
                  )}
                </div>
              </div>
            </FileUpload>
          </div>
        </div>

        {/* Display the list of file names */}
        {fileNames.length > 0 && (
          <div className="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
            <p className="text-sm font-semibold mb-2">Files to upload:</p>
            <ul className="list-disc list-inside text-sm text-gray-700">
              {fileNames.map((name, index) => (
                <li key={index}>{name}</li>
              ))}
            </ul>
          </div>
        )}

        {uploadState.isUploading && (
          <div className="space-y-2 mt-4">
            <div className="flex justify-between text-sm">
              <span>Uploading File...</span>
              <span>{uploadState.progress}%</span>
            </div>
            <Progress value={uploadState.progress} className="w-full" />
            <div className="text-xs text-muted-foreground">
              {formatBytes(uploadState.uploadedBytes)} /{" "}
              {formatBytes(uploadState.totalSize)}
            </div>
          </div>
        )}

        <Button type="submit">Upload</Button>
      </form>
    </div>
  );
}
