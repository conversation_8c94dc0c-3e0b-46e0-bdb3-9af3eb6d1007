/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useCallback, useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON><PERSON>gle, Loader2, Save } from "lucide-react";
import { toast } from "sonner";
import {
  CMSData,
  updateCMSData,
} from "@/lib/server/action/frontend/frontend.action";
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import { Alert, AlertDescription } from "@/components/ui/alert";
import GeneralTab from "./tabs/general";
import HeroTab from "./tabs/hero";
import FeaturesTab from "./tabs/features";
import AboutTab from "./tabs/about";

export default function DashboardPage({ cmsData }: { cmsData: CMSData }) {
  const [data, setData] = useState<CMSData>(cmsData);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const markAsChanged = () => {
    if (!hasUnsavedChanges) {
      setHasUnsavedChanges(true);
    }
  };

  const markAsSaved = () => {
    setHasUnsavedChanges(false);
  };

  // Use the unsaved changes hook
  const { resetUnsavedChanges } = useUnsavedChanges({
    hasUnsavedChanges,
    message:
      "You have unsaved changes to your CMS content. Are you sure you want to leave without saving?",
  });

  const handleSave = useCallback(async () => {
    if (!data) return;

    setIsSaving(true);
    try {
      await updateCMSData(data);
      markAsSaved();
      resetUnsavedChanges();
      toast.success("Changes saved successfully!");
    } catch (error) {
      console.error("Error saving changes:", error);
      toast.error("Failed to save changes. Please try again.");
    } finally {
      setIsSaving(false);
    }
  }, [data, resetUnsavedChanges]);

  // Add keyboard shortcut for saving (Ctrl+S / Cmd+S)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === "s") {
        e.preventDefault();
        if (hasUnsavedChanges && !isSaving) {
          handleSave();
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [hasUnsavedChanges, isSaving, handleSave]);

  const updateNestedData = (path: string[], value: any) => {
    if (!data) return;

    const newData = { ...data };
    let current: any = newData;

    for (let i = 0; i < path.length - 1; i++) {
      current = current[path[i]];
    }

    current[path[path.length - 1]] = value;
    setData(newData);
    markAsChanged();
  };

  return (
    <div>
      <div className="border-b">
        <div className="flex items-center justify-between pb-4">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold tracking-tight">
              Content Management Dashboard
            </h1>
            {hasUnsavedChanges && (
              <span className="text-sm text-orange-600 font-medium">
                • Unsaved changes
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {hasUnsavedChanges && (
              <span className="text-xs text-gray-500">
                Press Ctrl+S to save
              </span>
            )}
            <Button
              onClick={handleSave}
              disabled={isSaving}
              size="sm"
              className={`flex items-center space-x-1 ${
                hasUnsavedChanges ? "bg-orange-600 hover:bg-orange-700" : ""
              }`}
            >
              {isSaving ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              <span>
                {isSaving
                  ? "Saving..."
                  : hasUnsavedChanges
                  ? "Save Changes *"
                  : "Save Changes"}
              </span>
            </Button>
          </div>
        </div>
      </div>

      {hasUnsavedChanges && (
        <Alert className="mt-4 border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            You have unsaved changes. Don&apos;t forget to save your work before
            leaving the page.
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="general" className="space-y-6 mt-8">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="hero">Hero Section</TabsTrigger>
          <TabsTrigger value="about">About Page</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <GeneralTab
            site={data.site}
            contact={data.contact}
            onboarding={data.onboarding}
            data={data}
            setData={setData}
            markAsChanged={markAsChanged}
            updateNestedData={updateNestedData}
          />
        </TabsContent>

        <TabsContent value="hero" className="space-y-6">
          <HeroTab hero={data.hero} updateNestedData={updateNestedData} />
        </TabsContent>

        <TabsContent value="about" className="space-y-6">
          <AboutTab about={data.about} updateNestedData={updateNestedData} />
        </TabsContent>

        <TabsContent value="features" className="space-y-6">
          <FeaturesTab
            features={data.features}
            newsletter={data.newsletter}
            updateNestedData={updateNestedData}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
