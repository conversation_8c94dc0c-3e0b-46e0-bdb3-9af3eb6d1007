"use client";

import { useEffect, useRef, useState } from "react";
import NextVideo from "next-video/player";
import { cn } from "@/lib/utils";
import {
  getVideoProgress,
  trackLessonVideoProgress,
} from "@/lib/server/action/students/modules/lesson/lesson.action";
import { Button } from "@/components/ui/button";
import { Loader, Loader2, Pause, Play } from "lucide-react";
import Duration from "@/components/shared/Duration";
import { Progress } from "@/components/ui/progress";

interface VideoPlayerProps {
  lessonId: string;
  studentId: string;
  videoId: string;
  title: string;
  duration: number;
  controls?: boolean;
  className?: string;
  onProceed: () => void;
  onCancel: () => void;
  hasWatched?: boolean;
  videoUrl: string;
  thumbnailUrl: string;
}

export default function NextPlayer({
  lessonId,
  studentId,
  videoId,
  title,
  duration,
  controls = false,
  className,
  onProceed,
  onCancel,
  hasWatched,
  videoUrl,
  thumbnailUrl,
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [state, setState] = useState({
    isLoaded: false,
    videoProgress: 0,
    ended: false,
    playing: false,
    showControls: false,
  });

  const progress = videoRef.current?.currentTime || 0;

  // Load video progress on mount
  useEffect(() => {
    const loadVideoProgress = async () => {
      try {
        const res = await getVideoProgress(lessonId, studentId);
        setState((prev) => ({ ...prev, videoProgress: res || 0 }));
      } catch (error) {
        console.error("Error loading video progress:", error);
      } finally {
        setState((prev) => ({ ...prev, isLoaded: true }));
      }
    };

    loadVideoProgress();
  }, [lessonId, studentId]);

  // Save progress periodically
  useEffect(() => {
    if (state.ended) {
      return;
    }

    const interval = setInterval(() => {
      if (videoRef.current) {
        const progress = Math.floor(videoRef.current.currentTime);

        const saveVideoProgress = async (progress: number) => {
          try {
            await trackLessonVideoProgress({
              lessonId,
              studentId,
              videoDuration: duration,
              progress,
              isCompleted: progress >= duration * 0.9,
            });
          } catch (error) {
            console.error("Error saving video progress:", error);
          }
        };

        saveVideoProgress(progress);
      }
    }, 10000); // Save every 10 seconds

    return () => clearInterval(interval);
  }, [lessonId, studentId, videoId, duration, state.ended]);

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (state.playing) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setState((prev) => ({ ...prev, playing: !prev.playing }));
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current && state.videoProgress > 0) {
      videoRef.current.currentTime = state.videoProgress;
    }
  };

  const handleMouseMove = () => {
    if (!controls) {
      setState((prev) => ({ ...prev, showControls: true }));
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      controlsTimeoutRef.current = setTimeout(() => {
        setState((prev) => ({ ...prev, showControls: false }));
      }, 3000); // Hide controls after 3 seconds of inactivity
    }
  };

  const handleMouseEnter = () => {
    if (!controls) {
      setState((prev) => ({ ...prev, showControls: true }));
    }
  };

  const handleMouseLeave = () => {
    if (!controls) {
      setState((prev) => ({ ...prev, showControls: false }));
    }
  };

  return (
    <div
      className="flex-1 w-full"
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className={cn("w-full rounded-xl flex-none", className)}>
        <div className="relative aspect-video flex-none bg-black">
          {!state.isLoaded ? (
            <div className="size-full flex justify-center items-center">
              <Loader2 className="h-8 w-8 animate-spin text-white" />
            </div>
          ) : (
            <NextVideo
              ref={videoRef}
              src={videoUrl}
              controls={controls}
              poster={thumbnailUrl}
              className="w-full h-full"
              onLoadedMetadata={handleLoadedMetadata}
              onEnded={() => setState((prev) => ({ ...prev, ended: true }))}
            />
          )}

          {!controls ? (
            <div
              className={cn(
                "absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 transition-opacity duration-300",
                state.showControls ? "opacity-100" : "opacity-0"
              )}
            >
              <div className="flex items-center gap-2 w-full">
                {/* Play/Pause button */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white hover:bg-white/10"
                  onClick={handlePlayPause}
                >
                  {state.playing ? (
                    <Pause className="w-5 h-5" />
                  ) : (
                    <Play className="w-5 h-5" />
                  )}
                </Button>

                <div className="text-white text-sm">
                  <Duration seconds={progress} />/
                  <Duration seconds={duration} />
                </div>

                {/* Progress bar */}
                <Progress
                  value={(progress / duration) * 100}
                  className="w-full mr-4"
                />
              </div>
            </div>
          ) : null}
        </div>

        <div className="flex w-full items-center justify-between gap-x-4 p-2.5 sm:p-4">
          <p className="text-sm text-gray-600">{title}</p>
          <div className="flex gap-x-4">
            <Button
              size="sm"
              variant="outline"
              className="border-primary/80"
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button
              onClick={onProceed}
              disabled={(!state.ended && !hasWatched) || !state.isLoaded}
              className="disabled:bg-gray-400 disabled:text-gray-200 disabled:cursor-not-allowed bg-primary text-white"
              size="sm"
            >
              {!state.isLoaded ? (
                <span className="flex items-center gap-2">
                  <Loader className="h-4 w-4 animate-spin" />
                  Processing...
                </span>
              ) : hasWatched ? (
                "Skip"
              ) : (
                "Proceed"
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
