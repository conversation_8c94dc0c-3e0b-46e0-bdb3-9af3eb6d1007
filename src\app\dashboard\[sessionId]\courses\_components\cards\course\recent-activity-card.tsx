import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Users } from "lucide-react";

export default function RecentActivityCard({
  recentActivity,
}: {
  recentActivity: {
    quiz: string;
    student: string;
    score: number;
    date: string | null;
  }[];
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>Latest quiz attempts and scores</CardDescription>
      </CardHeader>
      <CardContent>
        {recentActivity.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No activity yet.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {recentActivity.map((activity, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Users className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">{activity.student}</p>
                    <p className="text-xs text-gray-500">
                      completed &quot;{activity.quiz}&quot;
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-green-600">
                    {activity.score}%
                  </div>
                  <div className="text-xs text-gray-500">{activity.date}</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
