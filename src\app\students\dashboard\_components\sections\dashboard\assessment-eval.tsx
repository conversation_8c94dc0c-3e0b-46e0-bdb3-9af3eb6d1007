import { getInitialGradeByStudentId } from "@/lib/server/action/students/reports/reports.list";

type AssessmentEvalProps = {
  studentId: string;
  completed: number;
  notYet: number;
};

export default async function AssessmentEval({
  studentId,
  completed,
  notYet,
}: AssessmentEvalProps) {
  const initialGrade = await getInitialGradeByStudentId(studentId);
  return (
    <section className="col-span-1 border-2 border-gray-200 rounded-xl shadow-sm overflow-hidden">
      <div className="flex justify-between gap-4 h-full">
        <div className="p-4 size-full">
          <h2 className="text-base font-semibold">My Assessment Evaluation</h2>
          <div className="flex flex-col items-center justify-center gap-4 size-full">
            <div className="relative w-fit h-fit">
              <svg
                className="w-24 h-24 transform -rotate-90"
                viewBox="0 0 36 36"
              >
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#e5e7eb"
                  strokeWidth="2"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="2"
                  strokeDasharray={`${
                    (completed / (completed + notYet)) * 100
                  }, 100`}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xl font-bold">
                  {((completed / (completed + notYet)) * 100 || 0).toFixed(0)}%
                </span>
              </div>
            </div>
            <div className="w-full">
              <div className="flex justify-between items-center">
                <span className="text-sm">Completed</span>
                <span className="font-bold text-blue-600">
                  {completed || 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Not Yet</span>
                <span className="font-bold">{notYet || 0}</span>
              </div>
            </div>
          </div>
        </div>
        <div className="bg-muted h-full flex items-center">
          <div className="text-center p-4">
            <span className="text-sm text-muted-foreground whitespace-nowrap">
              INITIAL GRADE:
            </span>
            <div className="text-4xl font-bold text-green-500">
              {initialGrade || 0}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
