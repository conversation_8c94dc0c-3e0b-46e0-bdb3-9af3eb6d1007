/* eslint-disable @typescript-eslint/no-explicit-any */
"use server";

import { PrismaClient } from "@prisma/client";
import { revalidatePath } from "next/cache";

// Initialize Prisma client
const prisma = new PrismaClient();

// Types for the CMS data structure
export interface CMSData {
  id: string
  site: {
    name: string;
    logo: string;
    footerLogo: string;
  };
  login: {
    items: Array<{
      image: string;
      title: string;
      subtitle: string;
    }>;
  };
  onboarding: {
    welcomeMessage: string;
    dataPrivacyMessage: string;
  };
  navigation: {
    items: Array<{
      label: string;
      href: string;
    }>;
  };
  hero: {
    title: string;
    subtitle: string;
    ctaText: string;
    ctaLink: string;
    image: string;
  };
  about: {
    title: string;
    description: string;
    brandIdentity: {
      title: string;
      logo: string;
      description: string;
    };
    designElements: Array<{
      title: string;
      details: string[];
      image: string;
    }>;
  };
  features: {
    title: string;
    description?: string;
    items: Array<{
      title: string;
      details: string[];
      image: string;
    }>;
  };
  newsletter: {
    title: string;
    placeholder: string;
    buttonText: string;
  };
  contact: {
    title: string;
    email: string;
    mobile: string;
    landline: string;
    address: string;
  };
}

// Default CMS data for initialization
const defaultCMSData: CMSData = {
  id: '',
  site: {
    name: "Complexus Pathways",
    logo: "/images/placeholder.svg?height=40&width=40",
    footerLogo: "/images/placeholder.svg?height=40&width=40",
  },
  login: {
    items: [
      {
        image: "/images/placeholder.svg",
        title: "Join Our Network,",
        subtitle: "Transform Your School",
      },
      {
        image: "/images/placeholder.svg",
        title: "Empower Educators,",
        subtitle: "Anytime, Anywhere",
      },
      {
        image: "/images/placeholder.svg",
        title: "Advance Your Practice,",
        subtitle: 'With Complexus Pathways',
      },
    ],
  },
  onboarding: {
    welcomeMessage: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry&apos;s standard dummy text  ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.",
    dataPrivacyMessage:
      "Your privacy and the security of your personal information are of utmost importance to us at the Virtue Series Training Platform. By using this online platform, you agree to our collection, use, and storage of your data in accordance with our privacy policy. We ensure that all personal information is handled with the highest level of confidentiality and is used solely for the purpose of enhancing your training experience. All content on this platform, including but not limited to course materials, videos, and assessments, is the intellectual property of CEAP-NCR and is protected by copyright laws. Unauthorized use, reproduction, or distribution of any materials is strictly prohibited and subject to legal action.",
  },
  navigation: {
    items: [
      { label: "Home", href: "/" },
      { label: "About", href: "/about" },
    ],
  },
  hero: {
    title: "Form Minds, Shape Hearts, Walk The Path.",
    subtitle:
      "The platform was designed to provide holistic and personalized experience",
    ctaText: "Get Started",
    ctaLink: "/sign-in",
    image: "/images/placeholder.svg?height=400&width=600",
  },
  about: {
    title: "About the platform",
    description:
      "Complexus Pathways is a next-generation Learning Management System (LMS) designed to provide a holistic and personalized learning experience.",
    brandIdentity: {
      title: "Brand Identity",
      logo: "/images/placeholder.svg?height=120&width=120",
      description:
        "Complexus Pathways - Form minds, shape hearts, walk the path",
    },
    designElements: [
      {
        title: "Shield Shape",
        details: [
          "Represents protection and security",
          "Symbolizes trust and reliability",
          "Conveys strength and stability",
        ],
        image: "/images/placeholder.svg?height=80&width=80",
      },
      {
        title: "Y-Shaped Pathway",
        details: [
          "Represents choice and direction",
          "Symbolizes growth and branching",
          "Conveys multiple learning paths",
        ],
        image: "/images/placeholder.svg?height=80&width=80",
      },
      {
        title: "Three Orange Circles",
        details: [
          "Represents unity and connection",
          "Symbolizes continuous learning",
          "Conveys community and collaboration",
        ],
        image: "/images/placeholder.svg?height=80&width=80",
      },
      {
        title: "Color Palette",
        details: [
          "Primary blue for trust and professionalism",
          "Orange accents for energy and creativity",
          "Neutral grays for balance and readability",
        ],
        image: "/images/placeholder.svg?height=80&width=80",
      },
      {
        title: "Typography",
        details: [
          "Clean, modern sans-serif fonts",
          "Excellent readability across devices",
          "Consistent hierarchy and spacing",
        ],
        image: "/images/placeholder.svg?height=80&width=80",
      },
    ],
  },
  features: {
    title: "Introduction to core features",
    description: "Streamline your entire learning management operations with our comprehensive suite of integrated modules designed specifically for modern educational institutions.",
    items: [
      {
        title: "Personalized Learning Pathways",
        details: [
          "Adaptive learning algorithms",
          "Individual progress tracking",
          "Customized content delivery",
          "Personal learning analytics",
        ],
        image: "/images/placeholder.svg?height=120&width=120",
      },
      {
        title: "Spiral and Modular Curriculum",
        details: [
          "Progressive skill building",
          "Interconnected learning modules",
          "Flexible course structure",
          "Reinforcement through repetition",
        ],
        image: "/images/placeholder.svg?height=120&width=120",
      },
    ],
  },
  newsletter: {
    title: "Subscribe your email for latest updates!",
    placeholder: "Enter your email",
    buttonText: "Subscribe",
  },
  contact: {
    title: "Contact Information",
    email: "<EMAIL>",
    mobile: "-",
    landline: "(02) 994-8403",
    address: "Centre Catholic College, A. Bonifacio Ave., Cainta Rizal 1900",
  },
};

/**
 * Get the current CMS data for a specific environment
 */
export async function getCMSData(
): Promise<CMSData> {
  try {
    // Find the active configuration for the specified environment
    const cmsData = await prisma.cMSConfig.findFirst({});

    if (!cmsData) {
      // If no active configuration exists, create the default one
      return await initializeCMSData();
    }

    // Transform database data back to CMSData format
    const config = cmsData;
    return {
      id: config.id,
      site: {
        name: config.siteName,
        logo: config.siteLogo,
        footerLogo: config.footerLogo,
      },
      login: {
        items: config.loginItems as any,
      },
      onboarding: {
        welcomeMessage: config.welcomeMessage,
        dataPrivacyMessage: config.dataPrivacyMessage,
      },
      navigation: config.navigation as any,
      hero: {
        title: config.heroTitle,
        subtitle: config.heroSubtitle,
        ctaText: config.heroCtaText,
        ctaLink: config.heroCtaLink,
        image: config.heroImage,
      },
      about: {
        title: config.aboutTitle,
        description: config.aboutDescription,
        brandIdentity: config.brandIdentity as any,
        designElements: config.designElements as any,
      },
      features: {
        title: config.featuresTitle,
        description: config.featuresDescription || undefined,
        items: config.featuresItems as any,
      },
      newsletter: {
        title: config.newsletterTitle,
        placeholder: config.newsletterPlaceholder,
        buttonText: config.newsletterButtonText,
      },
      contact: {
        title: config.contactTitle,
        email: config.contactEmail,
        mobile: config.contactMobile,
        landline: config.contactLandline,
        address: config.contactAddress,
      },
    };
  } catch (error) {
    console.error("Error fetching CMS data:", error);
    throw new Error("Failed to fetch CMS data");
  }
}

/**
 * Initialize CMS data with default values
 */
export async function initializeCMSData(): Promise<CMSData> {
  try {
    // Create new CMS configuration
    await prisma.cMSConfig.create({
      data: {
        siteName: defaultCMSData.site.name,
        siteLogo: defaultCMSData.site.logo,
        footerLogo: defaultCMSData.site.footerLogo,
        loginItems: defaultCMSData.login.items,
        welcomeMessage: defaultCMSData.onboarding.welcomeMessage,
        dataPrivacyMessage: defaultCMSData.onboarding.dataPrivacyMessage,
        navigation: defaultCMSData.navigation,
        heroTitle: defaultCMSData.hero.title,
        heroSubtitle: defaultCMSData.hero.subtitle,
        heroCtaText: defaultCMSData.hero.ctaText,
        heroCtaLink: defaultCMSData.hero.ctaLink,
        heroImage: defaultCMSData.hero.image,
        aboutTitle: defaultCMSData.about.title,
        aboutDescription: defaultCMSData.about.description,
        brandIdentity: defaultCMSData.about.brandIdentity,
        designElements: defaultCMSData.about.designElements,
        featuresTitle: defaultCMSData.features.title,
        featuresDescription: defaultCMSData.features.description,
        featuresItems: defaultCMSData.features.items,
        newsletterTitle: defaultCMSData.newsletter.title,
        newsletterPlaceholder: defaultCMSData.newsletter.placeholder,
        newsletterButtonText: defaultCMSData.newsletter.buttonText,
        contactTitle: defaultCMSData.contact.title,
        contactEmail: defaultCMSData.contact.email,
        contactMobile: defaultCMSData.contact.mobile,
        contactLandline: defaultCMSData.contact.landline,
        contactAddress: defaultCMSData.contact.address,
      },
    });

    return defaultCMSData;
  } catch (error) {
    console.error("Error initializing CMS data:", error);
    throw new Error("Failed to initialize CMS data");
  }
}

/**
 * Update CMS data
 */
export async function updateCMSData(
  data: Partial<CMSData>,
): Promise<void> {
  try {
    // Prepare update data
    const updateData: any = {};

    if (data.site) {
      if (data.site.name !== undefined) updateData.siteName = data.site.name;
      if (data.site.logo !== undefined) updateData.siteLogo = data.site.logo;
      if (data.site.footerLogo !== undefined)
        updateData.footerLogo = data.site.footerLogo;
    }

    if (data.login) {
      updateData.loginItems = data.login.items;
    }

    if (data.onboarding) {
      if (data.onboarding.welcomeMessage !== undefined)
        updateData.welcomeMessage = data.onboarding.welcomeMessage;
      if (data.onboarding.dataPrivacyMessage !== undefined)
        updateData.dataPrivacyMessage = data.onboarding.dataPrivacyMessage;
    }

    if (data.navigation) {
      updateData.navigation = data.navigation;
    }

    if (data.hero) {
      if (data.hero.title !== undefined) updateData.heroTitle = data.hero.title;
      if (data.hero.subtitle !== undefined)
        updateData.heroSubtitle = data.hero.subtitle;
      if (data.hero.ctaText !== undefined)
        updateData.heroCtaText = data.hero.ctaText;
      if (data.hero.ctaLink !== undefined)
        updateData.heroCtaLink = data.hero.ctaLink;
      if (data.hero.image !== undefined) updateData.heroImage = data.hero.image;
    }

    if (data.about) {
      if (data.about.title !== undefined)
        updateData.aboutTitle = data.about.title;
      if (data.about.description !== undefined)
        updateData.aboutDescription = data.about.description;
      if (data.about.brandIdentity !== undefined)
        updateData.brandIdentity = data.about.brandIdentity;
      if (data.about.designElements !== undefined)
        updateData.designElements = data.about.designElements;
    }

    if (data.features) {
      if (data.features.title !== undefined)
        updateData.featuresTitle = data.features.title;
      if (data.features.description !== undefined)
        updateData.featuresDescription = data.features.description;
      if (data.features.items !== undefined)
        updateData.featuresItems = data.features.items;
    }

    if (data.newsletter) {
      if (data.newsletter.title !== undefined)
        updateData.newsletterTitle = data.newsletter.title;
      if (data.newsletter.placeholder !== undefined)
        updateData.newsletterPlaceholder = data.newsletter.placeholder;
      if (data.newsletter.buttonText !== undefined)
        updateData.newsletterButtonText = data.newsletter.buttonText;
    }

    if (data.contact) {
      if (data.contact.title !== undefined)
        updateData.contactTitle = data.contact.title;
      if (data.contact.email !== undefined)
        updateData.contactEmail = data.contact.email;
      if (data.contact.mobile !== undefined)
        updateData.contactMobile = data.contact.mobile;
      if (data.contact.landline !== undefined)
        updateData.contactLandline = data.contact.landline;
      if (data.contact.address !== undefined)
        updateData.contactAddress = data.contact.address;
    }

    // Update the configuration
    await prisma.cMSConfig.update({
      where: { id: data.id },
      data: updateData,
    });

    // Revalidate relevant paths
    revalidatePath("/");
    revalidatePath("/about");
    revalidatePath("/dashboard");
  } catch (error) {
    console.error("Error updating CMS data:", error);
    throw new Error("Failed to update CMS data");
  }
}

export async function getSiteData () {
  try {
    const settings = await prisma.cMSConfig.findFirst({
      select: {
        siteName: true,
        siteLogo: true,
        footerLogo: true,
        contactEmail: true,
        contactMobile: true,
        contactLandline: true,
        contactAddress: true,
      },
    });

    return settings;
  } catch (error) {
    console.log(error);
    return null;
  }
}

export type SiteData = Awaited<ReturnType<typeof getSiteData>>;

export async function getLoginData () {
  try {
    const settings = await prisma.cMSConfig.findFirst({
      select: {
        siteName: true,
        loginItems: true,
      },
    });

    return settings?.loginItems as Array<{
      image: string;
      title: string;
      subtitle: string;
    }>;
  } catch (error) {
    console.log(error);
    return null;
  }
}

export async function getOnboardingData () {
  try {
    const settings = await prisma.cMSConfig.findFirst({
      select: {
        siteLogo: true,
        siteName: true,
        welcomeMessage: true,
        dataPrivacyMessage: true,
      },
    });

    return settings;
  } catch (error) {
    console.log(error);
    return null;
  }
}
