import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Text, User } from "lucide-react";
import Image from "next/image";

interface CourseHeaderProps {
  studentId: string;
  course: {
    title: string;
    description: string;
    program: string;
    _count: {
      enrollments: number;
      modules: number;
    };
    teacher: string;
    progress: number;
    image: string;
  };
}

export function CourseHeader({ studentId, course }: CourseHeaderProps) {
  console.log(studentId);

  return (
    <div className="bg-white border-b-2">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="flex flex-wrap gap-2 mb-3">
              <Badge variant="secondary">{course.program}</Badge>
              <Badge>{course.title}</Badge>
            </div>

            <h1 className="text-3xl font-bold mb-3">{course.title}</h1>
            <p className="text-gray-600 text-lg mb-4">{course.description}</p>
            <p className="text-gray-700 mb-4">
              Teacher: <span className="font-medium">{course.teacher}</span>
            </p>

            <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-6">
              <div className="flex items-center gap-2">
                <Text className="h-4 w-4" />
                {course._count.modules} modules
              </div>
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                {course._count.enrollments} students
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Modules Progress</span>
                <span className="font-medium">
                  {Math.round(course.progress)}%
                </span>
              </div>
              <Progress value={course.progress} className="h-3" />
            </div>
          </div>

          <div className="lg:col-span-1">
            <div className="relative">
              <Image
                src={course.image}
                alt={course.title}
                width={500}
                height={400}
                className="aspect-video w-full h-64 object-cover rounded-lg"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
