'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"
import { trackModuleProgress } from "../../progress-tracking/progress-tracking.action";

export async function trackLessonVideoProgress({lessonId, studentId, videoDuration, progress, isCompleted }: {lessonId: string, studentId: string, videoDuration: number, progress?: number, isCompleted?: boolean}) {
  try {
    const lessonProgress = await prisma.lessonVideoProgress.upsert({
      where: {
        studentId_lessonId: {
          studentId,
          lessonId
        }
      },
      update: {
        progress,
        lastAccessed: new Date(),
        completedAt: (progress ?? 0) >= 100 ? new Date() : null,
        isCompleted
      },
      create: {
        lessonId,
        studentId,
        videoDuration,
        hasWatched: true,
      },
      select: { hasWatched: true, lesson: { select: { moduleId: true } } }
    })

    if (!lessonProgress.hasWatched) {
      await trackModuleProgress(studentId, lessonProgress.lesson.moduleId, 50);
    }

    revalidatePath('/student/dashboard/modules')
    
    return { success: true, lessonProgress }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to track progress' }
  }
}

export async function getVideoProgress(lessonId: string, studentId: string) {
  try {
    const progress = await prisma.lessonVideoProgress.findUnique({
      where: {
        studentId_lessonId: {
          studentId,
          lessonId
        }
      },
      select: {
        progress: true
      }
    })
    
    return progress?.progress;
  } catch (error) {
    console.log(error)
    return null
  }
}