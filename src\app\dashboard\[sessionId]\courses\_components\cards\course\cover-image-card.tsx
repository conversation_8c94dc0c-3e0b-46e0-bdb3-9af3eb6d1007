"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { appConfig } from "@/config/app";
import { uploadFileToBunny } from "@/lib/bunny";
import { validateImageFile } from "@/lib/file-validators";
import { generateFileUploadUrl } from "@/lib/server/action/bunny/bunny.action";
import {
  deleteCourseImage,
  uploadCourseImage,
} from "@/lib/server/action/courses";
import { formatBytes } from "@/lib/utils";
import { Upload, X } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { toast } from "sonner";

export default function CourseCoverImageCard({
  courseId,
  fileUrl,
}: {
  courseId?: string;
  fileUrl?: string | null;
}) {
  const [file, setFile] = useState<File | null>(null);
  const [uploadState, setUploadState] = useState({
    progress: 0,
    isUploading: false,
    uploadedBytes: 0,
    totalSize: 0,
  });

  const updateUploadProgress = (
    percentage: number,
    loaded: number,
    total: number
  ) => {
    setUploadState({
      progress: Math.round(percentage),
      isUploading: true,
      uploadedBytes: loaded,
      totalSize: total,
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFile(file);
    }
  };

  const handleImageUpload = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!file) {
      toast.error("Please select a file");
      return;
    }

    setUploadState((prev) => ({
      ...prev,
      isUploading: true,
      progress: 0,
      uploadedBytes: 0,
      totalSize: 0,
    }));

    try {
      const validation = validateImageFile(file, appConfig.MAX_IMAGE_SIZE_MB);
      if (!validation.valid) {
        toast.error(validation.error || "Invalid file");
        return;
      }

      const { fileId, uploadUrl, accessKey, cdnUrl } =
        await generateFileUploadUrl(file.name);

      await uploadFileToBunny(file, uploadUrl, accessKey, updateUploadProgress);

      const res = await uploadCourseImage(courseId as string, {
        fileId,
        fileUrl: cdnUrl,
      });
      if (res.success) {
        toast.success(res.message);
      } else {
        toast.error(res.error);
      }
    } catch (error) {
      console.error(error);
      toast.error("An error occurred while uploading the file");
    } finally {
      setUploadState((prev) => ({
        ...prev,
        isUploading: false,
      }));
    }
  };

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this image?")) return;

    try {
      const res = await deleteCourseImage(courseId as string);
      if (res.success) {
        toast.success(res.message);
      } else {
        toast.error(res.error);
      }
    } catch (error) {
      console.error(error);
      toast.error("An error occurred while deleting the file");
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Course Cover Image</CardTitle>
      </CardHeader>
      <CardContent>
        {fileUrl ? (
          <div className="relative aspect-video rounded-lg overflow-hidden">
            <Image
              src={fileUrl}
              alt="Course Cover"
              fill
              className="object-cover"
            />

            <Button
              variant="outline"
              size="icon"
              className="absolute top-2 right-2"
              onClick={() => handleDelete()}
            >
              <X />
            </Button>
          </div>
        ) : (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <div className="space-y-2">
              <Upload className="w-8 h-8 mx-auto text-gray-400" />
              <p className="text-sm text-gray-600">Upload course cover image</p>
            </div>
          </div>
        )}
        {uploadState.isUploading && (
          <div className="space-y-2 mt-4">
            <div className="flex justify-between text-sm">
              <span>Uploading File...</span>
              <span>{uploadState.progress}%</span>
            </div>
            <Progress value={uploadState.progress} className="w-full" />
            <div className="text-xs text-muted-foreground">
              {formatBytes(uploadState.uploadedBytes)} /{" "}
              {formatBytes(uploadState.totalSize)}
            </div>
          </div>
        )}

        <form onSubmit={handleImageUpload} className="space-y-4 mt-4">
          <Input
            type="file"
            accept="image/*"
            onChange={(e) => handleChange(e)}
            className="cursor-pointer"
          />
          <Button type="submit">Upload</Button>
        </form>
      </CardContent>
    </Card>
  );
}
