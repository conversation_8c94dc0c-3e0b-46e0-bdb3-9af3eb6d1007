"use client";

import { getVideoProcessingStatus } from "@/lib/server/action/bunny/bunny.action";
import { cn, createIframeLink } from "@/lib/utils";
import { useEffect, useRef, useState } from "react";

export default function IframeVideoPlayer({
  videoId,
  className,
}: {
  videoId: string;
  className?: string;
}) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [state, setState] = useState({
    isLoaded: false,
    hasIncrementedView: false,
    isProcessing: true,
    processingProgress: 0,
  });

  useEffect(() => {
    const checkProcessingStatus = async () => {
      const status = await getVideoProcessingStatus(videoId);
      setState((prev) => ({
        ...prev,
        isProcessing: !status.isProcessed,
        processingProgress: status.encodingProgress,
      }));

      return status.isProcessed;
    };

    checkProcessingStatus();

    const intervalId = setInterval(async () => {
      const isProcessed = await checkProcessingStatus();
      if (isProcessed) {
        clearInterval(intervalId);
      }
    }, 3000);
    return () => {
      clearInterval(intervalId);
    };
  }, [videoId]);

  return (
    <div
      className={cn(
        "relative aspect-video w-full rounded-xl bg-[#000] flex-none",
        className
      )}
    >
      {state.isProcessing ? (
        <div className="size-full flex flex-col gap-2 justify-center items-center">
          <p className="text-white font-medium animate-pulse">
            Processing video...
          </p>
          <p className="text-white text-sm">
            {Math.round(state.processingProgress)}% complete
          </p>
        </div>
      ) : (
        <iframe
          ref={iframeRef}
          src={createIframeLink(videoId)}
          loading="lazy"
          title="Video player"
          style={{ border: 0, zIndex: 50 }}
          allow="accelerometer; gyroscope; encrypted-media; picture-in-picture"
          allowFullScreen
          onLoad={() => setState((prev) => ({ ...prev, isLoaded: true }))}
          className="w-full h-full"
        />
      )}
    </div>
  );
}
