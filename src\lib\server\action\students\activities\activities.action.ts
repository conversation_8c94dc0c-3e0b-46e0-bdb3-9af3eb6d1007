'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"

export async function createStudentActivity (activityId: string, studentId: string, response: string) {
  try {
    await prisma.activityResponse.create({
      data: {
        activityId,
        studentId,
        response
      }
    })

    revalidatePath(`/students/dashboard/activities`)
    
    return {  success: true, message: 'Response created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create response' }
  }
}

export async function updateStudentActivity (responseId: string, response: string) {
  try {
    await prisma.activityResponse.update({
      where: { id: responseId },
      data: { response }
    })

    revalidatePath(`/students/dashboard/activities`)
    
    return {  success: true, message: 'Response updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update response' }
  }
}

export async function assignScore (responseId: string, score: number ) {
  try {
    const activity = await prisma.activity.findFirst({
      where: { responses: { some: { id: responseId } } },
      select: { score: true }
    })

    if (!activity) return { success: false, error: 'Activity not found' }

    if (score > activity.score) return { success: false, error: `Score cannot be greater than activity pass score: ${activity.score}` }

    await prisma.activityResponse.update({
      where: { id: responseId },
      data: {
        score,
        status: 'GRADED',
        isGraded: true
      }
    })

    revalidatePath(`/dashboard/courses/activities/${responseId}`)
    
    return {  success: true, message: 'Score assigned' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to assign score' }
  }
}