import Image from "next/image";
import { ChangeEvent } from "react";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import { FileText, Upload, X } from "lucide-react";
import { Button } from "../ui/button";

interface FileInputProps {
  id: string;
  label: string;
  accept: string;
  file: File | null;
  previewUrl: string | null;
  inputRef: React.RefObject<HTMLInputElement | null>;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  onReset: () => void;
  type: "video" | "image" | "document";
}

export default function FileInput({
  id,
  label,
  accept,
  file,
  previewUrl,
  inputRef,
  onChange,
  onReset,
  type,
}: FileInputProps) {
  return (
    <section className="flex flex-col gap-2">
      <Label htmlFor={id}>{label}</Label>
      <Input
        type="file"
        id={id}
        accept={accept}
        hidden
        ref={inputRef}
        onChange={onChange}
      />

      {!file ? ( // Check for file presence instead of previewUrl
        <figure
          className="flex flex-col items-center justify-center gap-2 p-4 border-2 border-dashed rounded-md cursor-pointer"
          onClick={() => inputRef.current?.click()}
        >
          <Upload className="w-8 h-8 mx-auto text-gray-400" />
          <p>Click to upload your {label}</p>
        </figure>
      ) : (
        <div className="relative">
          {type === "video" ? (
            <video
              src={previewUrl as string}
              controls
              className="aspect-video"
            />
          ) : type === "image" ? (
            <Image src={previewUrl as string} alt={`Selected ${label}`} fill />
          ) : (
            // Handle document type
            <div className="w-full p-4 border-2 border-dashed rounded-md">
              <FileText className="w-8 h-8 mx-auto text-gray-400" />
              <p className="text-center text-sm font-medium text-primary">
                {file.name}
              </p>
            </div>
          )}
          <Button
            type="button"
            onClick={onReset}
            className="absolute top-2 right-2"
            variant="outline"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      )}
    </section>
  );
}
