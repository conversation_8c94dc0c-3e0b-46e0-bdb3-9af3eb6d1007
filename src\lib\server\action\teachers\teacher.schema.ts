import { z } from "zod";
import { UserStatus } from "@prisma/client";
import { TeacherWithUserAndSchool } from "./teachers.list";

export const updateTeacherSchema = z.object({
  firstName: z.string().min(1, { message: "Please provide your first name" }),
  lastName: z.string().min(1, { message: "Please provide your last name" }),
  email: z
    .string({ required_error: "Email field is required" })
    .email({ message: "Please provide a valid email" }),
  phone: z.string().min(1, { message: "Please provide your phone number" }),
  school: z.string().min(1, { message: "Please select your school" }),
  bio: z.string().min(1, { message: "Please provide your bio" }),
  specialization: z.string().min(1, { message: "Please provide your specialization" }),
  avatarUrl: z.string(),
});


export const teacherSchema = z.object({
  firstName: z.string().min(1, { message: "Please provide your first name" }),
  lastName: z.string().min(1, { message: "Please provide your last name" }),
  email: z
    .string({ required_error: "Email field is required" })
    .email({ message: "Please provide a valid email" }),
  phone: z.string().min(1, { message: "Please provide your phone number" }),
  school: z.string().min(1, { message: "Please select your school" }),
});

//Types
export type Teacher = TeacherWithUserAndSchool
export type TeacherKeys = keyof Teacher
export type TTeacherForm = z.infer<typeof teacherSchema>;
export type TUpdateTeacherForm = z.infer<typeof updateTeacherSchema> & {
  status?: UserStatus
};
export type TeacherTable = Teacher