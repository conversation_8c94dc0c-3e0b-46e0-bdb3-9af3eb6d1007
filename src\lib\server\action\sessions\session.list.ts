'use server'

import prisma from "@/lib/prisma"

export async function getSessions() {
  try {
    const sessions = await prisma.session.findMany({
      orderBy: { startDate: 'asc' }
    })
    
    return sessions
  } catch (error) {
    console.log(error)
    return []
  }
}

export async function getActiveSession() {
  try {
    const session = await prisma.session.findFirst({
      where: { isActive: true },
      select: {
        id: true
      }
    })
    
    return session
  } catch (error) {
    console.log(error)
    return null
  }
}