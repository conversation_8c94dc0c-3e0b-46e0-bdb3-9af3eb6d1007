import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import ModuleFilter from "../../filters/module-filter";
import ModuleOverviewCard from "../../cards/module/overview-card";
import DraggableModuleCard from "../../cards/module/draggable-module-card";
import { CustomSheet } from "@/components/shared/CustomSheet";
import ModuleForm from "../../forms/ModuleForm";
import {
  getModuleOverviewCounts,
  getModules,
} from "@/lib/server/action/courses/modules";
import { Suspense } from "react";
import Loading from "@/app/dashboard/[sessionId]/_components/Loading";

async function SuspendedComponent({
  courseId,
  sessionId,
  searchParams,
}: {
  courseId: string;
  sessionId: string;
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const { search } = await searchParams;
  const [modules, { totalModules, totalAttempts }] = await Promise.all([
    getModules({
      courseId,
      search: search || "",
    }),
    getModuleOverviewCounts(courseId),
  ]);

  return (
    <>
      <ModuleFilter />
      <ModuleOverviewCard
        totalModules={totalModules}
        totalAttempts={totalAttempts}
      />
      <DraggableModuleCard
        initialModules={modules}
        courseId={courseId}
        sessionId={sessionId}
      />

      {modules.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No module found.</p>
        </div>
      )}
    </>
  );
}

export function ModulesTab({
  courseId,
  sessionId,
  searchParams,
}: {
  courseId: string;
  sessionId: string;
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  return (
    <div className="space-y-6">
      <div className="flex lg:items-center justify-between flex-col lg:flex-row gap-3">
        <h2 className="text-2xl font-bold">Modules</h2>
        <CustomSheet
          title="Add Module"
          trigger={
            <Button size="sm">
              <Plus className="w-4 h-4" />
              Add Module
            </Button>
          }
        >
          <ModuleForm courseId={courseId} />
        </CustomSheet>
      </div>

      <Suspense fallback={<Loading />}>
        <SuspendedComponent
          courseId={courseId}
          sessionId={sessionId}
          searchParams={searchParams}
        />
      </Suspense>
    </div>
  );
}
