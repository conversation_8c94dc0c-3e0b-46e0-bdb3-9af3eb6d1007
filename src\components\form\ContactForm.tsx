// "use client";

// import React, { useState } from "react";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Textarea } from "@/components/ui/textarea";
// import { Label } from "@/components/ui/label";
// import { CheckCircle2 } from "lucide-react";
// import { sendContactEmail } from "@/lib/server/generalSettings/generalSettings.action";

// export default function ContactForm() {
//   const [isSubmitting, setIsSubmitting] = useState(false);
//   const [isSubmitted, setIsSubmitted] = useState(false);
//   const [error, setError] = useState<string | null>(null);

//   const [formData, setFormData] = useState({
//     firstName: "",
//     lastName: "",
//     email: "",
//     phone: "",
//     message: "",
//   });

//   const handleChange = (
//     e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
//   ) => {
//     const { id, value } = e.target;
//     setFormData((prev) => ({ ...prev, [id]: value }));
//   };

//   const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
//     e.preventDefault();
//     setIsSubmitting(true);
//     setError(null);

//     try {
//       // Simulate form submission
//       await sendContactEmail(formData);

//       setIsSubmitted(true);
//     } catch (err) {
//       console.error("Error sending email:", err);
//       setError("Failed to send your message. Please try again later.");
//     } finally {
//       setIsSubmitting(false);
//     }
//   };

//   if (isSubmitted) {
//     return (
//       <div className="flex flex-col items-center justify-center py-12 text-center">
//         <div className="mb-4 rounded-full bg-primary/10 p-3">
//           <CheckCircle2 className="h-6 w-6 text-primary" />
//         </div>
//         <h3 className="text-xl font-semibold">Message Sent!</h3>
//         <p className="mt-2 text-muted-foreground">
//           Thank you for reaching out. We&apos;ll get back to you as soon as
//           possible.
//         </p>
//         <Button
//           className="mt-6"
//           variant="outline"
//           onClick={() => {
//             setIsSubmitted(false);
//             setFormData({
//               firstName: "",
//               lastName: "",
//               email: "",
//               phone: "",
//               message: "",
//             });
//           }}
//         >
//           Send Another Message
//         </Button>
//       </div>
//     );
//   }

//   return (
//     <form onSubmit={handleSubmit} className="space-y-6">
//       <div className="grid gap-4 sm:grid-cols-2">
//         <div className="space-y-2">
//           <Label htmlFor="firstName">First name</Label>
//           <Input
//             id="firstName"
//             placeholder="John"
//             value={formData.firstName}
//             onChange={handleChange}
//             required
//           />
//         </div>
//         <div className="space-y-2">
//           <Label htmlFor="lastName">Last name</Label>
//           <Input
//             id="lastName"
//             placeholder="Doe"
//             value={formData.lastName}
//             onChange={handleChange}
//             required
//           />
//         </div>
//       </div>

//       <div className="space-y-2">
//         <Label htmlFor="email">Email</Label>
//         <Input
//           id="email"
//           type="email"
//           placeholder="<EMAIL>"
//           value={formData.email}
//           onChange={handleChange}
//           required
//         />
//       </div>

//       <div className="space-y-2">
//         <Label htmlFor="phone">Phone (optional)</Label>
//         <Input
//           id="phone"
//           type="tel"
//           placeholder="(*************"
//           value={formData.phone}
//           onChange={handleChange}
//         />
//       </div>

//       <div className="space-y-2">
//         <Label htmlFor="message">Message</Label>
//         <Textarea
//           id="message"
//           placeholder="How can we help you?"
//           className="min-h-[120px]"
//           value={formData.message}
//           onChange={handleChange}
//           required
//         />
//       </div>

//       {error && <p className="text-red-500 text-sm">{error}</p>}

//       <Button type="submit" className="w-full" disabled={isSubmitting}>
//         {isSubmitting ? "Sending..." : "Send Message"}
//       </Button>
//     </form>
//   );
// }
