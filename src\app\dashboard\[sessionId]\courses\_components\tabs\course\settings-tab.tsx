import CertificateTemplateSelector from "@/app/dashboard/[sessionId]/_components/shared/certificate-template-selector";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { getTemplates } from "@/lib/server/action/certificates/templates";
import { Suspense } from "react";

export default async function CourseSettingsTab({
  courseId,
  sessionId,
}: {
  courseId?: string;
  sessionId: string;
}) {
  const certificateTemplates = getTemplates(sessionId);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Course Settings</CardTitle>
        <Separator />
      </CardHeader>
      <CardContent>
        <Suspense fallback={<div>Loading...</div>}>
          <CertificateTemplateSelector
            courseId={courseId}
            templateData={await certificateTemplates}
          />
        </Suspense>
      </CardContent>
    </Card>
  );
}
