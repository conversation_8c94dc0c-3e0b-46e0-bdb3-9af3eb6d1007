"use client";

import { Button } from "@/components/ui/button";
import { updateCourseStatus } from "@/lib/server/action/courses";
import { Eye, Save } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

export default function CourseActionButtons({
  sessionId,
  courseId,
  courseStatus,
}: {
  sessionId: string;
  courseId: string;
  courseStatus: string;
}) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (status: "DRAFT" | "PUBLISHED") => {
    try {
      setIsSubmitting(true);
      const res = await updateCourseStatus(courseId, status);
      if (res.success) {
        toast.success(res.message);
        router.push(`/dashboard/${sessionId}/courses/${courseId}`);
      } else {
        toast.error(res.error);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex gap-2">
      <Button
        variant="outline"
        onClick={() => handleSubmit("DRAFT")}
        disabled={isSubmitting || courseStatus === "DRAFT"}
        className="cursor-pointer"
      >
        <Save className="w-4 h-4 mr-2" />
        {courseStatus === "DRAFT" ? "Saved as Draft" : "Save as Draft"}
      </Button>
      <Button
        onClick={() => handleSubmit("PUBLISHED")}
        disabled={isSubmitting || courseStatus === "PUBLISHED"}
        className="cursor-pointer"
      >
        <Eye className="w-4 h-4 mr-2" />
        {courseStatus === "PUBLISHED" ? "Published" : "Publish Course"}
      </Button>
    </div>
  );
}
