"use client";

import { useState, useMemo } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON><PERSON>,
  PlayCircle,
  PuzzleIcon as Quiz,
  Video,
  Lock,
} from "lucide-react";
import { QuizModal } from "../AssessmentModal";
import { StudentModule } from "@/lib/server/action/courses/modules";
import { cn } from "@/lib/utils";
import { trackLessonVideoProgress } from "@/lib/server/action/students/modules/lesson/lesson.action";
import { toast } from "sonner";
import { startAssessment } from "@/lib/server/action/students/modules/assessments";
import { trackModuleProgress } from "@/lib/server/action/students/progress-tracking/progress-tracking.action";
import NextPlayer from "../NextPlayer";

interface ModulesGridProps {
  studentId: string;
  modules: StudentModule[];
}

interface ProcessedModules extends StudentModule {
  isLocked: boolean;
}

export function ModulesGrid({ studentId, modules }: ModulesGridProps) {
  const [selectedModule, setSelectedModule] = useState<StudentModule | null>(
    null
  );
  const [showQuiz, setShowQuiz] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const [assessment, setAssessment] = useState<{
    attemptId: string;
    questionOrder: number[];
  } | null>(null);

  // Process modules to determine which ones should be locked
  const processedModules = useMemo(() => {
    return modules.map((module, index) => {
      // First module is always unlocked
      if (index === 0) {
        return { ...module, isLocked: false };
      }

      // Check if previous module is completed
      const prevModule = modules[index - 1];
      // Safely check for completion
      const isPrevModuleCompleted =
        (prevModule?.progress ?? 0) >= 100 ||
        prevModule?.completedAt !== null ||
        prevModule?.isCompleted;

      return {
        ...module,
        isLocked: !isPrevModuleCompleted,
      };
    });
  }, [modules]);

  const handleStartQuiz = async (module: ProcessedModules) => {
    if (!module.assessment || module.isLocked) return;
    setSelectedModule(module);
    setShowQuiz(true);

    const result = await startAssessment({
      assessmentId: module.assessment.id,
      studentId,
    });
    if (result.success) {
      setAssessment({
        attemptId: result.attempt!.id,
        questionOrder: JSON.parse(result.attempt!.questionOrder),
      });
      toast.success(result.message);
    } else {
      toast.error(result.error);
    }
  };

  const handleStartVideo = async (module: ProcessedModules) => {
    if (!module.lesson || !module.lesson.video || module.isLocked) return;
    setSelectedModule(module);
    await trackLessonVideoProgress({
      lessonId: module.lesson.id,
      studentId,
      videoDuration: module.lesson.video.duration,
    });

    setShowVideo(true);
  };

  const handleProceed = async () => {
    setShowVideo(false);
    if (
      !selectedModule ||
      !selectedModule.lesson ||
      !selectedModule.lesson.video
    )
      return;

    await trackLessonVideoProgress({
      lessonId: selectedModule.lesson.id,
      studentId,
      videoDuration: selectedModule.lesson.video.duration,
      progress: 100,
      isCompleted: true,
    });

    if (
      selectedModule.assessment &&
      selectedModule.assessment.questionCount > 0
    ) {
      return;
    } else {
      await trackModuleProgress(studentId, selectedModule.id, 100, true);
    }

    setShowVideo(false);
  };

  const handleViewDoc = async (module: ProcessedModules) => {
    if (!module.lesson || !module.lesson.fileUrl || module.isLocked) return;

    setSelectedModule(module);
    window.open(module.lesson.fileUrl!, "_blank");

    if (module.assessment && module.assessment.questionCount > 0) {
      return;
    } else {
      await trackModuleProgress(studentId, module.id, 100, true);
    }
  };

  if (!modules || modules.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">No module found.</p>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {processedModules.map((module) => {
          const isCompletedAssessment =
            module.assessment?.bestAttempt?.status === "COMPLETED";
          const isPassesAssessment = module.assessment?.bestAttempt?.passed;
          const assessmentScore = module.assessment?.bestAttempt?.score || 0;
          const isCompletedLessonVideo =
            module.lesson?.video?.progress?.isCompleted;

          return (
            <Card
              key={module.id}
              className={cn(
                "gap-0 relative h-fit",
                isCompletedAssessment
                  ? isPassesAssessment
                    ? "border-green-500"
                    : "border-red-500"
                  : ""
              )}
            >
              {isCompletedAssessment ? (
                <Badge
                  className={cn(
                    "absolute top-3 right-3",
                    isPassesAssessment
                      ? "bg-green-200 text-green-500"
                      : "bg-red-200 text-red-500"
                  )}
                  variant={module.isLocked ? "secondary" : "default"}
                >
                  {isPassesAssessment ? "Passed" : "Failed"}
                </Badge>
              ) : null}

              {module.isLocked && (
                <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-xl cursor-not-allowed">
                  <Lock className="h-10 w-10 text-white" />
                </div>
              )}

              <CardHeader>
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg line-clamp-2">
                    {module.title}
                  </CardTitle>
                </div>
              </CardHeader>

              <CardContent className="space-y-4 mt-auto">
                <p className="text-gray-600 text-sm line-clamp-3">
                  {module.description}
                </p>

                {isCompletedAssessment && (
                  <div>
                    <p className="text-xs font-medium text-muted-foreground">
                      Score: {Math.round(assessmentScore)}%
                    </p>
                    <p className="text-xs font-medium text-muted-foreground">
                      Passed: {isPassesAssessment ? "Yes" : "No"}
                    </p>
                  </div>
                )}

                <div className="flex items-center gap-4 text-sm text-gray-500">
                  {module.lesson?.type === "VIDEO" && module.lesson.video ? (
                    <div className="flex items-center gap-1 text-xs">
                      <Video className="h-4 w-4" />
                      {new Date(module.lesson.video.duration * 1000)
                        .toISOString()
                        .slice(14, 19)}
                    </div>
                  ) : null}
                  <div className="flex items-center gap-1 text-xs">
                    <BookOpen className="h-4 w-4" />
                    {module.assessment?.maxStudentQuestions || 0} questions
                  </div>
                </div>

                <div className="flex flex-col gap-2">
                  {module.lesson?.type === "VIDEO" && module.lesson.video && (
                    <Button
                      className="w-full cursor-pointer"
                      variant={
                        module.lesson.video.progress?.isCompleted
                          ? "secondary"
                          : "default"
                      }
                      onClick={() => handleStartVideo(module)}
                      disabled={module.isLocked}
                    >
                      <PlayCircle className="h-4 w-4 mr-2" />
                      {module.lesson.video.progress
                        ? module.lesson.video.progress.isCompleted
                          ? "Replay Module"
                          : "Continue"
                        : "View Module"}
                    </Button>
                  )}

                  {module.lesson?.type === "DOC" && (
                    <Button
                      className="w-full cursor-pointer"
                      variant="default"
                      onClick={() => handleViewDoc(module)}
                      disabled={module.isLocked}
                    >
                      <BookOpen className="h-4 w-4 mr-2" />
                      View Module
                    </Button>
                  )}

                  {module.assessment && module.assessment.questionCount > 0 && (
                    <Button
                      variant="outline"
                      className="w-full cursor-pointer"
                      onClick={() => handleStartQuiz(module)}
                      disabled={
                        (module.lesson?.type === "VIDEO" &&
                          !isCompletedLessonVideo) ||
                        module.isLocked
                      }
                    >
                      <Quiz className="h-4 w-4 mr-2" />
                      {module.assessment.bestAttempt &&
                      module.assessment.bestAttempt.status === "COMPLETED"
                        ? "Retake Assessment"
                        : "Take Assessment"}
                      {((module.lesson?.type === "VIDEO" &&
                        !isCompletedLessonVideo) ||
                        module.isLocked) && (
                        <Badge variant="secondary" className="ml-2">
                          Locked
                        </Badge>
                      )}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {showQuiz && selectedModule && selectedModule.assessment && (
        <QuizModal
          assessmentId={selectedModule.assessment.id}
          attemptId={assessment?.attemptId || ""}
          moduleId={selectedModule.id}
          passingScore={selectedModule.assessment.passingScore}
          maxStudentQuestions={selectedModule.assessment.maxStudentQuestions}
          instructions={selectedModule.assessment.instructions}
          isOpen={showQuiz}
          title={selectedModule.title}
          questionOrder={assessment?.questionOrder || []}
          onClose={() => {
            setShowQuiz(false);
            setSelectedModule(null);
          }}
        />
      )}

      {showVideo &&
        selectedModule?.lesson?.type === "VIDEO" &&
        selectedModule.lesson.fileId && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/90">
            <div className="w-full max-w-4xl bg-gray-200 rounded-lg overflow-hidden">
              <NextPlayer
                lessonId={selectedModule.lesson.id}
                studentId={studentId}
                videoId={selectedModule.lesson.fileId}
                videoUrl={selectedModule.lesson.video?.videoUrl as string}
                thumbnailUrl={
                  selectedModule.lesson.video?.thumbnailUrl as string
                }
                title={selectedModule.lesson.title}
                duration={selectedModule.lesson.video?.duration || 0}
                onProceed={handleProceed}
                onCancel={() => setShowVideo(false)}
                hasWatched={selectedModule.lesson.video?.progress?.isCompleted}
                controls={selectedModule.lesson.video?.progress?.isCompleted}
              />
            </div>
          </div>
        )}
    </div>
  );
}
