"use client";

import { But<PERSON> } from "@/components/ui/button";
import { getVideoProcessingStatus } from "@/lib/server/action/bunny/bunny.action";
import { cn, createIframeLink } from "@/lib/utils";
import { Loader, Loader2 } from "lucide-react";
import { useEffect, useRef, useState } from "react";

export default function IframeVideoPlayer({
  videoId,
  className,
  title,
  onProceed,
  onCancel,
  hasWatched,
}: {
  videoId: string;
  className?: string;
  title?: string;
  onProceed?: () => void;
  onCancel?: () => void;
  hasWatched?: boolean;
}) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [state, setState] = useState({
    isLoaded: false,
    hasIncrementedView: false,
    isProcessing: true,
    processingProgress: 0,
    ended: false,
  });

  useEffect(() => {
    const checkProcessingStatus = async () => {
      const status = await getVideoProcessingStatus(videoId);
      setState((prev) => ({
        ...prev,
        isProcessing: !status.isProcessed,
      }));

      return status.isProcessed;
    };

    checkProcessingStatus();

    const intervalId = setInterval(async () => {
      const isProcessed = await checkProcessingStatus();
      if (isProcessed) {
        clearInterval(intervalId);
      }
    }, 3000);
    return () => {
      clearInterval(intervalId);
    };
  }, [videoId]);

  return (
    <div className={cn("relative w-full rounded-xl flex-none", className)}>
      <div className="aspect-video flex-none bg-black">
        {state.isProcessing ? (
          <div className="size-full flex justify-center items-center">
            <Loader2 className="h-8 w-8 animate-spin text-white" />
          </div>
        ) : (
          <iframe
            ref={iframeRef}
            src={createIframeLink(videoId)}
            loading="lazy"
            title="Video player"
            style={{ border: 0, zIndex: 50 }}
            allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture"
            allowFullScreen
            onLoad={() => setState((prev) => ({ ...prev, isLoaded: true }))}
            onEnded={() => setState((prev) => ({ ...prev, ended: true }))}
            className="w-full h-full"
          />
        )}
      </div>

      <div className="flex w-full items-center justify-between gap-x-4 p-4">
        <p className="text-sm text-gray-600">{title}</p>
        <div className="flex gap-x-4">
          <Button
            size="sm"
            variant="outline"
            className="border-primary/80"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            onClick={onProceed}
            disabled={!hasWatched || state.isProcessing}
            className="disabled:bg-gray-400 disabled:text-gray-200 disabled:cursor-not-allowed bg-primary text-white"
            size="sm"
          >
            {state.isProcessing ? (
              <span className="flex items-center gap-2">
                <Loader className="h-4 w-4 animate-spin" />
                Processing...
              </span>
            ) : hasWatched ? (
              "Skip"
            ) : (
              "Proceed"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
