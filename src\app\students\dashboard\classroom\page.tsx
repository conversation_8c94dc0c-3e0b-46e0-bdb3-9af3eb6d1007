import { getMeetings } from "@/lib/server/action/courses/virtual-classrooms";
import { auth } from "@/lib/server/auth";
import { Suspense } from "react";
import MeetingsSection from "../_components/sections/meetings";

async function SuspendedComponent() {
  const session = await auth();
  if (!session || !session.user) {
    return null;
  }

  const meetings = await getMeetings(session.user.courseId as string);

  if (meetings.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">No meeting found.</p>
      </div>
    );
  }

  return (
    <>
      <MeetingsSection meetings={meetings} userId={session.user.id} />
    </>
  );
}

export default function VirtualClassroom() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Virtual Classroom</h1>
        {/* <div className="relative w-64">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input placeholder="Search" className="pl-10" />
        </div> */}
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <SuspendedComponent />
      </Suspense>
    </div>
  );
}
