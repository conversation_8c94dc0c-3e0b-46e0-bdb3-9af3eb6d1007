
import { z } from "zod";

export const templateSchema = z.object({
  name: z.string().min(1, { message: "Please provide the template name" }),
});

export type TTemplate = z.infer<typeof templateSchema>;
export type TTemplateForm = TTemplate;

export type TTemplateFile = TTemplate & {
  name: string;
  fileUrl: string;
  fileId: string;
  previewId: string;
  previewUrl: string;
  fields: { name: string; x: number; y: number; position: "left" | "center" | "right" }[];
};
