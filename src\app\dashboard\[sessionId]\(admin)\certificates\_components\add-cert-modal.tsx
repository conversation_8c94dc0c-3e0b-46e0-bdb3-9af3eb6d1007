"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { appConfig } from "@/config/app";
import { uploadFileToBunny } from "@/lib/bunny";
import { validateImageFile } from "@/lib/file-validators";
import { generateFileUploadUrl } from "@/lib/server/action/bunny/bunny.action";
import { createTemplate } from "@/lib/server/action/certificates/templates";
import { formatBytes } from "@/lib/utils";
import { Loader, Upload } from "lucide-react";
import Image from "next/image";
import { useParams } from "next/navigation";
import { ChangeEvent, useState } from "react";
import { toast } from "sonner";

export default function AddCertModal() {
  // get session id from url
  const params = useParams<{ sessionId: string }>();
  if (!params.sessionId) throw new Error("Session ID is required");

  const [isUploading, setIsUploading] = useState(false);
  const [preview, setPreview] = useState<File | null>(null);
  const [uploadState, setUploadState] = useState({
    progress: 0,
    isUploading: false,
    uploadedBytes: 0,
    totalSize: 0,
  });

  const updateUploadProgress = (
    percentage: number,
    loaded: number,
    total: number
  ) => {
    setUploadState({
      progress: Math.round(percentage),
      isUploading: true,
      uploadedBytes: loaded,
      totalSize: total,
    });
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setPreview(selectedFile);
      processFile(selectedFile);
    }
  };

  const processFile = (selectedFile: File) => {
    // Validate file
    const validation = validateImageFile(
      selectedFile,
      appConfig.MAX_IMAGE_SIZE_MB
    );
    if (!validation.valid) {
      toast.error(validation.error || "Invalid file");
      return;
    }
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!preview) return;

    try {
      setIsUploading(true);
      const { fileId, uploadUrl, accessKey, cdnUrl } =
        await generateFileUploadUrl(preview.name);

      await uploadFileToBunny(
        preview,
        uploadUrl,
        accessKey,
        updateUploadProgress
      );

      const res = await createTemplate({
        fileId,
        fileUrl: cdnUrl,
        previewId: "",
        previewUrl: "",
        name: preview?.name || "",
        fields: [],
      });
      if (res.success) {
        toast.success(res.message);
        setPreview(null);
      } else {
        toast.error(res.error);
      }
    } catch (error) {
      console.error(error);
      toast.error("An error occurred while uploading the file");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          {preview ? (
            <div className="space-y-2 flex flex-col justify-center items-center">
              {/* <ImageIcon className="w-8 h-8 mx-auto text-green-600" /> */}
              <Image
                src={URL.createObjectURL(preview)}
                alt="Preview"
                width={200}
                height={200}
              />
              {/* <p className="text-sm text-green-600">{preview.name}</p> */}
            </div>
          ) : (
            <div className="space-y-2">
              <Upload className="w-8 h-8 mx-auto text-gray-400" />
              <p className="text-sm text-gray-600">
                Upload certificate template
              </p>
            </div>
          )}
        </div>
        <Input
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="cursor-pointer"
          required
        />
        {isUploading && (
          <div className="space-y-2 mt-4">
            <div className="flex justify-between text-sm">
              <span>Uploading File...</span>
              <span>{uploadState.progress}%</span>
            </div>
            <Progress value={uploadState.progress} className="w-full" />
            <div className="text-xs text-muted-foreground">
              {formatBytes(uploadState.uploadedBytes)} /{" "}
              {formatBytes(uploadState.totalSize)}
            </div>
          </div>
        )}
      </div>
      <Button type="submit" disabled={isUploading}>
        {isUploading ? (
          <>
            <Loader /> Uploading
          </>
        ) : (
          "Upload"
        )}
      </Button>
    </form>
  );
}
