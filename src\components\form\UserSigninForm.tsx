// "use client";

// import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
// import { useForm } from "react-hook-form";
// import {
//   Form,
//   FormControl,
//   FormField,
//   FormItem,
//   FormLabel,
//   FormMessage,
// } from "@/components/ui/form";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Loader } from "lucide-react";
// import { toast } from "sonner";
// import { useRouter } from "next/navigation";
// import { TUserLoginForm, userLoginSchema } from "@/lib/server/user/user.schema";
// import Link from "next/link";
// import { loginUser } from "@/lib/server/user/user.action";

// const UserSigninForm = () => {
//   const router = useRouter();

//   const form = useForm<TUserLoginForm>({
//     resolver: zodResolver(userLoginSchema),
//     defaultValues: {
//       loginCode: "",
//     },
//     mode: "onChange",
//   });

//   const onSubmit = async (values: TUserLoginForm) => {
//     try {
//       const res = await loginUser(values);
//       if (res.success) {
//         router.push("/user");
//         toast.success("Successfully signed in");
//         router.refresh();
//       } else {
//         toast.error(res.message);
//       }
//     } catch (error) {
//       console.error(error);
//       toast.error("An error occurred during sign in");
//     }
//   };

//   return (
//     <Form {...form}>
//       <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
//         <div className="flex flex-col gap-4 mt-5">
//           <FormField
//             control={form.control}
//             name="loginCode"
//             render={({ field }) => (
//               <FormItem>
//                 <FormLabel>Passcode</FormLabel>
//                 <FormControl>
//                   <Input {...field} type="password" />
//                 </FormControl>
//                 <FormMessage />
//               </FormItem>
//             )}
//           />
//         </div>
//         {/* <div className="flex w-full justify-end mt-3">
//           <Link
//             href="/auth/user/expired-code"
//             className="text-primary hover:underline text-sm font-medium"
//           >
//             Expired code?
//           </Link>
//         </div> */}
//         <div className="flex justify-center items-center mt-6">
//           <Button
//             type="submit"
//             disabled={form.formState.isSubmitting}
//             size="sm"
//             className="button w-full sm:w-fit sm:px-32 disabled:bg-primary/70"
//           >
//             {form.formState.isSubmitting ? (
//               <>
//                 <Loader /> Signing In
//               </>
//             ) : (
//               <>Sign In</>
//             )}
//           </Button>
//         </div>
//       </form>
//       <div className="flex w-full justify-center items-center mt-4">
//         <p className="text-sm font-medium text-muted-foreground">
//           Don&apos;t have an account?{" "}
//           <Button variant="link" className="mx-0 px-1" asChild>
//             <Link href="/onboarding">Sign up</Link>
//           </Button>
//         </p>
//       </div>
//     </Form>
//   );
// };

// export default UserSigninForm;
