'use server'

import { auth } from "../../../../../../auth";
import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { zoomService } from "@/lib/zoom";
import { MeetingFormData } from "./virtual-classrooms.schema";

export async function createMeeting(courseId: string, data: MeetingFormData) {
  const session = await auth()

  if (!session) {
    return { success: false, error: 'Unauthorized' }
  }

  try {
    const { title, description, scheduledAt, duration, maxParticipants, waitingRoomEnabled, zoomEmail } = data;

    // Create Zoom meeting
    const zoomConfig = {
      topic: title,
      type: 2, // Scheduled meeting
      start_time: new Date(scheduledAt!).toISOString(),
      duration,
      timezone: 'UTC',
      waiting_room: waitingRoomEnabled,
      settings: {
        host_video: true,
        participant_video: true,
        cn_meeting: false,
        in_meeting: false,
        join_before_host: false,
        mute_upon_entry: true,
        watermark: false,
        use_pmi: false,
        approval_type: waitingRoomEnabled ? 1 : 0,
        audio: 'both',
        auto_recording: 'none'
      }
    };

    const zoomMeeting = await zoomService.createMeeting(zoomConfig);

    await prisma.meeting.create({
      data: {
        title,
        description,
        scheduledAt: new Date(scheduledAt!),
        endedAt: new Date(new Date(scheduledAt!).getTime() + duration * 60000),
        duration,
        maxParticipants,
        waitingRoomEnabled,
        zoomEmail,
        zoomMeetingId: zoomMeeting.id.toString(),
        zoomJoinUrl: zoomMeeting.join_url,
        zoomStartUrl: zoomMeeting.start_url,
        zoomPassword: zoomMeeting.password,
        creatorId: session.user.id,
        courseId,
      },
      include: {
        creator: true,
        participants: {
          include: { user: true }
        }
      }
    });

    revalidatePath(`/dashboard/courses/${courseId}`)

    return { success: true, message: 'Meeting created' }
  } catch (error) {
    console.error('Error creating meeting:', error)
    return { success: false, error: 'Failed to create meeting' }
  }
}

export async function updateMeeting(meetingId: string, data: MeetingFormData) {
  const session = await auth()

  if (!session) {
    return { success: false, error: 'Unauthorized' }
  }

  try {

    const meeting = await prisma.meeting.findUnique({
      where: { id: meetingId },
      select: {
        courseId: true,
      }
    });
    if (!meeting) {
      return { success: false, error: 'Meeting not found' };
    }

    const { title, description, scheduledAt, duration, maxParticipants, waitingRoomEnabled } = data;

    // Update Zoom meeting
    const zoomConfig = {
      topic: title,
      type: 2, // Scheduled meeting
      start_time: new Date(scheduledAt!).toISOString(),
      duration,
      timezone: 'UTC',
      waiting_room: waitingRoomEnabled,
      settings: {
        host_video: true,
        participant_video: true,
        cn_meeting: false,
        in_meeting: false,
        join_before_host: false,
        mute_upon_entry: true,
        watermark: false,
        use_pmi: false,
        approval_type: waitingRoomEnabled ? 1 : 0,
        audio: 'both',
        auto_recording: 'none'
      }
    };

    const zoomMeeting = await zoomService.createMeeting(zoomConfig);

    await prisma.meeting.update({
      where: { id: meetingId },
      data: {
        title,
        description,
        scheduledAt: new Date(scheduledAt!),
        endedAt: new Date(new Date(scheduledAt!).getTime() + duration * 60000),
        duration,
        maxParticipants,
        waitingRoomEnabled,
        zoomMeetingId: zoomMeeting.id.toString(),
        zoomJoinUrl: zoomMeeting.join_url,
        zoomStartUrl: zoomMeeting.start_url,
        zoomPassword: zoomMeeting.password
      },
      include: {
        creator: true,
        participants: {
          include: { user: true }
        }
      }
    });

    revalidatePath(`/dashboard/courses/${meeting.courseId}`)

    return { success: true, message: 'Meeting updated' }
  } catch (error) {
    console.error('Error updating meeting:', error)
    return { success: false, error: 'Failed to update meeting' }
  }
}

export async function startMeeting(meetingId: string) {
  const session = await auth()

  if (!session) {
    return { success: false, error: 'Unauthorized' }
  }

  try {
    const meeting = await prisma.meeting.findUnique({
      where: { id: meetingId },
      select: {
        id: true,
        zoomMeetingId: true,
        zoomStartUrl: true,
        zoomPassword: true,
        creator: {
          select: {
            id: true
          }
        }
      }
    });

    if (!meeting) {
      return { success: false, error: 'Meeting not found' };
    }

    // Check if user is a creator
    if (meeting.creator.id !== session.user.id) {
      return { success: false, error: 'Only the creator can start the meeting' };
    }

    // Start Zoom meeting
    // await zoomService.startMeeting(meeting.zoomMeetingId);

    await prisma.meetingParticipant.updateMany({
      where: {
        meetingId,
        userId: session.user.id
      },
      data: {
        status: 'JOINED',
        joinedAt: new Date()
      }
    });

    revalidatePath(`/classroom/${meetingId}`);

    return { 
      success: true,
      startUrl: meeting.zoomStartUrl,
      password: meeting.zoomPassword,
    }
  } catch (error) {
    console.error('Error starting meeting:', error);
    return { success: false, error: 'Failed to start meeting' };
  }
}

export async function joinMeeting(meetingId: string) {
  const session = await auth()

  if (!session) {
    return { success: false, error: 'Unauthorized' }
  }

  try {
    const meeting = await prisma.meeting.findUnique({
      where: { id: meetingId },
      include: {
        participants: true,
      }
    });

    if (!meeting) {
      return { success: false, error: 'Meeting not found' };
    }

    // Check if user is already a participant
    const existingParticipant = meeting.participants.find(
      p => p.userId === session.user.id
    );

    if (!existingParticipant) {
      // Add user as participant
      await prisma.meetingParticipant.create({
        data: {
          meetingId,
          userId: session.user.id,
          role: 'PARTICIPANT'
        }
      });
    }

    // Update participant status
    await prisma.meetingParticipant.updateMany({
      where: {
        meetingId,
        userId: session.user.id
      },
      data: {
        status: 'JOINED',
        joinedAt: new Date()
      }
    });

    revalidatePath(`/classroom/${meetingId}`);

    return { 
      success: true,
      joinUrl: meeting.zoomJoinUrl,
      password: meeting.zoomPassword,
    }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to join meeting' }
  }
}

export async function deleteMeeting (meetingId: string) {
  try {
    const meeting = await prisma.meeting.findUnique({
      where: { id: meetingId },
      include: { course: {
        select: {
          id: true
        }
      } }
    })

    if (!meeting) {
      return { success: false, error: 'Meeting not found' }
    }

    // Delete meeting from database
    await prisma.meeting.delete({
      where: { id: meeting.id }
    })

    revalidatePath(`/dashboard/courses/${meeting.course.id}`)

    return { success: true, message: 'Meeting deleted' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete meeting' }
  }
}

export async function endMeeting (meetingId: string) {
  try {
    const meeting = await prisma.meeting.findUnique({
      where: { id: meetingId },
      select: {
        zoomMeetingId: true
      }
    });

    if (!meeting) {
      return { success: false, error: 'Meeting not found' };
    }

    await prisma.meeting.update({
      where: { id: meetingId },
      data: {
        status: 'ENDED',
        endedAt: new Date()
      }
    });

    revalidatePath(`/classroom/${meetingId}`);

    return { success: true, message: 'Meeting ended' };
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to end meeting' };
  }
}

export async function joinMeetingWithZoomId (zoomId: string) {
  try {
    const meeting = await prisma.meeting.findUnique({
      where: { zoomMeetingId: zoomId },
      select: {
        id: true,
        zoomJoinUrl: true,
        zoomPassword: true,
      }
    });

    if (!meeting) {
      return { success: false, error: 'Meeting not found' };
    }

    await joinMeeting(meeting.id)

    return { 
      success: true,
      joinUrl: meeting.zoomJoinUrl,
      password: meeting.zoomPassword,
    }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to join meeting' }
  }
}