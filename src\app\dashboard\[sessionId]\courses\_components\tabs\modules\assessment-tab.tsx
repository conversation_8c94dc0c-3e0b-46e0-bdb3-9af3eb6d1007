import { But<PERSON> } from "@/components/ui/button";
import { CloudUpload } from "lucide-react";
import AssessmentSettingsCard from "../../cards/module/assessment-settings-card";
import CustomDialog from "@/components/shared/CustomDialog";
import { ExcelImportDialog } from "../../shared/excel-import";
import QuestionSection from "../../sections/question-section";
import { getAssessment } from "@/lib/server/action/courses/modules/assessments";
import DeleteAssessment from "../../DeleteAssessment";
import {
  getQuestions,
  Question,
} from "@/lib/server/action/courses/modules/assessments/questions";
import ExportQuestions from "../../ExportQuestions";
import { getSiteData } from "@/lib/server/action/frontend/frontend.action";

export default async function AssessmentPage({
  moduleId,
}: {
  moduleId: string;
}) {
  const assessment = await getAssessment(moduleId);
  const siteData = await getSiteData();

  const questions = (await getQuestions(
    assessment?.id as string
  )) as Question[];

  return (
    <>
      <div className="">
        <div className="flex flex-col lg:flex-row justify-between lg:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Assessment Management
            </h1>
            <p className="text-gray-600">
              Create and manage your assessment questions
            </p>
          </div>

          {assessment ? (
            <div className="lg:ml-auto space-x-4">
              <ExcelImportDialog assessmentId={assessment.id} />
              <CustomDialog
                title="Export"
                trigger={
                  <Button size="sm">
                    <CloudUpload /> Export
                  </Button>
                }
              >
                <ExportQuestions
                  moduleTitle={assessment.module.title}
                  questions={questions}
                  siteName={siteData ? siteData.siteName : ""}
                />
              </CustomDialog>

              <DeleteAssessment assessmentId={assessment.id} />
            </div>
          ) : null}
        </div>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Quiz Settings */}
          <AssessmentSettingsCard moduleId={moduleId} assessment={assessment} />

          {/* Questions */}
          <div className="lg:col-span-2">
            <QuestionSection
              questions={questions}
              assessmentId={assessment?.id}
              maxQuestionCount={assessment?.maxStudentQuestions}
            />
          </div>
        </div>
      </div>
    </>
  );
}
