import { z } from "zod";

export const lessonSchema = z.object({
  title: z.string().min(1, { message: "Please provide the lesson title" }),
  type: z.enum(["VIDEO", "DOC"]),
  description: z.string().min(1, { message: "Please provide the lesson description" }),
  duration: z.optional(z.coerce.number()),
  mimeType: z.string().optional(),
  originalName: z.string().optional(),
  fileSize: z.optional(z.coerce.number()),
});

export type TLesson = z.infer<typeof lessonSchema>;
export type TLessonForm = TLesson;
export type LessonAction = TLesson & { fileId: string, fileUrl: string }