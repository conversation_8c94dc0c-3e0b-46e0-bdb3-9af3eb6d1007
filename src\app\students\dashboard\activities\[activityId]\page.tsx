import { getStudentActivity } from "@/lib/server/action/students/activities/activities.list";
import { notFound } from "next/navigation";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft, Calendar } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import ActivitiesDetail from "./_components/Activity";
import { auth } from "../../../../../../auth";

export default async function ActivitiesDetailPage({
  params,
  searchParams,
}: {
  params: Promise<{ activityId: string }>;
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const { activityId } = await params;
  const session = await auth();
  const { reviewed } = await searchParams;

  const activity = await getStudentActivity(
    activityId as string,
    session?.user.profileId as string
  );

  if (!activity) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      <Button variant="ghost" asChild>
        <Link href="/students/dashboard/activities">
          <ArrowLeft className="w-4 h-4" />
          Back to activities
        </Link>
      </Button>

      <Card className="py-0">
        <CardContent className="p-6">
          <div className="space-y-6">
            <div className="flex-1">
              <p className="text-lg mb-4">{activity.question}</p>

              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                {activity.response.status === "SUBMITTED" && (
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>
                      Submitted on:{" "}
                      {activity.response.submittedAt.toDateString()}
                    </span>
                  </div>
                )}
                {activity.response.score ? (
                  <div className="flex items-center gap-2">
                    <span>
                      Score:{" "}
                      <span className="text-green-600 font-semibold">
                        {activity.response.score}
                      </span>
                    </span>
                  </div>
                ) : null}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>My answer</CardTitle>
        </CardHeader>
        <CardContent>
          <ActivitiesDetail
            response={activity.response}
            reviewed={reviewed === "true"}
            activityId={activityId}
            studentId={session?.user.profileId as string}
          />
        </CardContent>
      </Card>
    </div>
  );
}
