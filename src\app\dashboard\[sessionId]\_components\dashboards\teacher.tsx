import Statistics from "./sections/teacher/statistics";
import UpcomingSchedules from "./sections/teacher/upcoming-schedules";
import CourseProgressReport from "./sections/teacher/course-progress-report";
import RecentlyCompleted from "./sections/teacher/recenty-completed";
import { auth } from "@/lib/server/auth";
import { getTeachersDashboardData } from "@/lib/server/action/teachers";

export default async function TeacherDashboard() {
  const session = await auth();
  if (!session || !session.user) {
    return null;
  }

  const dashboardData = await getTeachersDashboardData(session.user.profileId);

  return (
    <>
      {/* Header */}
      <header className="border-b pb-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              Hello Teacher {session.user.firstName || "User"}
              <span className="text-2xl">👋</span>
            </h1>
            <p className="text-gray-600 mt-1">
              {"Let's learn something today"}
            </p>
          </div>
        </div>
      </header>

      <div className="py-6">
        {/* Statistics Cards */}
        <Statistics count={dashboardData?.count} />

        {/* Upcoming Schedules */}
        <UpcomingSchedules
          upcomingSchedules={dashboardData?.upcomingMeetings}
        />

        {/* Course Progress Report */}
        <CourseProgressReport courseProgress={dashboardData?.courseProgress} />

        {/* Recently Completed */}
        <RecentlyCompleted
          recentlyCompleted={dashboardData?.recentlyCompletedCourse}
        />
      </div>
    </>
  );
}
