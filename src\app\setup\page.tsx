import React from "react";
import InitializationWizard from "./_components/setup-wizard";
import { checkActiveUser } from "@/lib/server/action/users/user.action";
import { redirect } from "next/navigation";

export const dynamic = "force-dynamic";

export default async function SetupPage() {
  const user = await checkActiveUser();
  if (user) {
    redirect("/");
  }

  return (
    <>
      <InitializationWizard />
    </>
  );
}
