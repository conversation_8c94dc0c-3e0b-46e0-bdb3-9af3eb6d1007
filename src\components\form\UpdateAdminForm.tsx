"use client";

import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "../ui/button";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import {
  TUpdateAdminForm,
  updateAdminSchema,
} from "@/lib/server/action/admins/admins.schema";
import { updateAdmin } from "@/lib/server/action/admins";
import { FormInputField } from "../form-element/input-field";
import Link from "next/link";

const UpdateAdminForm = ({
  adminData,
  adminId,
}: {
  adminData: TUpdateAdminForm;
  adminId: string;
}) => {
  const form = useForm<TUpdateAdminForm>({
    resolver: zod<PERSON><PERSON><PERSON>ver(updateAdminSchema),
    defaultValues: adminData,
    mode: "onChange",
  });

  const onSubmit = async (values: TUpdateAdminForm) => {
    const res = await updateAdmin(adminId, values);
    if (res.success) {
      toast.success("Admin updated");
    } else {
      toast.error("Failed to update admin");
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid gap-6 md:grid-cols-2">
          <FormInputField
            control={form.control}
            name="firstName"
            label="First Name"
            placeholder="John"
          />
          <FormInputField
            control={form.control}
            name="lastName"
            label="Last Name"
            placeholder="Doe"
          />
          <FormInputField
            control={form.control}
            name="email"
            label="Email"
            type="email"
            placeholder="<EMAIL>"
          />
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline">
            <Link href="/dashboard/admins">Cancel</Link>
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader /> Updating Admin
              </>
            ) : (
              <>Update Admin</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default UpdateAdminForm;
