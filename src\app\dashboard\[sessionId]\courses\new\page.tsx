import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import React from "react";
import Link from "next/link";
import CreateCourse from "@/app/dashboard/[sessionId]/courses/new/_components/CreateCourse";
import { auth } from "@/lib/server/auth";
import { notFound, redirect } from "next/navigation";

const ALLOWED_ROLES = ["ADMIN"];

export default async function NewCoursePage({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) {
  const session = await auth();
  if (!session || !session.user) {
    redirect("/sign-in");
  }

  if (!ALLOWED_ROLES.includes(session.user.role)) {
    notFound();
  }

  const isAdmin = session.user.role === "ADMIN";

  const { sessionId } = await params;
  return (
    <>
      <Button variant="outline" asChild>
        <Link
          href={`/dashboard/${sessionId}/${isAdmin ? "courses" : "my-courses"}`}
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Courses
        </Link>
      </Button>

      <CreateCourse sessionId={sessionId} isAdmin={isAdmin} />
    </>
  );
}
