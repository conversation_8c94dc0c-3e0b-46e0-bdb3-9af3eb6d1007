
import { z } from "zod";
import { UserStatus } from "@prisma/client";
import { StudentWithUserAndSchool } from "./student.list";

export const studentSchema = z.object({
  firstName: z.string().min(1, { message: "Please provide your first name" }),
  lastName: z.string().min(1, { message: "Please provide your last name" }),
  email: z
    .string({ required_error: "Email field is required" })
    .email({ message: "Please provide a valid email" }),
  phone: z.string().min(1, { message: "Please provide your phone number" }),
  school: z.string().min(1, { message: "Please select your school" }),
  program: z.string().min(1, { message: "Please select your program" }),
  course: z.string().min(1, { message: "Please select your course" }),
});

export const studentUpdateSchema = z.object({
  ...studentSchema.shape,
  status: z.enum(["APPROVED", "REJECTED", "PENDING", "SUSPENDED"]),
});

//Types
export type Student = StudentWithUserAndSchool
export type StudentKeys = keyof Student
export type TStudentForm = z.infer<typeof studentSchema>;
export type TUpdateStudentForm = z.infer<typeof studentSchema> & {
  status?: UserStatus
  avatarUrl?: string
};

