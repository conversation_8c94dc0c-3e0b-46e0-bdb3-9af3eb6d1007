import { z } from "zod";
import { AdminProfile, UserStatus } from "@prisma/client";

export const updateAdminSchema = z.object({
  firstName: z.string().min(1, { message: "Please provide your first name" }),
  lastName: z.string().min(1, { message: "Please provide your last name" }),
  email: z
    .string({ required_error: "Email field is required" })
    .email({ message: "Please provide a valid email" }),
  phone: z.string().min(1, { message: "Please provide your phone number" }),
  school: z.string().min(1, { message: "Please select your school" }),
  bio: z.string().min(1, { message: "Please provide your bio" }),
  specialization: z.string().min(1, { message: "Please provide your specialization" }),
  avatarUrl: z.string(),
});

export const adminPasswordSchema = z.object({
  password: z
    .string()
    .min(8, { message: "Your password must have a minimum of 8 characters" }),
    confirmPassword: z
    .string()
    .min(8, { message: "Your password must have a minimum of 8 characters" }),
}).refine(
  (values) => {
    return values.password === values.confirmPassword;
  },
  {
    message: "Passwords must match!",
    path: ["confirmPassword"],
  }
);

export const changeAdminPasswordSchema = z.object({
  email: z
    .string({ required_error: "Email field is required" })
    .email({ message: "Please provide a valid email" }),
  password: z
    .string()
    .min(8, { message: "Your password must have a minimum of 8 characters" }),
    confirmPassword: z
    .string()
    .min(8, { message: "Your password must have a minimum of 8 characters" }),
}).refine(
  (values) => {
    return values.password === values.confirmPassword;
  },
  {
    message: "Passwords must match!",
    path: ["confirmPassword"],
  }
);

export const adminSchema = z.object({
  firstName: z.string().min(1, { message: "Please provide your first name" }),
  lastName: z.string().min(1, { message: "Please provide your last name" }),
  email: z
    .string({ required_error: "Email field is required" })
    .email({ message: "Please provide a valid email" }),
  password: z.string().min(8, { message: "Please provide your password with a minimum of 8 characters" }),
  phone: z.string().min(1, { message: "Please provide your phone number" }),
});

//Types
export type Admin = AdminProfile
export type AdminKeys = keyof Admin
export type TAdminForm = z.infer<typeof adminSchema>;
export type TUpdateAdminForm = z.infer<typeof updateAdminSchema> & {
  status?: UserStatus
};
export type TChangePasswordForm = z.infer<typeof changeAdminPasswordSchema>
export type TAdminPassword = z.infer<typeof adminPasswordSchema>;
export type AdminTable = Omit<Admin, 'password'>