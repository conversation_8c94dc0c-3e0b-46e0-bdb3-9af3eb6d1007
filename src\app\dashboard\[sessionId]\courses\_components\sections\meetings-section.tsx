"use client";

import {
  deleteMeeting,
  endMeeting,
  joinMeeting,
  Meeting,
  startMeeting,
} from "@/lib/server/action/courses/virtual-classrooms";
import MeetingCard from "../cards/course/meeting-card";
import { toast } from "sonner";
import { useState } from "react";

export default function MeetingsSection({
  meetings,
  userId,
}: {
  meetings: Meeting[];
  userId: string;
}) {
  const [filter, setFilter] = useState<"active" | "upcoming" | "live" | "past">(
    "active"
  );

  const filteredMeetings = meetings.filter((meeting) => {
    const now = new Date();
    const startTime = new Date(meeting.scheduledAt);
    const endTime = new Date(meeting.endedAt);

    switch (filter) {
      case "upcoming":
        return startTime > now;
      case "live":
        return startTime <= now && now <= endTime;
      case "past":
        return endTime <= now;
      case "active":
        return startTime > now || (startTime <= now && now <= endTime);
      default:
        return true;
    }
  });

  const handleJoinMeeting = async (meetingId: string) => {
    const res = await joinMeeting(meetingId);
    if (res.joinUrl) {
      window.open(res.joinUrl, "_blank");
    } else if (res.error) {
      toast.error(res.error);
    }
  };

  const handleStartMeeting = async (meetingId: string) => {
    const res = await startMeeting(meetingId);
    if (res.startUrl) {
      window.open(res.startUrl, "_blank");
    } else if (res.error) {
      toast.error(res.error);
    }
  };

  const handleEndMeeting = async (meetingId: string) => {
    const res = await endMeeting(meetingId);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  const handleDeleteMeeting = async (meetingId: string) => {
    const res = await deleteMeeting(meetingId);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <>
      <div className="py-5 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Course Meetings
          </h3>
          <div className="flex space-x-2">
            <button
              onClick={() => setFilter("active")}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                filter === "active"
                  ? "bg-indigo-100 text-indigo-700"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Active
            </button>
            <button
              onClick={() => setFilter("upcoming")}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                filter === "upcoming"
                  ? "bg-indigo-100 text-indigo-700"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Upcoming
            </button>
            <button
              onClick={() => setFilter("live")}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                filter === "live"
                  ? "bg-indigo-100 text-indigo-700"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Live
            </button>
            <button
              onClick={() => setFilter("past")}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                filter === "past"
                  ? "bg-indigo-100 text-indigo-700"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Past
            </button>
          </div>
        </div>
      </div>
      {filteredMeetings.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No meeting found.</p>
        </div>
      ) : (
        <ul className="grid grid-cols-1 md:grid-cols-2 2xl:grid-cols-3 gap-6">
          {filteredMeetings.slice(0, 5).map((meeting) => (
            <MeetingCard
              key={meeting.id}
              meeting={meeting}
              userId={userId}
              onJoin={handleJoinMeeting}
              onStart={handleStartMeeting}
              onEnd={handleEndMeeting}
              onDelete={handleDeleteMeeting}
            />
          ))}
        </ul>
      )}
    </>
  );
}
