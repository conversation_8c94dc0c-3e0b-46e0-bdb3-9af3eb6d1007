import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";

export default function AssessmentOverviewCard({
  studentEvaluation,
}: {
  studentEvaluation: {
    completedModules: number;
    inProgressModules: number;
    totalModules: number;
  };
}) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
              <span className="text-black font-bold text-xs">W</span>
            </div>
            <CardTitle>Student Assessment Evaluation</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Progress Circle */}
          <div className="flex items-center justify-center">
            <div className="relative w-32 h-32">
              <svg
                className="w-32 h-32 transform -rotate-90"
                viewBox="0 0 120 120"
              >
                <circle
                  cx="60"
                  cy="60"
                  r="50"
                  stroke="#e5e7eb"
                  strokeWidth="8"
                  fill="none"
                />
                <circle
                  cx="60"
                  cy="60"
                  r="50"
                  stroke="#3b82f6"
                  strokeWidth="8"
                  fill="none"
                  strokeDasharray={
                    (studentEvaluation.completedModules /
                      studentEvaluation.totalModules) *
                      3.14159 || 0
                  }
                  strokeLinecap="round"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold">
                  {Math.round(
                    (studentEvaluation.completedModules /
                      studentEvaluation.totalModules) *
                      100 || 0
                  )}
                  %
                </span>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 gap-6 text-center">
            <div>
              <div className="text-primary text-sm font-medium mb-1">
                Completed
              </div>
              <div className="text-3xl font-bold">
                {studentEvaluation.completedModules}
              </div>
            </div>
            <div>
              <div className="text-gray-400 text-sm font-medium mb-1">
                Not Yet
              </div>
              <div className="text-3xl font-bold">
                {studentEvaluation.inProgressModules}
              </div>
            </div>
          </div>

          {/* Legend */}
          <div className="flex items-center justify-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-primary rounded-sm"></div>
              <span>Completed</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-gray-300 rounded-sm"></div>
              <span>Not yet</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
