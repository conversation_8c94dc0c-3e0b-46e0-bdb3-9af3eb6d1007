"use client";

import { useForm } from "react-hook-form";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Lock, LogIn, Mail } from "lucide-react";
import Logo from "../shared/logo";
import TextInput from "../form-element/text-input";
import PasswordInput from "../form-element/password-input";
import SubmitButton from "../form-element/submit-button";
import CustomCarousel from "../shared/CustomCarousel";
import { signInWithCredentials } from "@/lib/server/action/users/user.action";
import { toast } from "sonner";
import Image from "next/image";

export type RegisterInputProps = {
  type: "ep" | "lc";
  email: string;
  password: string;
  loginCode: string;
};
export default function SigninForm({
  type,
  loginItems,
}: {
  type: "ep" | "lc";
  loginItems:
    | {
        image: string;
        title: string;
        subtitle: string;
      }[]
    | null;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<RegisterInputProps>();
  const router = useRouter();

  async function onSubmit(data: RegisterInputProps) {
    setIsLoading(true);
    try {
      const res = await signInWithCredentials({
        type,
        email: data.email,
        password: data.password,
        loginCode: data.loginCode,
      });
      if (res.success) {
        toast.success(res.message);
        router.push(`${type === "ep" ? "/dashboard" : "/students/dashboard"}`);
      } else {
        toast.error(res.error);
      }
    } catch (error) {
      console.error("Login failed", error);
      // Handle login failure (e.g., show an error message)
    } finally {
      setIsLoading(false);
      reset();
    }
  }
  return (
    <div className="w-full lg:grid h-screen lg:min-h-[600px] lg:grid-cols-2 relative ">
      <div className="flex items-center justify-center py-12">
        <div className="mx-auto grid w-[350px] gap-6">
          <div className="absolute left-1/3 top-14 lg:top-5 lg:left-5">
            <Logo />
          </div>
          <div className="grid gap-2 text-center mt-20 lg:mt-0">
            <h1 className="text-3xl font-bold">Login to Your Account</h1>
          </div>
          <form className="grid gap-4 px-2" onSubmit={handleSubmit(onSubmit)}>
            {type === "ep" ? (
              <>
                <TextInput
                  label="Email Address"
                  register={register}
                  name="email"
                  type="email"
                  errors={errors}
                  placeholder="Eg. <EMAIL>"
                  icon={Mail}
                />
                <PasswordInput
                  label="Password"
                  register={register}
                  name="password"
                  type="password"
                  errors={errors}
                  placeholder="******"
                  // forgotPasswordLink="/forgot-password"
                  icon={Lock}
                />
              </>
            ) : type === "lc" ? (
              <>
                <TextInput
                  label="Login Code"
                  register={register}
                  name="loginCode"
                  type="password"
                  errors={errors}
                  placeholder="******"
                  icon={Lock}
                />
              </>
            ) : null}

            <SubmitButton
              title="Login"
              loading={isLoading}
              loadingTitle="Logging in please wait..."
              buttonIcon={LogIn}
            />
          </form>
        </div>
      </div>
      <div className="hidden bg-muted lg:block relative">
        {loginItems ? (
          <CustomCarousel loginItems={loginItems} />
        ) : (
          <div className="relative w-full h-screen bg-purple-900 overflow-hidden">
            <div className="absolute inset-0">
              <div className="absolute inset-0 transition-opacity duration-1000 opacity-0">
                <Image
                  src={"/images/placeholder.svg"}
                  alt="Placeholder Image"
                  className="object-cover w-full h-full"
                  layout="fill"
                />
                <div className="absolute inset-0 bg-purple-900/50" />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
