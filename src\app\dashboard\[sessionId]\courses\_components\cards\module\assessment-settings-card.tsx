import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>itle,
  CardDescription,
} from "@/components/ui/card";
import AssessmentForm from "../../forms/AssessmentForm";
import { getAssessment } from "@/lib/server/action/courses/modules/assessments";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function AssessmentSettingsCard({
  assessment,
  moduleId,
}: {
  assessment: Awaited<ReturnType<typeof getAssessment>>;
  moduleId: string;
}) {
  const totalQuestions = assessment?._count.questions ?? 0;
  const maxStudentQuestions = assessment?.maxStudentQuestions ?? 0;
  const passingScore = assessment?.passingScore ?? 0;

  return (
    <div className="lg:col-span-1">
      <Card className="py-4">
        <CardHeader>
          <CardTitle>Assessment Settings</CardTitle>
          <CardDescription>Configure your assessment details</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <AssessmentForm
            assessmentData={
              assessment
                ? {
                    passingScore: assessment.passingScore,
                    maxStudentQuestions: assessment.maxStudentQuestions,
                    instructions: assessment.instructions || "",
                  }
                : undefined
            }
            moduleId={moduleId}
            assessmentId={assessment?.id}
          />

          <div className="pt-4 border-t">
            <AssInfo
              title="Total Questions"
              value={totalQuestions}
              className={
                totalQuestions >= maxStudentQuestions
                  ? "text-green-600"
                  : "text-red-600"
              }
              message={
                totalQuestions >= maxStudentQuestions
                  ? "You have enough questions"
                  : "You need more questions (Total questions is less than Max student questions)"
              }
            />
            <AssInfo
              title="Max Student Questions"
              value={maxStudentQuestions}
              message="Total number or questions available to students"
            />
            <AssInfo
              title="Passing Score"
              value={passingScore}
              message="Minimum score required to pass assessment"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function AssInfo({
  title,
  value,
  message,
  className,
}: {
  title: string;
  value: number;
  message: string;
  className?: string;
}) {
  return (
    <div className="flex justify-between text-sm text-gray-600">
      <span>{title}</span>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className={className}>{value}</span>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-base">{message}</p>
        </TooltipContent>
      </Tooltip>
    </div>
  );
}
