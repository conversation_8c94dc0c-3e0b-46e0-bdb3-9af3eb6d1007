"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import { createSession, updateSession } from "@/lib/server/action/sessions";
import { Button } from "@/components/ui/button";
import { FormInputField } from "@/components/form-element/input-field";
import { FormDatePickerField } from "@/components/form-element/date-picker";
import { FormTextareaField } from "@/components/form-element/text-area";
import { FormRadioGroupField } from "@/components/form-element/radio-group";
import {
  sessionSchema,
  TSessionForm,
} from "@/lib/server/action/sessions/session.schema";

export default function SessionForm({
  sessionId,
  sessionData,
}: {
  sessionId?: string;
  sessionData?: TSessionForm;
}) {
  const form = useForm<TSessionForm>({
    resolver: zodResolver(sessionSchema),
    defaultValues: sessionData ?? {
      name: "",
      description: "",
      startDate: new Date(),
      endDate: new Date(),
      isActive: "false",
    },
    mode: "onChange",
  });

  const onSubmit = async (values: TSessionForm) => {
    const res = sessionData
      ? await updateSession(sessionId as string, values)
      : await createSession(values);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormInputField
          control={form.control}
          name="name"
          label="Name"
          placeholder="Enter session name"
        />
        <FormDatePickerField
          control={form.control}
          name="startDate"
          label="Start Date"
          placeholder="Select a start date"
        />
        <FormDatePickerField
          control={form.control}
          name="endDate"
          label="End Date"
          placeholder="Select an end date"
        />
        <FormTextareaField
          control={form.control}
          name="description"
          label="Description"
          placeholder="Enter session description"
        />
        {!sessionId ? (
          <FormRadioGroupField
            control={form.control}
            name="isActive"
            label="Set as active session"
            options={[
              { value: "true", label: "Yes" },
              { value: "false", label: "No" },
            ]}
          />
        ) : null}
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader />{" "}
                {sessionData ? "Updating Session" : "Creating Session"}
              </>
            ) : (
              <>{sessionData ? "Update Session" : "Create Session"}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
