import { z } from "zod";


export const socialSchema =  z.object({
  id: z.string(),
  platform: z.string(),
  link: z.string().url({ message: "Please provide a valid url" }),
});

export const generalSettingsSchema = z.object({
  siteTitle: z.string().min(1, { message: "Please provide the site title" }),
  siteName: z.string().min(1, { message: "Please provide the site name" }),
  siteAddress: z.string().min(1, { message: "Please provide the site address" }),
  email: z.string().email({ message: "Please provide a valid email" }),
  phone1: z.string().min(1, { message: "Please provide the phone number" }),
  phone2: z.string().min(1, { message: "Please provide the phone number" }),
  landline1: z.string().min(1, { message: "Please provide the landline number" }),
  landline2: z.string().min(1, { message: "Please provide the landline number" }),
  address: z.string().min(1, { message: "Please provide the address" }),
  socials: z.array(socialSchema),
});

export const generalSettingsFormSchema = generalSettingsSchema.omit({
  socials: true,
});

export type SocialMediaEntry = z.infer<typeof socialSchema>;

export type TGeneralSettings = z.infer<typeof generalSettingsSchema>;
export type TGeneralSettingsForm = z.infer<typeof generalSettingsFormSchema>;
export type TGeneralSettingsSubmit = TGeneralSettingsForm & { socials: string };
