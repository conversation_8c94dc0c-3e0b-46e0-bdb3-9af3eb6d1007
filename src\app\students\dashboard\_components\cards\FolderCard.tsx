"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { CourseResourceFolderWithResources } from "@/lib/server/action/courses/resources";
import { formatBytes } from "@/lib/utils";
import { Folder } from "lucide-react";
import { useState } from "react";

export default function FolderCard({
  folder,
}: {
  folder: CourseResourceFolderWithResources;
}) {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Card
        className="hover:shadow-md transition-shadow cursor-pointer py-0"
        onClick={() => setOpen(true)}
      >
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Folder className="w-8 h-8 text-primary" />
              <div>
                <h4 className="font-medium">
                  {folder.name.toLocaleUpperCase()}
                </h4>
                <p className="text-sm text-gray-500">
                  {folder.resources.length} files
                </p>
              </div>
            </div>
            <Badge variant="secondary">{folder.resources.length}</Badge>
          </div>
        </CardContent>
      </Card>

      <FolderDialog open={open} setOpen={setOpen} folder={folder} />
    </>
  );
}

function FolderDialog({
  open,
  setOpen,
  folder,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  folder: CourseResourceFolderWithResources;
}) {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-2xl max-h-[85vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>{folder.name.toLocaleUpperCase()}</DialogTitle>
          <DialogDescription>View course resources.</DialogDescription>
        </DialogHeader>

        <div className="flex-1 min-h-0">
          {folder.resources.length === 0 ? (
            <div className="flex items-center justify-center h-32">
              <p className="text-sm text-muted-foreground">No file found.</p>
            </div>
          ) : (
            <ScrollArea className="h-[400px]">
              <div className="space-y-3 p-1">
                {folder.resources.map((resource) => (
                  <div
                    key={resource.id}
                    className="border flex flex-col px-4 py-2 rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => window.open(resource.fileUrl!, "_blank")}
                  >
                    <span className="text-sm font-medium">{resource.name}</span>
                    <p className="text-xs text-muted-foreground">
                      {formatBytes(resource.fileSize!)}
                    </p>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
