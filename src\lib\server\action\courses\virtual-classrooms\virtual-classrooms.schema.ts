import { z } from "zod";

// Zod schema for form validation
export const meetingSchema = z.object({
  title: z.string().min(1, "Meeting title is required").max(100, "Title must be less than 100 characters"),
  description: z.string().max(500, "Description must be less than 500 characters").optional(),
  scheduledAt: z.string().optional(),
  duration: z.number().min(15, "Duration must be at least 15 minutes").max(480, "Duration cannot exceed 8 hours"),
  maxParticipants: z.number().min(2, "At least 2 participants required").max(1000, "Maximum 1000 participants allowed"),
  waitingRoomEnabled: z.boolean(),
  recordingEnabled: z.boolean(),
  chatEnabled: z.boolean(),
  zoomEmail: z.string().email("Valid email is required"),
});

export type MeetingFormData = z.infer<typeof meetingSchema>;