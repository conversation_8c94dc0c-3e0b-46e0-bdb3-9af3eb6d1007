import { But<PERSON> } from "@/components/ui/button";
import SmallTitle from "../shared/small-title";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

type HeroSectionProps = {
  siteName: string;
  heroTitle: string;
  heroSubtitle: string;
  heroCtaText: string;
  heroCtaLink: string;
};

export function HeroSection({
  siteName,
  heroTitle,
  heroSubtitle,
  heroCtaText,
  heroCtaLink,
}: HeroSectionProps) {
  return (
    <section className="w-full min-h-[80vh] bg-gradient-to-b from-white to-gray-100 flex items-center justify-center">
      <div className="container max-w-7xl mx-auto px-4 md:px-6 text-center flex flex-col items-center space-y-8">
        <SmallTitle
          logo="✨"
          title={`Welcome to ${siteName.toLocaleUpperCase()}`}
        />

        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-gray-900 mb-6">
          {heroTitle}
        </h1>

        <p className="max-w-2xl mx-auto text-lg text-gray-600 mb-10">
          {heroSubtitle}
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" className="px-6 h-12 text-base rounded-lg" asChild>
            <Link href={heroCtaLink}>
              {heroCtaText}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
