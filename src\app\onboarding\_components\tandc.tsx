import { Button } from "@/components/ui/button";

export default function TermsAndConditions({
  handleBack,
  handleNext,
  dataPrivacyMessage,
}: {
  handleBack: () => void;
  handleNext: () => void;
  dataPrivacyMessage: string;
}) {
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">
        Data Privacy Statement
      </h2>

      <div className="bg-gray-50 p-6 rounded-lg space-y-4 text-gray-700 leading-relaxed">
        <p>{dataPrivacyMessage}</p>

        <div className="mt-6 p-4 bg-blue-50 border-l-4 border-blue-400">
          <p className="text-sm text-gray-600">
            By clicking on the agree button, you agree to our Terms and
            Conditions and Privacy Policy, and consent to the collection and use
            of your personal information as described.
          </p>
        </div>
      </div>

      <div className="flex justify-between pt-6">
        <Button
          variant="outline"
          onClick={handleBack}
          className="px-6 bg-transparent"
        >
          Back
        </Button>
        <Button onClick={handleNext} className="px-6">
          Agree
        </Button>
      </div>
    </div>
  );
}
