  'use server'

import prisma from '@/lib/prisma'
import { revalidatePath } from 'next/cache'
import { TGeneralSettingsSubmit } from './settings.schema';


export async function updateGeneralSettings(id: string, data: Partial<TGeneralSettingsSubmit>) {
  try {
    await prisma.generalSettings.update({
      where: { id },
      data,
    })
    
    revalidatePath('/dashboard/settings')
    return { success: true, message: 'General settings updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update general settings' }
  }
}

