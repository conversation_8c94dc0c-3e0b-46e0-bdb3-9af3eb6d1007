import { auth } from "../../../../../auth";
import { ProgramCard } from "../_components/cards/ProgramCard";

const programData = [
  {
    title: "Program 1",
    description:
      "Comprehensive examination covering fundamental programming concepts, data structures, and algorithmic thinking. This assessment evaluates your understanding of core CS principles.",
  },
];

export default async function ProgramsPage() {
  const session = await auth();
  if (!session || !session.user) {
    return null;
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">My Program</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {programData.map((program, index) => (
          <ProgramCard
            key={index}
            {...program}
            studentId={session.user.profileId}
          />
        ))}
      </div>
    </div>
  );
}
