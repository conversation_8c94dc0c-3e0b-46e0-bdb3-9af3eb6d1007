import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ActivityStatus } from "@prisma/client";

type ActivitiesReportProps = {
  activities?: {
    id: string;
    question: string;
    score: number;
    passScore: number;
    isGraded: boolean;
    deadline: Date;
    status: ActivityStatus;
  }[];
};

export default function ActivitiesReport({
  activities,
}: ActivitiesReportProps) {
  const now = new Date();

  return (
    <section>
      <Card className="py-0">
        <CardHeader className="bg-gray-100 py-2">
          <CardTitle className="text-center font-semibold">
            Activities report
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold">Activity name</TableHead>
                <TableHead className="font-semibold">Status</TableHead>
                <TableHead className="font-semibold">Score</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {!activities ? (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : activities.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    No activity found.
                  </TableCell>
                </TableRow>
              ) : (
                activities.map((activity) => {
                  let gradeDisplay;
                  const expired = activity.deadline < now;
                  const submitted = activity.status === "SUBMITTED";

                  if (activity.isGraded) {
                    gradeDisplay = `${activity.score.toFixed(0)} / ${
                      activity.passScore
                    }`;
                  } else if (!expired && submitted && !activity.isGraded) {
                    gradeDisplay = "Waiting to be graded";
                  } else if (!expired && !submitted) {
                    gradeDisplay = "Pending submission";
                  } else {
                    gradeDisplay = `0 / ${activity.passScore}`;
                  }

                  return (
                    <TableRow key={activity.id}>
                      <TableCell>{activity.question}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            activity.isGraded
                              ? "default"
                              : expired && !submitted
                              ? "destructive"
                              : submitted
                              ? "secondary"
                              : "outline"
                          }
                        >
                          {activity.isGraded
                            ? "Graded"
                            : expired && !submitted
                            ? "Expired"
                            : submitted
                            ? "Submitted"
                            : "Pending"}
                        </Badge>
                      </TableCell>
                      <TableCell>{gradeDisplay}</TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </section>
  );
}
