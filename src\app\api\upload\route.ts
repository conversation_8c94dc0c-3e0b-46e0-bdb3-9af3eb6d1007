import { Upload<PERSON>and<PERSON> } from '@/lib/file-upload/upload-handler'
import { getActiveSession } from '@/lib/server/action/sessions'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  const session = await getActiveSession()

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const handler = new UploadHandler(session.id)
  const result = await handler.handleUpload(request)
  
  if (result.error) {
    return NextResponse.json({ error: result.error }, { status: 400 })
  }
  
  return NextResponse.json(result)
}