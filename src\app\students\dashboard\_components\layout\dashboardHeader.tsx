"use client";

import React from "react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { User } from "next-auth";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

const DashboardHeader = ({ user }: { user: User }) => {
  return (
    <div className="flex h-16 items-center gap-4 border-b px-4 sticky top-0 z-50 bg-background">
      <SidebarTrigger />
      <div className="flex-1"></div>

      <Button size="lg" variant="outline" className="px-1 sm:px-2">
        <Avatar className="h-8 w-8 rounded-lg">
          <AvatarFallback className="rounded-lg">
            {user.firstName[0].toLocaleUpperCase() +
              user.lastName[0].toLocaleUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div className="hidden sm:grid flex-1 text-left text-sm leading-tight">
          <span className="truncate font-semibold">
            {user.firstName + " " + user.lastName}
          </span>
          <span className="truncate text-xs">{user.email}</span>
        </div>
      </Button>
    </div>
  );
};

export default DashboardHeader;
