'use server'

import prisma from "@/lib/prisma"
import { Prisma, PublishStatus } from "@prisma/client";


export type CourseWithExtras = Prisma.CourseGetPayload<{
  include: {
    program: {
      select: {
        id: true,
        name: true
      }
    },
    _count: {
      select: {
        enrollments: true,
        modules: true
      }
    },
    modules: true,
    enrollments: {
      select: {
        id: true,
        student: {
          select: {
            id: true,
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        }
      }
    },
    teacherAssignments: {
      select: {
        role: true,
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    },
  };
}>;

export type CourseAssignedTeachers = Prisma.TeacherCourseAssignmentGetPayload<{
  select: {
    id: true,
    role: true,
    userId: true,
    user: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true
        teacherProfile: {
          select: {
            id: true
          }
        }
      }
    }
  }
}>;

export async function getCourses({
  sessionId,
  search,
  program,
  sortby,
  status = "PUBLISHED",
}: {
  sessionId: string;
  page: number;
  search?: string;
  program?: string;
  sortby?: string;
  status?: PublishStatus | 'all';
}) {
  const whereClause: Prisma.CourseWhereInput = {};
  const orderByClause: Prisma.CourseOrderByWithRelationInput[] = [];

  whereClause.sessionId = sessionId;

  if (search && search !== 'all') {
    whereClause.OR = [
      { title: { contains: search } },
      { description: { contains: search } },
      { program: { name: { contains: search } } },
    ];
  }

  if (status !== 'all' ) {
    whereClause.status = status;
  }

  if (program !== 'all') {
    whereClause.programId = program;
  }

  if (sortby) {
    switch (sortby) {
      case "program":
        orderByClause.push({ program: { id: "asc" } });
        break;
      case "status":
        orderByClause.push({ status: "asc" });
        break;
      default:
        orderByClause.push({ title: "asc" });
        break;
    }
  }

  if (orderByClause.length === 0) {
    orderByClause.push({ title: "asc" });
  }

  const queryOptions: Prisma.CourseFindManyArgs = {
    include: {
      program: {
        select: {
          id: true,
          name: true
        }
      },
      _count: {
        select: {
          enrollments: true,
          modules: true
        }
      },
      modules: true,
      enrollments: {
        select: {
          id: true,
          student: {
            select: {
              id: true,
              user: {
                select: {
                  firstName: true,
                  lastName: true
                }
              }
            }
          }
        }
      },
      teacherAssignments: {
        select: {
          role: true,
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      },
      resources: true
    },
    where: whereClause,
    orderBy: orderByClause,
  };
  
  try {
    const courses = await prisma.course.findMany(queryOptions) as CourseWithExtras[];

    const total = await prisma.course.count({
      where: whereClause,
    });

    return { courses, total };
  } catch (error) {
    console.error("Error fetching courses:", error);
    throw new Error("Failed to fetch courses.");
  }
}

export async function getTeacherCourses({teacherId, sessionId}: {teacherId: string, sessionId: string}) {
  try {
    const courses = await prisma.course.findMany({
      where: { teacherAssignments: { some: { user: { teacherProfile: { id: teacherId } } } }, sessionId },
      include: {
        program: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            enrollments: true,
            modules: true
          }
        },
        modules: true,
        enrollments: {
          select: {
            id: true,
            student: {
              select: {
                id: true,
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            }
          }
        },
        teacherAssignments: {
          select: {
            role: true,
            user: {
              select: {
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        },
      },
    })

    return courses
  } catch (error) {
    console.log(error)
    return []
  }
}

export async function getCourseStatusData(sessionId: string) {
  try {
    const total = await prisma.course.count({ where: { sessionId } });
    const draft = await prisma.course.count({ where: { status: 'DRAFT', sessionId } });
    const published = await prisma.course.count({ where: { status: 'PUBLISHED', sessionId } });
    const students = await prisma.enrollment.count({ where: { course: { sessionId } } });

    return { total, draft, published, students };
  } catch (error) {
    console.error("Error fetching course status data:", error);
    throw new Error("Failed to fetch course status data.");
  }
}

export async function getTeacherCourseStatusData(teacherId: string, sessionId: string) {
  try {
    const total = await prisma.course.count({ where: { sessionId, teacherAssignments: { some: { user: { teacherProfile: { id: teacherId } } } } } });
    const draft = await prisma.course.count({ where: { status: 'DRAFT', sessionId, teacherAssignments: { some: { user: { teacherProfile: { id: teacherId } } } } } });
    const published = await prisma.course.count({ where: { status: 'PUBLISHED', sessionId, teacherAssignments: { some: { user: { teacherProfile: { id: teacherId } } } } } });
    const students = await prisma.enrollment.count({ where: { course: { sessionId, teacherAssignments: { some: { user: { teacherProfile: { id: teacherId } } } } } } });

    return { total, draft, published, students };
  } catch (error) {
    console.error("Error fetching teacher course status data:", error);
    throw new Error("Failed to fetch teacher course status data.");
  }
}

export async function checkCourse(courseId: string) {
  try {
    const course = await prisma.course.findUnique({
      where: { id: courseId },
    })
    
    return course
  } catch (error) {
    console.log(error)
    return null
  }
}

export async function getCourseOptions(programId: string) {
  try {
    const courses = await prisma.course.findMany({
      where: { programId },
      select: {
        id: true,
        title: true
      },
      orderBy: { order: 'asc' }
    })
    
    return courses.map(course => ({ label: course.title, value: course.id }))
  } catch (error) {
    console.log(error)
    return []
  }
}

export async function getCourseStudents (courseId: string) {
  try {
    const students = await prisma.enrollment.findMany({
      where: { courseId },
      include: {
        student: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                email: true
              }
            },
            school: {
              select: {
                name: true
              }
            },
            progressTracking: {
              select: {
                progress: true,
                completedAt: true
              },
            }
          }
        },
      }
    })

    const total = await prisma.enrollment.count({
      where: { courseId }
    })
    
    return { students, total }
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch students.")
  }
}


export async function getCourseWithDetails(courseId: string) {
  return await prisma.course.findUnique({
    where: { id: courseId },
    select: {
      id: true,
      title: true,
      description: true,
      status: true,
      fileId: true,
      fileUrl: true,
      program: {
        select: {
          name: true,
          id: true
        }
      },
      teacherAssignments: {
        select: {
          id: true,
          role: true,
          userId: true,
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              teacherProfile: {
                select: {
                  id: true
                }
              }
            }
          }
        }
      },
    }
  })
}

// Analytics
export async function getCourseAnalytics(courseId: string) {
  try {
    const [totalModules, totalAttempts, averageScore, totalStudents] = await Promise.all([
      prisma.module.count({
        where: { courseId }
      }),
      prisma.assessmentAttempt.count({
        where: { assessment: { module: { courseId } } }
      }),
      prisma.assessmentAttempt.aggregate({
        where: { assessment: { module: { courseId } } },
        _avg: { score: true }
      }),
      prisma.enrollment.count({
        where: { courseId }
      })
    ])
    
    return { totalModules, totalAttempts, averageScore: averageScore._avg.score ?? 0, totalStudents }
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch analytics.")
  }
}

export async function getCourseTeachers(courseId: string) {
  try {
    const teachers = await prisma.teacherCourseAssignment.findMany({
      where: { courseId },
      select: {
        id: true,
        role: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            teacherProfile: {
              select: {
                id: true
              }
            }
          }
        }
      }
    })
    
    const primaryTeacher = teachers.find(ta => ta.role === 'PRIMARY')
    const primaryTeacherName = primaryTeacher ? primaryTeacher.user.firstName + " " + primaryTeacher.user.lastName : "N/A"
    const assistantTeachers = teachers.filter(ta => ta.role === 'ASSISTANT')
    const assistantTeacherNames = assistantTeachers.map(ta => ta.user.firstName + " " + ta.user.lastName)

    return { primaryTeacherName, assistantTeacherNames }
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch teachers.")
  }
}

export async function getTopPerformingModules(courseId: string) {
  try {
    const modules = await prisma.module.findMany({
      where: { courseId },
      select: {
        id: true,
        title: true,
        assessment: {
          select: {
            _count: {
              select: {
                attempts: true
              }
            },
            attempts: true
          }
        }
      },
      orderBy: { order: 'asc' }
    })
    
    const moduleData = modules.map(module => ({
      id: module.id,
      title: module.title,
      attempts: Math.round(module.assessment?._count.attempts ?? 0),
      avgScore: module.assessment && Array.isArray(module.assessment.attempts) && module.assessment.attempts.length > 0
        ? Math.round(module.assessment.attempts.reduce((sum, attempt) => sum + (attempt.score ?? 0), 0) / module.assessment.attempts.length)
        : 0
    }))
    
    return moduleData.sort((a, b) => b.avgScore - a.avgScore).slice(0, 4)
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch modules.")
  }
}

export async function getRecentActivity(courseId: string) {
  try {
    const recentActivity = await prisma.assessmentAttempt.findMany({
      where: { assessment: { module: { courseId } } },
      select: {
        id: true,
        student: {
          select: {
            id: true,
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        assessment: {
          select: {
            module: {
              select: {
                title: true
              }
            }
          }
        },
        score: true,
        completedAt: true
      },
      orderBy: { completedAt: 'desc' },
      take: 5
    })
    
    return recentActivity.map(activity => ({
      quiz: activity.assessment.module.title,
      student: activity.student.user.firstName + " " + activity.student.user.lastName,
      score: Math.round(activity.score ?? 0),
      date: activity.completedAt ? activity.completedAt.toDateString() : null
    }))
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch recent activity.")
  }
}

export async function getStudentEvaluation(courseId: string) {
  try {
    const [completedModules, inProgressModules] = await Promise.all([
      prisma.studentProgress.count({
        where: { courseId , progress: 100 },
      }), 
      prisma.studentProgress.count({
        where: { courseId, progress: { lt: 100 } },
      })
    ])
    
    return { completedModules, inProgressModules, totalModules: completedModules + inProgressModules }
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch student evaluation.")
  }
}