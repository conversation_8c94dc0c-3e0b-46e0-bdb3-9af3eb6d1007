/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import ImageUploadSection from "../image-upload";

type AboutTabProps = {
  about: {
    title: string;
    description: string;
    brandIdentity: {
      title: string;
      logo: string;
      description: string;
    };
  };
  updateNestedData: (path: string[], value: any) => void;
};

export default function AboutTab({ about, updateNestedData }: AboutTabProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>About Section</CardTitle>
        <CardDescription>Manage about page content</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="aboutTitle">Title</Label>
          <Input
            id="aboutTitle"
            value={about.title}
            onChange={(e) =>
              updateNestedData(["about", "title"], e.target.value)
            }
          />
        </div>
        <div>
          <Label htmlFor="aboutDescription">Description</Label>
          <Textarea
            id="aboutDescription"
            value={about.description}
            onChange={(e) =>
              updateNestedData(["about", "description"], e.target.value)
            }
            rows={6}
          />
        </div>
        <div>
          <Label htmlFor="brandTitle">Brand Identity Title</Label>
          <Input
            id="brandTitle"
            value={about.brandIdentity.title}
            onChange={(e) =>
              updateNestedData(
                ["about", "brandIdentity", "title"],
                e.target.value
              )
            }
          />
        </div>
        <div>
          <Label htmlFor="brandDescription">Brand Identity Description</Label>
          <Input
            id="brandDescription"
            value={about.brandIdentity.description}
            onChange={(e) =>
              updateNestedData(
                ["about", "brandIdentity", "description"],
                e.target.value
              )
            }
          />
        </div>

        <ImageUploadSection
          imageType="brandLogo"
          currentImageUrl={about.brandIdentity.logo}
          title="Brand Logo"
          description="Logo representing the brand identity"
          width={120}
          height={120}
          updateNestedData={updateNestedData}
        />
      </CardContent>
    </Card>
  );
}
