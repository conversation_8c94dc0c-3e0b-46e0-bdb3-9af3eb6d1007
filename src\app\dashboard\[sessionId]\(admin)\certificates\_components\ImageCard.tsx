"use client";

import Image from "next/image";
// import DeleteTemplate from "./delete-template";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { useState } from "react";

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  altText: string;
}

function ImageModal({ isOpen, onClose, imageUrl, altText }: ImageModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-0 bg-black/90 border-none">
        <DialogTitle className="sr-only">Certificate Template</DialogTitle>
        <div className="relative w-full h-full flex items-center justify-center">
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-4 right-4 z-50 bg-black/50 hover:bg-black/70 text-white rounded-full"
            onClick={onClose}
          >
            <X className="h-6 w-6" />
            <span className="sr-only">Close</span>
          </Button>
          <div className="relative w-full h-full flex items-center justify-center p-8">
            <Image
              src={imageUrl}
              alt={altText}
              fill
              className="object-contain"
              sizes="95vw"
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default function ImageCard({
  imageUrl,
  index,
}: {
  fileId: string;
  imageUrl: string;
  index: number;
}) {
  const [selectedImage, setSelectedImage] = useState<{
    url: string;
    alt: string;
  } | null>(null);

  const handleImageClick = (imageUrl: string, altText: string) => {
    setSelectedImage({ url: imageUrl, alt: altText });
  };

  const handleCloseModal = () => {
    setSelectedImage(null);
  };

  return (
    <>
      <li>
        <div className="relative border-2 border-dashed border-gray-300 rounded-lg p-2 text-center space-y-2">
          {/* <div className="absolute top-2 right-2 z-20">
            <DeleteTemplate templateId={fileId} />
          </div> */}
          <div
            className="cursor-pointer hover:opacity-80 transition-opacity"
            onClick={() =>
              handleImageClick(
                imageUrl || "/images/placeholder.svg",
                `Certificate template ${index + 1}`
              )
            }
          >
            <Image
              src={imageUrl || "/images/placeholder.svg"}
              alt={`certificate template ${index + 1}`}
              width={300}
              height={200}
              className="object-contain rounded-lg"
              placeholder="blur"
              blurDataURL="/images/placeholder-blur.jpg"
            />
          </div>
          <p className="text-sm text-gray-600">Template {index + 1}</p>
        </div>
      </li>

      <ImageModal
        isOpen={!!selectedImage}
        onClose={handleCloseModal}
        imageUrl={selectedImage?.url || ""}
        altText={selectedImage?.alt || ""}
      />
    </>
  );
}
