import PageWrapper from "../../../_components/layout/PageWrapper";
import { Suspense } from "react";
import Loading from "../../../_components/Loading";
import Pagination from "../../../_components/table/Pagination";
import { DataTable } from "../../../_components/table/data-table";
import TableSearch from "../../../_components/table/TableSearch";
import { TableFilter } from "../../../_components/table/TableFilter";
import { getTeachers } from "@/lib/server/action/teachers";
import { columns } from "./column";

const breadcrumbItems = [
  { label: "Home", href: "/dashboard" },
  { label: "Teacher", href: "#" },
  { label: "Pending Request" },
];

async function SuspendedComponent({
  searchParams,
  params,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
  params: Promise<{ sessionId: string }>;
}) {
  const { sessionId } = await params;
  const { page, search, sortby } = await searchParams;
  const currentPage = page ? +page : 1;
  const { teachers, total } = await getTeachers({
    sessionId,
    page: currentPage,
    search: search || "",
    sortby: sortby || "",
    status: "PENDING",
  });

  return (
    <>
      <DataTable columns={columns} data={teachers} />
      <Pagination page={currentPage} count={total} />
    </>
  );
}

export default function TeachersPage({
  searchParams,
  params,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
  params: Promise<{ sessionId: string }>;
}) {
  const filterItems = [
    { name: "Name", value: "name" },
    { name: "Email", value: "email" },
    { name: "Status", value: "status" },
    { name: "School", value: "school" },
  ];

  return (
    <PageWrapper pgTitle="Manage Teacher" breadcrumbItems={breadcrumbItems}>
      {/* TOP */}
      <div className="flex justify-between items-center gap-4 w-full md:w-auto">
        <TableSearch />
        <div className="flex items-center gap-4 self-end">
          <TableFilter filterItems={filterItems} />
        </div>
      </div>
      <Suspense fallback={<Loading />}>
        <SuspendedComponent searchParams={searchParams} params={params} />
      </Suspense>
    </PageWrapper>
  );
}
