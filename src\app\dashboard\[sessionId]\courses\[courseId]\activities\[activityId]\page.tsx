import Link from "next/link";
import { ChevronLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { DataTable } from "@/app/dashboard/[sessionId]/_components/table/data-table";
import Pagination from "@/app/dashboard/[sessionId]/_components/table/Pagination";
import { columns } from "./columns";
import { getActivity } from "@/lib/server/action/courses/activities";
import { notFound } from "next/navigation";

export default async function ActivityDetail({
  params,
  searchParams,
}: {
  params: Promise<{ sessionId: string; courseId: string; activityId: string }>;
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const { sessionId, courseId, activityId } = await params;
  const { page } = await searchParams;
  const currentPage = page ? +page : 1;
  const activityData = await getActivity({
    activityId,
    page: currentPage,
  });

  if (!activityData || !activityData.activity) {
    return notFound();
  }

  const { activity, totalStudentResponse } = activityData;

  return (
    <>
      {/* Back Button */}
      <Button variant="ghost" asChild className="mb-4 -mt-2">
        <Link href={`/dashboard/${sessionId}/courses/${courseId}`}>
          <ChevronLeft className="w-4 h-4 mr-1" />
          Back to activity
        </Link>
      </Button>

      {/* Question Card */}
      <Card className="bg-white mb-6 py-0">
        <CardContent className="p-6">
          <div className="flex items-start">
            <div className="flex-1">
              <p className="text-gray-900 mb-4">
                What is the best programming language we can use to develop an
                inventory management system?
              </p>
              <div className="flex items-center gap-6 text-sm text-gray-600">
                <span>Pass score: {activity.score}</span>
                <span>
                  📊 Total response:{" "}
                  <span className="font-medium">{totalStudentResponse}</span>
                </span>
                <span>
                  ❌ Not yet:{" "}
                  <span className="font-medium">
                    {activity.course._count.enrollments - totalStudentResponse}
                  </span>
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Students Response Section */}
      <div className="space-y-4">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900">
            Students response
          </h2>
        </div>

        {/* Table Header */}
        <DataTable columns={columns} data={activity.responses} />
        <Pagination page={currentPage} count={totalStudentResponse} />
      </div>
    </>
  );
}
