"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Check, Building2, FileText, User } from "lucide-react";
import SignupForm from "./SignupForm";
import TermsAndConditions from "./tandc";
import Link from "next/link";
import Introduction from "./intro";

type OnboardingData = {
  siteLogo: string;
  siteName: string;
  welcomeMessage: string;
  dataPrivacyMessage: string;
};

export default function OnboardingFlow({
  type,
  sessionId,
  onboardingData,
}: {
  type: string;
  sessionId: string;
  onboardingData: OnboardingData;
}) {
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    { id: "introduction", label: "Introduction", icon: Building2 },
    { id: "personal", label: "Terms and Conditions", icon: User },
    { id: "terms", label: "Personal Details", icon: FileText },
  ];

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    // Process form submission
    setCurrentStep(3); // Go to success page
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center space-x-8 mb-8 bg-gray-50 py-4 px-6 rounded-lg">
      {steps.map((step, index) => {
        const Icon = step.icon;
        const isActive = index === currentStep;
        const isCompleted = index < currentStep;

        return (
          <div key={step.id} className="flex items-center space-x-2">
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full ${
                isActive
                  ? "bg-blue-500 text-white"
                  : isCompleted
                  ? "bg-green-500 text-white"
                  : "bg-gray-300 text-gray-600"
              }`}
            >
              <Icon className="w-4 h-4" />
            </div>
            <span
              className={`text-sm font-medium ${
                isActive
                  ? "text-blue-600"
                  : isCompleted
                  ? "text-green-600"
                  : "text-gray-500"
              }`}
            >
              {step.label}
            </span>
          </div>
        );
      })}
    </div>
  );

  const renderSuccess = () => (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center p-4">
      <div className="bg-white rounded-3xl p-12 max-w-md w-full text-center space-y-6 shadow-2xl">
        <div className="flex justify-center">
          <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center">
            <Check className="w-10 h-10 text-white stroke-[3]" />
          </div>
        </div>

        <div className="space-y-4">
          <h1 className="text-2xl font-bold text-gray-900">
            Thank you for registering!
          </h1>
          <p className="text-gray-600 leading-relaxed">
            Your account is under review and you will be notified in email once
            approved.
          </p>
        </div>

        <Button asChild className="w-full h-12 text-lg font-medium">
          <Link href="/">Home</Link>
        </Button>
      </div>
    </div>
  );

  if (currentStep === 3) {
    return renderSuccess();
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        {renderStepIndicator()}

        <div className="max-w-6xl mx-auto">
          {currentStep === 0 && (
            <Introduction
              handleNext={handleNext}
              siteLogo={onboardingData.siteLogo || "/images/placeholder.svg"}
              siteName={onboardingData.siteName || "Site Name"}
              welcomeMessage={
                onboardingData.welcomeMessage || "Welcome site message"
              }
            />
          )}
          {currentStep === 1 && (
            <TermsAndConditions
              handleBack={handleBack}
              handleNext={handleNext}
              dataPrivacyMessage={
                onboardingData.dataPrivacyMessage || "Data privacy message"
              }
            />
          )}
          {currentStep === 2 && (
            <SignupForm
              sessionId={sessionId}
              handleSubmit={handleSubmit}
              handleBack={handleBack}
              type={type as "teacher" | "student"}
            />
          )}
        </div>
      </div>
    </div>
  );
}
