"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ImageIcon, Loader2, Plus, Trash2, Upload, X } from "lucide-react";
import Image from "next/image";
import { CMSData } from "@/lib/server/action/frontend/frontend.action";
import { deleteFromBunnyCDN } from "@/lib/server/action/bunny/bunny.action";

type CoreFeaturesProps = {
  data: CMSData;
  setData: React.Dispatch<React.SetStateAction<CMSData | null>>;
  markAsChanged: () => void;
  uploadingStates: { [key: string]: boolean };
  setUploadingStates: React.Dispatch<
    React.SetStateAction<{ [key: string]: boolean }>
  >;
  handleFileChange: (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number,
    type: "feature" | "design"
  ) => void;
  triggerFileInput: (index: number, type: "feature" | "design") => void;
  replaceImage: (
    index: number,
    type: "feature" | "design",
    oldImageUrl: string
  ) => void;
  featureFileInputRefs: React.MutableRefObject<{
    [key: number]: HTMLInputElement | null;
  }>;
};

export default function CoreFeatures({
  data,
  setData,
  markAsChanged,
  uploadingStates,
  handleFileChange,
  triggerFileInput,
  replaceImage,
  featureFileInputRefs,
}: CoreFeaturesProps) {
  const addFeature = () => {
    if (!data) return;

    const newData = { ...data };
    newData.features.items.push({
      title: "New Feature",
      details: ["Feature detail 1", "Feature detail 2"],
      image: "/images/placeholder.svg?height=120&width=120",
    });
    setData(newData);
    markAsChanged();
  };

  const updateFeature = (
    index: number,
    field: string,
    value: string | string[]
  ) => {
    if (!data) return;

    const newData = { ...data };
    newData.features.items[index] = {
      ...newData.features.items[index],
      [field]: value,
    };
    setData(newData);
    markAsChanged();
  };

  const removeFeature = async (index: number) => {
    if (!data) return;

    if (confirm("Are you sure you want to remove this feature?")) {
      const feature = data.features.items[index];

      // Delete image from Bunny CDN if it's a CDN URL
      if (
        feature.image &&
        feature.image.includes(process.env.NEXT_PUBLIC_BUNNY_CDN_URL || "")
      ) {
        try {
          await deleteFromBunnyCDN(feature.image);
        } catch (error) {
          console.error("Failed to delete image from CDN:", error);
        }
      }

      const newData = { ...data };
      newData.features.items.splice(index, 1);
      setData(newData);
      markAsChanged();
    }
  };

  const addFeatureDetail = (featureIndex: number) => {
    if (!data) return;

    const newData = { ...data };
    newData.features.items[featureIndex].details.push("New detail");
    setData(newData);
    markAsChanged();
  };

  const updateFeatureDetail = (
    featureIndex: number,
    detailIndex: number,
    value: string
  ) => {
    if (!data) return;

    const newData = { ...data };
    newData.features.items[featureIndex].details[detailIndex] = value;
    setData(newData);
    markAsChanged();
  };

  const removeFeatureDetail = (featureIndex: number, detailIndex: number) => {
    if (!data) return;

    const newData = { ...data };
    newData.features.items[featureIndex].details.splice(detailIndex, 1);
    setData(newData);
    markAsChanged();
  };

  return (
    <Card className="mt-8">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Core Features</CardTitle>
            <CardDescription>
              Manage the features displayed on the homepage
            </CardDescription>
          </div>
          <Button
            onClick={addFeature}
            size="sm"
            className="flex items-center space-x-1"
          >
            <Plus className="w-4 h-4" />
            <span>Add Feature</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {data.features.items.map((feature, index) => (
            <div key={index} className="border rounded-lg p-6 space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-lg">Feature {index + 1}</h3>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => removeFeature(index)}
                  className="flex items-center space-x-1"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Remove</span>
                </Button>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor={`feature-title-${index}`}>Title</Label>
                    <Input
                      id={`feature-title-${index}`}
                      value={feature.title}
                      onChange={(e) =>
                        updateFeature(index, "title", e.target.value)
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Feature Image</Label>
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        <Image
                          src={feature.image || "/images/placeholder.svg"}
                          alt={feature.title}
                          width={80}
                          height={80}
                          className="rounded-lg border object-cover"
                        />
                        {uploadingStates[`feature-${index}`] && (
                          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                            <Loader2 className="w-6 h-6 text-white animate-spin" />
                          </div>
                        )}
                      </div>

                      <div className="space-y-2 flex-1">
                        <input
                          type="file"
                          ref={(el) => {
                            featureFileInputRefs.current[index] = el;
                          }}
                          onChange={(e) =>
                            handleFileChange(e, index, "feature")
                          }
                          accept="image/*"
                          className="hidden"
                        />

                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => triggerFileInput(index, "feature")}
                            disabled={uploadingStates[`feature-${index}`]}
                            className="flex items-center space-x-2"
                          >
                            {uploadingStates[`feature-${index}`] ? (
                              <Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                              <Upload className="w-4 h-4" />
                            )}
                            <span>
                              {uploadingStates[`feature-${index}`]
                                ? "Uploading..."
                                : "Upload"}
                            </span>
                          </Button>

                          {feature.image &&
                            !feature.image.includes("placeholder") && (
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  replaceImage(index, "feature", feature.image)
                                }
                                disabled={uploadingStates[`feature-${index}`]}
                                className="flex items-center space-x-2"
                              >
                                <ImageIcon className="w-4 h-4" />
                                <span>Replace</span>
                              </Button>
                            )}
                        </div>

                        <Input
                          placeholder="Or enter image URL"
                          value={feature.image}
                          onChange={(e) =>
                            updateFeature(index, "image", e.target.value)
                          }
                          className="text-xs"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label>Feature Details</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => addFeatureDetail(index)}
                      className="flex items-center space-x-1"
                    >
                      <Plus className="w-3 h-3" />
                      <span>Add Detail</span>
                    </Button>
                  </div>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {feature.details.map((detail, detailIndex) => (
                      <div
                        key={detailIndex}
                        className="flex items-center space-x-2"
                      >
                        <Input
                          value={detail}
                          onChange={(e) =>
                            updateFeatureDetail(
                              index,
                              detailIndex,
                              e.target.value
                            )
                          }
                          placeholder="Feature detail"
                          className="text-sm"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            removeFeatureDetail(index, detailIndex)
                          }
                          className="flex-shrink-0"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
