import { NextRequest } from "next/server";
import path from "path";
import fs from "fs/promises";
import { randomUUID } from "crypto";

export class UploadHandler {
  private uploadDir = path.join(
    process.cwd(),
    "src",
    "uploads",
    "certificates"
  );

  constructor(private sessionId: string) {
    this.sessionId = sessionId;
  }

  async handleUpload(request: NextRequest) {
    try {
      const formData = await request.formData();
      const file = formData.get("file") as File;

      if (!file) {
        return { error: "No file provided" };
      }

      const filename = this.generateFilename(file.name);
      const buffer = await file.arrayBuffer();

      const storagePath = path.join(this.uploadDir, filename);
      await fs.mkdir(path.dirname(storagePath), { recursive: true });
      await fs.writeFile(storagePath, Buffer.from(buffer));

      return {
        success: true,
        message: "File uploaded successfully, queued for processing",
        data: {
          fileId: filename,
          fileUrl: `/api/upload/${filename}`,
        },
      };
    } catch (error) {
      console.error("Upload error:", error);
      return { error: "Upload failed" };
    }
  }

  private generateFilename(originalName: string): string {
    const ext = path.extname(originalName);
    const name = path.basename(originalName, ext);
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    return `${name}_${timestamp}_${random}${ext}`;
  }

  private generateFileId(): string {
    return randomUUID();
  }
}
