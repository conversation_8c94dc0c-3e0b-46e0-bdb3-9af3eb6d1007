"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Search } from "lucide-react";
import { useRouter } from "next/navigation";

export default function NoMore({ text, link }: { text: string; link: string }) {
  const router = useRouter();

  return (
    <div className="text-center py-12">
      <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
        <Search className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{text}</h3>
      <p className="text-gray-600 mb-4">
        Try adjusting your search terms or filters
      </p>
      <Button
        variant="outline"
        className="cursor-pointer"
        onClick={() => router.push(link)}
      >
        Clear Search
      </Button>
    </div>
  );
}
