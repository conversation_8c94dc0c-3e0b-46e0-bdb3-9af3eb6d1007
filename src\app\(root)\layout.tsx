import { getCMSData } from "@/lib/server/action/frontend/frontend.action";
import { auth } from "../../../auth";
import { Footer } from "./_components/layout/footer";
import Header from "./_components/layout/header";
import {
  checkActiveUser,
  checkUser,
} from "@/lib/server/action/users/user.action";
import { redirect } from "next/navigation";

export const dynamic = "force-dynamic";

export default async function HomeLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const user = await checkActiveUser();
  if (!user) {
    redirect("/setup");
  }

  const session = await auth();
  const checkSessionUser = await checkUser(session?.user.id as string);
  const data = await getCMSData();

  if (!data) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Failed to load CMS data</h2>
          <p className="text-muted-foreground mb-4">
            There was an error loading the content management data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-dvh">
      <Header
        user={session?.user}
        checkSessionUser={checkSessionUser ? true : false}
      />
      <div className="flex-1">{children}</div>
      <Footer site={data.site} contact={data.contact} />
    </div>
  );
}
