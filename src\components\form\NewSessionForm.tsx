"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { Button } from "../ui/button";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import { FormInputField } from "../form-element/input-field";
import { FormTextareaField } from "../form-element/text-area";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { FormDatePickerField } from "../form-element/date-picker";
import {
  sessionSchema,
  TSessionForm,
} from "@/lib/server/action/sessions/session.schema";
import { createSession } from "@/lib/server/action/sessions";

export default function NewSessionForm() {
  const form = useForm<TSessionForm>({
    resolver: zodResolver(sessionSchema),
    defaultValues: {
      name: "",
      startDate: new Date(),
      endDate: new Date(),
      description: "",
    },
    mode: "onChange",
  });

  const onSubmit = async (values: TSessionForm) => {
    const res = await createSession({
      ...values,
      isActive: "true",
    });
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <div className="bg-gray-50 flex items-center justify-center p-4 w-full">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">
            Create New Session
          </CardTitle>
          <CardDescription>
            Fill in the details below to create a new session
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormInputField
                control={form.control}
                name="name"
                label="Name"
                placeholder="2020-2021"
              />
              <FormDatePickerField
                control={form.control}
                name="startDate"
                label="Start Date"
                placeholder="Select a start date"
              />
              <FormDatePickerField
                control={form.control}
                name="endDate"
                label="End Date"
                placeholder="Select an end date"
              />
              <FormTextareaField
                control={form.control}
                name="description"
                label="Description (Optional)"
                placeholder="Enter session description"
              />
              <div className="flex justify-end gap-2">
                <Button type="submit" disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting ? (
                    <>
                      <Loader /> Creating Session
                    </>
                  ) : (
                    <>Create Session</>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
