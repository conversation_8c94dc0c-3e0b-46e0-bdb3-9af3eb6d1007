// import { generateCertificate } from "@/lib/server/action/certificates/certificates.action";
import React from "react";

export default async function page() {
  // const certificate = await generateCertificate({
  //   fileUrl: "cert-1.pdf",
  //   studentName: "<PERSON>",
  //   courseName: "Example Course",
  //   fields: [
  //     { name: "name", x: 80, y: 310, position: "center" },
  //     { name: "courseName", x: 80, y: 370, position: "center" },
  //     { name: "date", x: 650, y: 236, position: "left" },
  //   ],
  // });

  // if (!certificate.success) {
  //   return <div>Error generating certificate: {certificate.error}</div>;
  // }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold tracking-tight">
        Generated Certificate
      </h1>
      <p className="text-gray-700">
        Your certificate has been generated successfully. You can download it
        using the link below.
      </p>
    </div>
  );
}
