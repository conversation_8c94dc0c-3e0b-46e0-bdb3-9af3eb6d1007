import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function TopPerformingModules({
  topModules,
}: {
  topModules: {
    title: string;
    attempts: number;
    avgScore: number;
  }[];
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Top Performing Modules</CardTitle>
        <CardDescription>
          Most popular modules by attempts and scores
        </CardDescription>
      </CardHeader>
      <CardContent>
        {topModules.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No activity yet.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {topModules.map((module, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-sm">{module.title}</h4>
                  </div>
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span>{module.attempts} attempts</span>
                    <span>{module.avgScore}% avg score</span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-green-600">
                    {module.avgScore}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
