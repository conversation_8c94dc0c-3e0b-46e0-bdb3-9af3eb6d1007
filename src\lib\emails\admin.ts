import { transporter } from "../mailTransporter";

export async function adminWelcomeMail({email, firstName, password, siteName}: {email: string, firstName: string, password: string, siteName: string}) {
  await transporter.sendMail({
    from: process.env.SMTP_USER,
    to: email,
    subject: `Welcome to ${siteName}`,
    text: `Dear ${firstName},\n\nWelcome to ${siteName}! We are pleased to inform you that you've been added to our admin team.\n\nThis is your login credentials:\n\nEmail: ${email}\n\nPassword: ${password}\n\nIf you have any questions, feel free to reach out.\n\nBest regards,\nThe ${siteName} Team`,
  });
}
