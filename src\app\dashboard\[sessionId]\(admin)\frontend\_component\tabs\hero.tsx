/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import ImageUploadSection from "../image-upload";

type HeroTabProps = {
  hero: {
    title: string;
    subtitle: string;
    ctaText: string;
    ctaLink: string;
    image: string;
  };
  updateNestedData: (path: string[], value: any) => void;
};

export default function HeroTab({ hero, updateNestedData }: HeroTabProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Hero Section</CardTitle>
        <CardDescription>Manage the main hero section content</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="heroTitle">Title</Label>
          <Input
            id="heroTitle"
            value={hero.title}
            onChange={(e) =>
              updateNestedData(["hero", "title"], e.target.value)
            }
          />
        </div>
        <div>
          <Label htmlFor="heroSubtitle">Subtitle</Label>
          <Textarea
            id="heroSubtitle"
            value={hero.subtitle}
            onChange={(e) =>
              updateNestedData(["hero", "subtitle"], e.target.value)
            }
          />
        </div>
        <div>
          <Label htmlFor="heroCtaText">CTA Button Text</Label>
          <Input
            id="heroCtaText"
            value={hero.ctaText}
            onChange={(e) =>
              updateNestedData(["hero", "ctaText"], e.target.value)
            }
          />
        </div>
        <div>
          <Label htmlFor="heroCtaLink">CTA Button Link</Label>
          <Input
            id="heroCtaLink"
            value={hero.ctaLink}
            onChange={(e) =>
              updateNestedData(["hero", "ctaLink"], e.target.value)
            }
          />
        </div>

        <ImageUploadSection
          imageType="hero"
          currentImageUrl={hero.image}
          title="Hero Image"
          description="Main background or featured image for the hero section"
          width={200}
          height={120}
          updateNestedData={updateNestedData}
        />
      </CardContent>
    </Card>
  );
}
