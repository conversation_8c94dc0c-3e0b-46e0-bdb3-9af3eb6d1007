"use client";

import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "../ui/button";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import { redirect } from "next/navigation";
import {
  changeUserPasswordSchema,
  TChangePasswordForm,
} from "@/lib/server/action/users/users.schema";
import { updateAdminPassword } from "@/lib/server/action/admins";

const ChangePasswordForm = ({
  adminId,
  userEmail,
}: {
  adminId: string;
  userEmail: string;
}) => {
  const form = useForm<TChangePasswordForm>({
    resolver: zodResolver(changeUserPasswordSchema),
    defaultValues: {
      email: userEmail,
      password: "",
      confirmPassword: "",
    },
    mode: "onChange",
  });

  const onSubmit = async (values: TChangePasswordForm) => {
    const res = await updateAdminPassword(adminId, values);
    if (res.success) {
      toast.success(res.success || "Password updated");
    } else {
      toast.error(res.error || "Failed to update password");
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input {...field} type="email" disabled />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input {...field} type="password" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Confirm Password</FormLabel>
              <FormControl>
                <Input {...field} type="password" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between border-t pt-6">
          <Button variant="outline" onClick={() => redirect("/user")}>
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={form.formState.isSubmitting}
            size="sm"
            className="disabled:bg-primary/70"
          >
            {form.formState.isSubmitting ? (
              <>
                <Loader /> Updating Password
              </>
            ) : (
              <>Update Password</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default ChangePasswordForm;
