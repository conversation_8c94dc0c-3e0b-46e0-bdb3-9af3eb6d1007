"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { appConfig } from "@/config/app";
import { uploadFileToBunny } from "@/lib/bunny";
import { validateImageFile } from "@/lib/file-validators";
import { generateFileUploadUrl } from "@/lib/server/action/bunny/bunny.action";
import {
  deletePhoto,
  uploadPhoto,
} from "@/lib/server/action/users/user.action";
// import { updateAdminPhoto } from "@/lib/server/user/user.action";
// import {
//   deleteFileByType,
//   uploadImage,
// } from "@/lib/server/fileUpload/fileUpload.action";
import { Loader } from "lucide-react";
import { ChangeEvent, useRef, useState } from "react";
import { toast } from "sonner";

const ProfilePhotoCard = ({
  userId,
  avatar,
  firstName,
  lastName,
}: {
  userId: string;
  avatar: string | null;
  firstName: string;
  lastName: string;
}) => {
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      processFile(selectedFile);
    }
  };

  const processFile = (selectedFile: File) => {
    // Validate file
    const validation = validateImageFile(
      selectedFile,
      appConfig.MAX_IMAGE_SIZE_MB
    );
    if (!validation.valid) {
      toast.error(validation.error || "Invalid file");
      return;
    }

    handleUpload(selectedFile);
  };

  const handleUpload = async (fileToUpload: File) => {
    if (!fileToUpload) return;

    setUploading(true);

    try {
      const { fileId, uploadUrl, accessKey, cdnUrl } =
        await generateFileUploadUrl(fileToUpload.name);

      await uploadFileToBunny(
        fileToUpload,
        uploadUrl,
        accessKey,
        (progress) => {
          console.log(progress);
        }
      );

      const res = await uploadPhoto(userId, {
        fileId,
        fileUrl: cdnUrl,
      });
      if (res.success) {
        toast.success(res.message);
      } else {
        toast.error(res.error);
      }
    } catch (err) {
      console.error(err);
      toast.error("Upload failed");
    } finally {
      setUploading(false);
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Profile</CardTitle>
          <CardDescription>Manage your profile picture</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center space-y-4">
          <Avatar className="h-24 w-24">
            {avatar && <AvatarImage src={avatar} alt="User" />}
            <AvatarFallback>{`${firstName[0].toUpperCase()}${lastName[0].toUpperCase()}`}</AvatarFallback>
          </Avatar>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={async () => {
                const res = await deletePhoto(userId);
                if (res.success) {
                  toast.success("Removed successfully");
                } else {
                  toast.error(res.message);
                }
              }}
            >
              Remove
            </Button>
            <Button
              size="sm"
              disabled={uploading}
              onClick={() => fileInputRef.current?.click()}
              className="disabled:opacity/20"
            >
              {uploading ? (
                <>
                  <Loader /> Uploading
                </>
              ) : (
                "Upload"
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        id="userPhoto"
        name="userPhoto"
        type="file"
        accept="image/*"
        className="hidden"
        onChange={handleFileChange}
      />
    </>
  );
};

export default ProfilePhotoCard;
