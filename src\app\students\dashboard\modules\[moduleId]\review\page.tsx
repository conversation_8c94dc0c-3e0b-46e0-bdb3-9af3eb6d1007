import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { getAssessmentAttemptResult } from "@/lib/server/action/students/modules/assessments";
import { notFound } from "next/navigation";

export default async function QuizReview({
  searchParams,
}: {
  searchParams: Promise<{ attemptId: string | undefined }>;
}) {
  const { attemptId } = await searchParams;
  if (!attemptId) {
    return notFound();
  }

  const assessmentResult = await getAssessmentAttemptResult(attemptId);
  if (!assessmentResult) {
    return <div className="text-center py-12">Result not found.</div>;
  }

  const totalQuestions = assessmentResult.assessment.questions.length;

  const correctAnswers = assessmentResult.responses.filter(
    (q) => q.isCorrect
  ).length;
  const accuracy = Math.round((correctAnswers / totalQuestions) * 100);

  const getQuestionTypeColor = (type: string) => {
    switch (type) {
      case "True/False":
        return "bg-blue-100 text-blue-800";
      case "Multiple Choice":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="max-w-7xl mx-auto py-6 -mt-4">
      <Button variant="ghost" asChild className="mb-4">
        <Link href="/students/dashboard/modules">
          <ArrowLeft className="w-4 h-4" />
          Back to Modules
        </Link>
      </Button>
      <Card className="mb-6">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {assessmentResult.assessment.module.title}
              </h1>
              <p className="text-sm text-gray-600">
                Finished{" "}
                {(assessmentResult.completedAt as Date).toLocaleString()}
              </p>
            </div>
            <div className="flex items-center gap-6">
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-1">
                  <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {accuracy}%
                    </span>
                  </div>
                </div>
                <p className="text-xs text-gray-600">Accuracy</p>
              </div>
              <div className="text-center">
                <div className="flex items-center gap-1 mb-1">
                  <span className="text-lg font-semibold text-gray-900">
                    {correctAnswers}/{totalQuestions}
                  </span>
                  <div className="w-4 h-4 rounded-full border-2 border-gray-300 flex items-center justify-center">
                    <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                  </div>
                </div>
                <p className="text-xs text-gray-600">Correct Answers</p>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold text-gray-900">
            Assessment Summary:
          </h2>
        </CardHeader>
        <CardContent className="space-y-4">
          {assessmentResult.responses.map((data, index) => (
            <div key={data.id} className="border rounded-lg p-4 bg-white">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-start gap-3 mb-2">
                    <span className="font-semibold text-gray-900">
                      Q {index}.
                    </span>
                    <div className="flex-1">
                      <p className="text-gray-900 mb-2">
                        {data.question.question}
                      </p>
                      <div className="flex items-center gap-3 text-sm">
                        <Badge
                          variant="outline"
                          className={getQuestionTypeColor(
                            data.question.questionType
                          )}
                        >
                          Type: {data.question.questionType}
                        </Badge>
                        <div className="flex items-center gap-1">
                          {data.isCorrect ? (
                            <Check className="w-4 h-4 text-green-600" />
                          ) : (
                            <X className="w-4 h-4 text-red-600" />
                          )}
                          <span
                            className={
                              data.isCorrect ? "text-green-600" : "text-red-600"
                            }
                          >
                            {data.isCorrect ? "Correct" : "Incorrect"}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );
}
