"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ImageIcon, Loader2, Plus, Trash2, Upload } from "lucide-react";
import Image from "next/image";
import { CMSData } from "@/lib/server/action/frontend/frontend.action";
import {
  deleteFromBunnyCDN,
  uploadToBunnyCDN,
} from "@/lib/server/action/bunny/bunny.action";
import { useRef, useState } from "react";
import { toast } from "sonner";

type CoreFeaturesProps = {
  data: CMSData;
  setData: React.Dispatch<React.SetStateAction<CMSData>>;
  markAsChanged: () => void;
};

export default function LoginCarouselData({
  data,
  setData,
  markAsChanged,
}: CoreFeaturesProps) {
  const loginFileInputRefs = useRef<{
    [key: number]: HTMLInputElement | null;
  }>([]);
  const [uploadingStates, setUploadingStates] = useState<{
    [key: string]: boolean;
  }>({});

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      handleImageUpload(file, index);
    }
    // Reset the input value so the same file can be selected again
    event.target.value = "";
  };

  const handleImageUpload = async (file: File, index: number) => {
    const uploadKey = `login-${index}`;
    setUploadingStates((prev) => ({ ...prev, [uploadKey]: true }));

    try {
      const formData = new FormData();
      formData.append("file", file);

      const result = await uploadToBunnyCDN(formData, "cms/images");

      if (result.success && result.url) {
        updateLoginItem(index, "image", result.url);

        toast.success("Image uploaded successfully!");
      } else {
        toast.error(result.error || "Failed to upload image");
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("An error occurred while uploading the image");
    } finally {
      setUploadingStates((prev) => ({ ...prev, [uploadKey]: false }));
    }
  };

  const replaceImage = async (index: number, oldImageUrl: string) => {
    if (
      oldImageUrl &&
      oldImageUrl.includes(process.env.NEXT_PUBLIC_BUNNY_CDN_URL || "")
    ) {
      try {
        await deleteFromBunnyCDN(oldImageUrl);
      } catch (error) {
        console.error("Failed to delete old image:", error);
      }
    }

    // Trigger file input for new image
    triggerFileInput(index);
  };

  const triggerFileInput = (index: number) => {
    loginFileInputRefs.current[index]?.click();
  };

  const addLoginItem = () => {
    if (!data) return;

    const newData = { ...data };
    newData.login.items.push({
      title: "New login title",
      subtitle: "New login subtitle",
      image: "/images/placeholder.svg?height=120&width=120",
    });
    setData(newData);
    markAsChanged();
  };

  const updateLoginItem = (
    index: number,
    field: string,
    value: string | string[]
  ) => {
    if (!data) return;

    const newData = { ...data };
    newData.login.items[index] = {
      ...newData.login.items[index],
      [field]: value,
    };
    setData(newData);
    markAsChanged();
  };

  const removeFeature = async (index: number) => {
    if (!data) return;

    if (confirm("Are you sure you want to remove this feature?")) {
      const loginItem = data.login.items[index];

      // Delete image from Bunny CDN if it's a CDN URL
      if (
        loginItem.image &&
        loginItem.image.includes(process.env.NEXT_PUBLIC_BUNNY_CDN_URL || "")
      ) {
        try {
          await deleteFromBunnyCDN(loginItem.image);
        } catch (error) {
          console.error("Failed to delete image from CDN:", error);
        }
      }

      const newData = { ...data };
      newData.login.items.splice(index, 1);
      setData(newData);
      markAsChanged();
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Login Items</CardTitle>
            <CardDescription>
              Manage the features displayed on the login page
            </CardDescription>
          </div>
          <Button
            onClick={addLoginItem}
            size="sm"
            className="flex items-center space-x-1"
          >
            <Plus className="w-4 h-4" />
            <span>Add Login Item</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {data.login.items.map((item, index) => (
            <div key={index} className="border rounded-lg p-6 space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-lg">Login Item {index + 1}</h3>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => removeFeature(index)}
                  className="flex items-center space-x-1"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Remove</span>
                </Button>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor={`login-title-${index}`}>Title</Label>
                    <Input
                      id={`login-title-${index}`}
                      value={item.title}
                      onChange={(e) =>
                        updateLoginItem(index, "title", e.target.value)
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor={`login-subtitle-${index}`}>Subtitle</Label>
                    <Input
                      id={`login-subtitle-${index}`}
                      value={item.subtitle}
                      onChange={(e) =>
                        updateLoginItem(index, "subtitle", e.target.value)
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Login Image</Label>
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        <Image
                          src={item.image || "/images/placeholder.svg"}
                          alt={item.title}
                          width={80}
                          height={80}
                          className="rounded-lg border object-cover"
                        />
                        {uploadingStates[`login-${index}`] && (
                          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                            <Loader2 className="w-6 h-6 text-white animate-spin" />
                          </div>
                        )}
                      </div>

                      <div className="space-y-2 flex-1">
                        <input
                          type="file"
                          ref={(el) => {
                            loginFileInputRefs.current[index] = el;
                          }}
                          onChange={(e) => handleFileChange(e, index)}
                          accept="image/*"
                          className="hidden"
                        />

                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => triggerFileInput(index)}
                            disabled={uploadingStates[`login-${index}`]}
                            className="flex items-center space-x-2"
                          >
                            {uploadingStates[`login-${index}`] ? (
                              <Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                              <Upload className="w-4 h-4" />
                            )}
                            <span>
                              {uploadingStates[`login-${index}`]
                                ? "Uploading..."
                                : "Upload"}
                            </span>
                          </Button>

                          {item.image &&
                            !item.image.includes("placeholder") && (
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => replaceImage(index, item.image)}
                                disabled={uploadingStates[`login-${index}`]}
                                className="flex items-center space-x-2"
                              >
                                <ImageIcon className="w-4 h-4" />
                                <span>Replace</span>
                              </Button>
                            )}
                        </div>

                        <Input
                          placeholder="Or enter image URL"
                          value={item.image}
                          onChange={(e) =>
                            updateLoginItem(index, "image", e.target.value)
                          }
                          className="text-xs"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
