/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import ImageUploadSection from "../image-upload";
import LoginCarouselData from "../sections/login-carousel-data";
import { CMSData } from "@/lib/server/action/frontend/frontend.action";

type GeneralTabProps = {
  site: {
    name: string;
    logo: string;
    footerLogo: string;
  };
  contact: {
    email: string;
    mobile: string;
    landline: string;
    address: string;
  };
  onboarding: {
    welcomeMessage: string;
    dataPrivacyMessage: string;
  };
  data: CMSData;
  setData: React.Dispatch<React.SetStateAction<CMSData>>;
  markAsChanged: () => void;
  updateNestedData: (path: string[], value: any) => void;
};

export default function GeneralTab({
  site,
  contact,
  onboarding,
  data,
  setData,
  markAsChanged,
  updateNestedData,
}: GeneralTabProps) {
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Site Settings</CardTitle>
          <CardDescription>Manage general site information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="siteName">Site Name</Label>
            <Input
              id="siteName"
              value={site.name}
              onChange={(e) =>
                updateNestedData(["site", "name"], e.target.value)
              }
            />
          </div>

          <ImageUploadSection
            imageType="siteLogo"
            currentImageUrl={site.logo}
            title="Site Logo"
            description="Main logo displayed in the header"
            width={80}
            height={80}
            updateNestedData={updateNestedData}
          />

          <ImageUploadSection
            imageType="footerLogo"
            currentImageUrl={site.footerLogo}
            title="Footer Logo"
            description="Logo displayed in the footer"
            width={80}
            height={80}
            updateNestedData={updateNestedData}
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
          <CardDescription>Update contact details</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              value={contact.email}
              onChange={(e) =>
                updateNestedData(["contact", "email"], e.target.value)
              }
            />
          </div>
          <div>
            <Label htmlFor="mobile">Mobile</Label>
            <Input
              id="mobile"
              value={contact.mobile}
              onChange={(e) =>
                updateNestedData(["contact", "mobile"], e.target.value)
              }
            />
          </div>
          <div>
            <Label htmlFor="landline">Landline</Label>
            <Input
              id="landline"
              value={contact.landline}
              onChange={(e) =>
                updateNestedData(["contact", "landline"], e.target.value)
              }
            />
          </div>
          <div>
            <Label htmlFor="address">Address</Label>
            <Textarea
              id="address"
              value={contact.address}
              onChange={(e) =>
                updateNestedData(["contact", "address"], e.target.value)
              }
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Onboarding Information</CardTitle>
          <CardDescription>Update Onboarding information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="welcomeMessage">Welcome Message</Label>
            <Textarea
              id="welcomeMessage"
              value={onboarding.welcomeMessage}
              onChange={(e) =>
                updateNestedData(
                  ["onboarding", "welcomeMessage"],
                  e.target.value
                )
              }
            />
          </div>
          <div>
            <Label htmlFor="dataPrivacyMessage">Data Privacy Message</Label>
            <Textarea
              id="dataPrivacyMessage"
              value={onboarding.dataPrivacyMessage}
              onChange={(e) =>
                updateNestedData(
                  ["onboarding", "dataPrivacyMessage"],
                  e.target.value
                )
              }
            />
          </div>
        </CardContent>
      </Card>

      <LoginCarouselData
        data={data}
        setData={setData}
        markAsChanged={markAsChanged}
      />
    </>
  );
}
