'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache";
import { TTemplateFile } from "./templates.schema";
import { getActiveSession } from "../../sessions";
import { deleteFromBunny } from "../../bunny/bunny.action";


export async function createTemplate(data: TTemplateFile) {
  const activeSession = await getActiveSession()
  if (!activeSession) {
    return { success: false, error: 'No active session' }
  }

  try {
    const template = await prisma.certificateTemplate.create({
      data: {
        name: data.name,
        fileUrl: data.fileUrl,
        fileId: data.fileId,
        previewId: data.previewId,
        previewUrl: data.previewUrl,
        fields: data.fields,
        session: { connect: { id: activeSession.id } },
      },
      select: { id: true }
    })
    
    revalidatePath('/admin/certificates')

    return { success: true, message: 'Template created', templateId: template.id }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create template' }
  }
}

export async function deleteTemplate(templateId: string) {
  try {
    const template = await prisma.certificateTemplate.findUnique({
      where: { id: templateId },
      select: { fileId: true, previewId: true }
    })
    
    if (template?.fileId) {
      await deleteFromBunny(template.fileId, 'certificates')
      await deleteFromBunny(template.previewId, 'certificates')
    }

    await prisma.certificateTemplate.delete({
      where: { id: templateId }
    })
    
    revalidatePath('/admin/certificates')

    return { success: true }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete template. Already in use' }
  }
}
