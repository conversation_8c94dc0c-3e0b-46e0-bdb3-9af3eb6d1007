import { Award, TrendingUp, Users } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const Icons = {
  award: Award,
  attempts: TrendingUp,
  score: Award,
  students: Users,
};

export default function OverviewCard({
  totalModules,
  totalAttempts,
  averageScore,
  totalStudents,
}: {
  totalModules: number;
  totalAttempts: number;
  averageScore: number;
  totalStudents: number;
}) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <OverviewCardContent
        title="Total Modules"
        value={totalModules}
        icon="award"
      />
      <OverviewCardContent
        title="Total Attempts"
        value={totalAttempts}
        icon="attempts"
      />
      <OverviewCardContent
        title="Average Score"
        value={averageScore}
        icon="score"
      />
      <OverviewCardContent
        title="Active Students"
        value={totalStudents}
        icon="students"
      />
    </div>
  );
}

function OverviewCardContent({
  title,
  value,
  icon,
}: {
  title: string;
  value: number;
  icon: keyof typeof Icons;
}) {
  const Icon = Icons[icon];
  return (
    <Card className="gap-0 py-4">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
      </CardContent>
    </Card>
  );
}
