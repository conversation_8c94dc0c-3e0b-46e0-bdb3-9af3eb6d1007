import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, Settings } from "lucide-react";
import Link from "next/link";
import { getCourseTeachers } from "@/lib/server/action/courses";

export default function TeacherInfoCard({
  courseId,
  sessionId,
  teachers,
  authUserName,
  isAdmin,
}: {
  courseId: string;
  sessionId: string;
  teachers: Awaited<ReturnType<typeof getCourseTeachers>>;
  authUserName: string;
  isAdmin: boolean;
}) {
  const isPrimaryTeacher = teachers.primaryTeacherName === authUserName;

  return (
    <div className="space-y-6">
      <Card className="gap-0 py-4">
        <CardHeader>
          <CardTitle className="text-sm font-medium text-gray-600">
            Teacher
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center">
              <span className="text-black font-bold text-sm">
                {teachers.primaryTeacherName[0].toLocaleUpperCase()}
              </span>
            </div>
            <span className="font-medium">{teachers.primaryTeacherName}</span>
          </div>
        </CardContent>
      </Card>

      <Card className="gap-0 py-4">
        <CardHeader>
          <CardTitle className="text-sm font-medium text-gray-600">
            Collaborator teacher
          </CardTitle>
        </CardHeader>
        <CardContent>
          {teachers.assistantTeacherNames.map((teacher) => (
            <div key={teacher} className="flex items-center gap-3">
              <Users className="w-4 h-4 text-gray-400" />
              <span className="font-medium">{teacher}</span>
            </div>
          ))}
        </CardContent>
      </Card>

      {isAdmin || isPrimaryTeacher ? (
        <div className="space-y-3">
          <Button variant="outline" asChild className="w-full justify-start">
            <Link href={`/dashboard/${sessionId}/courses/${courseId}/settings`}>
              <Settings className="w-4 h-4 mr-3" />
              Course setup
            </Link>
          </Button>
        </div>
      ) : null}
    </div>
  );
}
