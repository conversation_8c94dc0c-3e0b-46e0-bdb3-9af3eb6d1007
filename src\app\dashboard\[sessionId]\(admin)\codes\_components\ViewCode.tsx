import { cn } from "@/lib/utils";
import OtherDetails from "../../../_components/shared/OtherDetails";
import { CodeStatus, LoginCodeType } from "@prisma/client";

type Props = {
  name: string;
  email: string;
  code: string;
  status: CodeStatus;
  type: LoginCodeType;
};

const ViewCode = ({ name, email, code, status, type }: Props) => {
  return (
    <div>
      <OtherDetails name="Name" value={name} />
      <OtherDetails name="Email" value={email} />
      <OtherDetails
        name="Exam code"
        value={code}
        textClass={cn(status === "ACTIVE" ? "text-green-500" : "text-red-500")}
      />
      <OtherDetails name="Code status" value={status} />
      <OtherDetails name="Code type" value={type} />
    </div>
  );
};

export default ViewCode;
