export type FileValidationResult = {
  valid: boolean
  error?: string
}

export const validateImageFile = (file: File, maxSizeMB = 5): FileValidationResult => {
  // Check if file is an image
  if (!file.type.startsWith("image/")) {
    return { valid: false, error: "File must be an image" }
  }

  // Check file size
  const maxSizeBytes = maxSizeMB * 1024 * 1024
  if (file.size > maxSizeBytes) {
    return { valid: false, error: `Image must be smaller than ${maxSizeMB}MB` }
  }

  return { valid: true }
}

export const validatePdfFile = (file: File, maxSizeMB = 10): FileValidationResult => {
  // Check if file is a PDF
  if (file.type !== "application/pdf") {
    return { valid: false, error: "File must be a PDF" }
  }

  // Check file size
  const maxSizeBytes = maxSizeMB * 1024 * 1024
  if (file.size > maxSizeBytes) {
    return { valid: false, error: `PDF must be smaller than ${maxSizeMB}MB` }
  }

  return { valid: true }
}

export const validateVideoFile = (file: File, maxSizeMB = 100): FileValidationResult => {
  // Check if file is a video
  if (!file.type.startsWith("video/")) {
    return { valid: false, error: "File must be a video" }
  }

  // Check file size
  const maxSizeBytes = maxSizeMB * 1024 * 1024
  if (file.size > maxSizeBytes) {
    return { valid: false, error: `Video must be smaller than ${maxSizeMB}MB` }
  }

  return { valid: true }
}

export const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return bytes + " bytes"
  else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB"
  else if (bytes < 1073741824) return (bytes / 1048576).toFixed(1) + " MB"
  else return (bytes / 1073741824).toFixed(1) + " GB"
}

