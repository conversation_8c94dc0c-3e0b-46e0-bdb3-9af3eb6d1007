/* eslint-disable @typescript-eslint/no-explicit-any */

import { Loader, Plus } from "lucide-react";
import React from "react";
import { Button } from "../ui/button";
type SubmitButtonProps = {
  title: string;
  loadingTitle?: string;
  loaderIcon?: any;
  buttonIcon?: any;
  loading: boolean;
  showIcon?: boolean;
  size?: "default" | "sm" | "lg" | "icon" | null | undefined;
};
export default function SubmitButton({
  title,
  loadingTitle = "Saving Please wait...",
  loading,
  loaderIcon = Loader,
  buttonIcon = Plus,
  showIcon = true,
}: SubmitButtonProps) {
  const LoaderIcon = loaderIcon;
  const ButtonIcon = buttonIcon;
  return (
    <>
      {loading ? (
        <Button type="button" disabled className="cursor-not-allowed">
          <LoaderIcon className="w-4 h-4 animate-spin mr-2" />
          {loadingTitle}
        </Button>
      ) : (
        <Button type="submit">
          {showIcon && <ButtonIcon className="w-4 h-4 mr-2" />}
          {title}
        </Button>
      )}
    </>
  );
}
