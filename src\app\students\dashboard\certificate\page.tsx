import { getStudentCertificate } from "@/lib/server/action/certificates/certificates.action";
import { auth } from "@/lib/server/auth";
import { Suspense } from "react";
import CertificateCard from "../_components/cards/CertificateCard";

async function SuspendedComponent() {
  const session = await auth();
  if (!session || !session.user) {
    return (
      <div className="text-red-500">
        You must be logged in to view your certificates.
      </div>
    );
  }

  const certificate = await getStudentCertificate(session.user.profileId);
  if (!certificate) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">No certificate found.</p>
      </div>
    );
  }

  return (
    <div className="py-12">
      <CertificateCard
        certificate={{
          id: certificate.id,
          fullName: certificate.fullName,
          courseName: certificate.courseName,
          fileSize: certificate.fileSize,
          issueDate: certificate.issueDate,
          issuingOrganization: certificate.issuingOrganization,
          certificateId: certificate.id,
          status: "completed",
          fileUrl: certificate.fileUrl,
        }}
      />
    </div>
  );
}

export default function CertificatesPage() {
  return (
    <>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold tracking-tight">My Certificate</h1>
      </div>
      <Suspense fallback={<div>Loading...</div>}>
        <SuspendedComponent />
      </Suspense>
    </>
  );
}
