'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"
import { TAssessmentForm } from "./assessments.schema"


export async function createAssessment(moduleId: string, data: TAssessmentForm) {
  try {
    const assessment = await prisma.assessment.create({
      data: {
        ...data,
        module: { connect: { id: moduleId } }
      },
      select: { module: { select: { course: { select: { id: true } } } } }
    })

    const courseId = assessment.module.course.id
    
    revalidatePath(`/admin/courses/${courseId}/modules/${moduleId}`)
    
    return { success: true, message: 'Assessment created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create assessment' }
  }
}

export async function updateAssessment(assessmentId: string, data: Partial<TAssessmentForm>) {
  try {
    const assessment = await prisma.assessment.update({
      where: { id: assessmentId },
      data,
      select: { module: { select: { id: true, course: { select: { id: true } } } } }
    })

    const courseId = assessment.module.course.id
    
    revalidatePath(`/admin/courses/${courseId}/modules/${assessment.module.id}`)
    
    return { success: true, message: 'Assessment updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update assessment' }
  }
}

export async function deleteAssessment(assessmentId: string) {
  try {
    const assessment = await prisma.assessment.findUnique({
      where: { id: assessmentId },
      select: { module: { select: { id: true, course: { select: { id: true } } } } }
    })
    
    if (!assessment) {
      return { success: false, error: 'Assessment not found' }
    }
    
    await prisma.assessment.delete({
      where: { id: assessmentId }
    })

    const courseId = assessment.module.course.id;
    
    revalidatePath(`/admin/courses/${courseId}/modules/${assessment.module.id}`)
    
    return { success: true }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete assessment' }
  }
}
