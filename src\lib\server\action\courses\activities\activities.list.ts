'use server'

import { appConfig } from "@/config/app"
import prisma from "@/lib/prisma"

export async function getActivities(courseId: string) {
  try {
    const activities = await prisma.activity.findMany({
      where: { courseId },
      orderBy: { createdAt: 'desc' },
      include: {
        _count: {
          select: {
            responses: true
          }
        },
        course: {
          select: {
            _count: {
              select: {
                enrollments: true
              }
            }
          }
        }
      }
    })
    
    return activities
  } catch (error) {
    console.log(error)
    return []
  }
}

export async function getActivity({activityId, page }: { activityId: string; page: number }) {
  try {
    const activity = await prisma.activity.findUnique({
      where: { id: activityId },
      include: {
        course: {
          select: {
            _count: {
              select: {
                enrollments: true
              }
            }
          }
        },
        responses: {
          select: {
            id: true,
            student: {
              select: {
                id: true,
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            },
            response: true,
            submittedAt: true,
            status: true,
            score: true
          },
          orderBy: { submittedAt: 'desc' },
          skip: (page - 1) * appConfig.ITEMS_PER_PAGE,
          take: appConfig.ITEMS_PER_PAGE
        }
      }
    })

    const totalStudentResponse = await prisma.activityResponse.count({
      where: { activityId }
    })
    
    return { activity, totalStudentResponse }
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch activity.")
  }
}
