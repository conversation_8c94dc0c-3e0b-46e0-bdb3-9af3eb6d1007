"use client";

import { Card, CardContent } from "@/components/ui/card";
import {
  getStudentReport,
  StudentReport,
} from "@/lib/server/action/students/reports/reports.list";
import { useEffect, useState } from "react";

export default function AcademicReport({ studentId }: { studentId: string }) {
  const [studentReport, setStudentReport] = useState<StudentReport | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchStudentReport() {
      try {
        setLoading(true);
        const report = await getStudentReport(studentId);
        setStudentReport(report);
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    }
    fetchStudentReport();
  }, [studentId]);

  const totalGrade =
    ((studentReport?.totalAssessmentPercentage ?? 0) +
      (studentReport?.totalActivityPercentage ?? 0)) /
    2;

  if (loading) {
    return <div className="text-center w-full h-24">Loading...</div>;
  }

  if (!studentReport) {
    return <div className="text-center w-full h-24">No report found.</div>;
  }

  const now = new Date();

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="text-center py-3 bg-gray-100 rounded-lg w-full">
        <h1 className="text-lg font-bold text-gray-900 tracking-wide">
          {studentReport.courseName}
        </h1>
      </div>

      {/* First Table - Module Details */}
      <Card className="py-0 w-full overflow-hidden">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-200">
                  <th className="text-left p-3 font-semibold text-gray-700 border-r border-gray-300">
                    Module name
                  </th>
                  <th className="text-center p-3 font-semibold text-gray-700 border-r border-gray-300 w-24">
                    Attempts
                  </th>
                  <th className="text-center p-3 font-semibold text-gray-700 w-24">
                    Grade
                  </th>
                </tr>
              </thead>
              <tbody>
                {!studentReport.modules ? (
                  <tr>
                    <td colSpan={3} className="p-3 text-center">
                      No modules found.
                    </td>
                  </tr>
                ) : (
                  studentReport.modules.map((module, index) => (
                    <tr
                      key={index}
                      className="border-b border-gray-200 hover:bg-gray-50"
                    >
                      <td className="p-3 text-sm text-gray-900 border-r border-gray-200 w-full">
                        <p className="line-clamp-1">{module.name}</p>
                      </td>
                      <td className="p-3 text-sm text-gray-700 text-center border-r border-gray-200">
                        {module.attempts}
                      </td>
                      <td className="p-1 text-sm text-gray-700 text-center">
                        {module.score.toFixed(0) + " /100"}
                      </td>
                    </tr>
                  ))
                )}
                <tr className="bg-gray-50">
                  <td className="p-3 text-sm font-semibold text-gray-900 border-r border-gray-200">
                    Average score:
                  </td>
                  <td></td>
                  <td className="p-3 text-sm font-bold text-green-600 text-center">
                    {studentReport.totalAssessmentPercentage.toFixed(0)}%
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Second Table - Activity Programs */}
      <Card className="py-0 w-full overflow-hidden">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-200">
                  <th className="text-left p-3 font-semibold text-gray-700 border-r border-gray-300">
                    Activity name
                  </th>
                  <th className="text-center p-3 font-semibold text-gray-700 w-32">
                    Grade
                  </th>
                </tr>
              </thead>
              <tbody>
                {!studentReport.activityTest ? (
                  <tr>
                    <td colSpan={2} className="p-3 text-center">
                      No achievement test found.
                    </td>
                  </tr>
                ) : (
                  studentReport.activityTest.map((activity, index) => {
                    let gradeDisplay;

                    if (activity.isGraded) {
                      gradeDisplay = `${activity.score.toFixed(0)} / ${
                        activity.passScore
                      }`;
                    } else if (new Date(activity.deadline) > now) {
                      gradeDisplay = "NG";
                    } else {
                      gradeDisplay = `0 / ${activity.passScore}`;
                    }

                    return (
                      <tr
                        key={index}
                        className="border-b border-gray-200 hover:bg-gray-50"
                      >
                        <td className="p-3 text-sm text-gray-900 border-r border-gray-200">
                          <p className=" line-clamp-1">{activity.name}</p>
                        </td>
                        <td className="p-3 text-sm text-gray-700 text-center">
                          {gradeDisplay}
                        </td>
                      </tr>
                    );
                  })
                )}
                <tr className="bg-gray-50">
                  <td className="p-3 text-sm font-semibold text-gray-900 border-r border-gray-200">
                    Average score:
                  </td>
                  <td className="p-3 text-sm font-bold text-green-600 text-center">
                    {studentReport.totalActivityPercentage.toFixed(0)}%
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <p className="text-center text-sm font-medium">
        Initial Grade: {Math.round(totalGrade)}%
      </p>
    </div>
  );
}
