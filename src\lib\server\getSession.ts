export type Session = {
  id: string;
  name: string;
  description: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
};

export async function getSession(): Promise<Session | null> {
  const session: Session = {
    // id: "1",
    // name: "Session 1",
    // description: "This is the first session",
    // startDate: new Date(),
    // endDate: new Date(),
    // isActive: true,
  } as Session;

  return session;
}