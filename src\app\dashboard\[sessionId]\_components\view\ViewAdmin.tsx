import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import OtherDetails from "../shared/OtherDetails";

type Props = {
  adminPhotoUrl?: string | null;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
};

const ViewAdmin = ({
  adminPhotoUrl,
  firstName,
  lastName,
  email,
  phone,
}: Props) => {
  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex flex-col items-center">
        {adminPhotoUrl ? (
          <div className="relative w-14 h-14 rounded-full overflow-hidden">
            <Image
              src={adminPhotoUrl ?? "/images/avatar.jpg"}
              alt="photo"
              fill
              className="object-cover"
            />
          </div>
        ) : (
          <div className="w-14 h-14 rounded-full bg-gray-200 flex justify-center items-center">
            {firstName[0].toLocaleUpperCase() + lastName[0].toLocaleUpperCase()}
          </div>
        )}
        <h2 className="text-center font-medium text-base">{`${firstName} ${lastName}`}</h2>
        <p className="text-center font-medium text-xs text-muted-foreground">
          {email}
        </p>
      </div>
      <Separator />
      <div>
        <OtherDetails name="Phone" value={phone} />
      </div>
    </div>
  );
};

export default ViewAdmin;
