import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

type RecentApprovedProps = {
  recentlyApproved?: {
    id: string;
    name: string;
    school?: string;
    type: string;
    time: string;
  }[];
};

export default function RecentApproved({
  recentlyApproved,
}: RecentApprovedProps) {
  return (
    <section>
      <h2 className="text-lg font-semibold mb-4">Recently approved</h2>
      <Card className="py-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>School</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Time</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!recentlyApproved ? (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  Loading...
                </TableCell>
              </TableRow>
            ) : recentlyApproved.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  No recently approved users found.
                </TableCell>
              </TableRow>
            ) : (
              <>
                {recentlyApproved.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="flex items-center gap-2">
                      <Avatar className="w-8 h-8">
                        <AvatarFallback>
                          {item.name
                            .split(" ")
                            .filter(Boolean)
                            .filter(
                              (_, i, arr) => i === 0 || i === arr.length - 1
                            )
                            .map((word) => word[0])
                            .join("")
                            .toLocaleUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      {item.name}
                    </TableCell>
                    <TableCell>{item.school || "Unknown"}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          item.type === "Teacher" ? "default" : "secondary"
                        }
                      >
                        {item.type}
                      </Badge>
                    </TableCell>
                    <TableCell>{item.time}</TableCell>
                  </TableRow>
                ))}
              </>
            )}
          </TableBody>
        </Table>
      </Card>
    </section>
  );
}
