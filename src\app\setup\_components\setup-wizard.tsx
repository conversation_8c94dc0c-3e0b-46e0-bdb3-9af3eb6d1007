"use client";

import type React from "react";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon, Check } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { setupAction } from "@/lib/server/action/setup/setup.action";
import { useRouter } from "next/navigation";

interface AdminData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
}

interface SessionData {
  name: string;
  startDate: Date | undefined;
  endDate: Date | undefined;
  description: string;
}

export default function InitializationWizard() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [adminData, setAdminData] = useState<AdminData>({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    phone: "",
  });
  const [sessionData, setSessionData] = useState<SessionData>({
    name: "",
    startDate: new Date(),
    endDate: new Date(),
    description: "",
  });
  const router = useRouter();

  const steps = [
    {
      number: 1,
      title: "Create Admin",
      description: "Set up administrator account",
    },
    {
      number: 2,
      title: "Create Session",
      description: "Configure initial session",
    },
  ];

  const handleAdminSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentStep(2);
  };

  const handleSessionSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    const res = await setupAction({ adminData, sessionData });
    if (res.success) {
      toast.success(res.message);
      toast.success("Initialization completed successfully!");
      router.push("/");
    } else {
      toast.error(res.error);
    }

    setIsSubmitting(false);
  };

  const handleBack = () => {
    setCurrentStep(1);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to Your App Setup
          </h1>
          <p className="text-gray-600 text-lg">
            Let&apos;s get your application configured in just a few steps
          </p>
        </div>

        {/* Step Indicator */}
        <div className="flex justify-center mb-8">
          <div className="flex items-center space-x-4">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div
                    className={cn(
                      "w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium border-2",
                      currentStep === step.number
                        ? "bg-purple-600 text-white border-purple-600"
                        : currentStep > step.number
                        ? "bg-green-500 text-white border-green-500"
                        : "bg-white text-gray-400 border-gray-300"
                    )}
                  >
                    {currentStep > step.number ? (
                      <Check className="w-5 h-5" />
                    ) : (
                      step.number
                    )}
                  </div>
                  <div className="mt-2 text-center">
                    <div
                      className={cn(
                        "text-sm font-medium",
                        currentStep >= step.number
                          ? "text-gray-900"
                          : "text-gray-400"
                      )}
                    >
                      {step.title}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {step.description}
                    </div>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={cn(
                      "w-16 h-0.5 mx-4 mt-[-2rem]",
                      currentStep > step.number ? "bg-green-500" : "bg-gray-300"
                    )}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form Content */}
        <div className="flex justify-center">
          <Card className="w-full max-w-2xl shadow-lg">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-semibold text-gray-900">
                {steps[currentStep - 1].title}
              </CardTitle>
              <p className="text-gray-600 mt-2">
                {steps[currentStep - 1].description}
              </p>
            </CardHeader>
            <CardContent className="px-8 pb-8">
              {currentStep === 1 ? (
                <form onSubmit={handleAdminSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First Name</Label>
                      <Input
                        id="firstName"
                        value={adminData.firstName}
                        onChange={(e) =>
                          setAdminData({
                            ...adminData,
                            firstName: e.target.value,
                          })
                        }
                        className="h-12"
                        placeholder="John"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input
                        id="lastName"
                        value={adminData.lastName}
                        onChange={(e) =>
                          setAdminData({
                            ...adminData,
                            lastName: e.target.value,
                          })
                        }
                        className="h-12"
                        placeholder="Doe"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={adminData.email}
                        onChange={(e) =>
                          setAdminData({ ...adminData, email: e.target.value })
                        }
                        className="h-12"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="password">Password</Label>
                      <Input
                        id="password"
                        type="password"
                        value={adminData.password}
                        onChange={(e) =>
                          setAdminData({
                            ...adminData,
                            password: e.target.value,
                          })
                        }
                        className="h-12"
                        placeholder="••••••••"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      value={adminData.phone}
                      onChange={(e) =>
                        setAdminData({ ...adminData, phone: e.target.value })
                      }
                      className="h-12"
                      placeholder="(*************"
                      required
                    />
                  </div>

                  <div className="flex justify-end pt-6">
                    <Button
                      type="submit"
                      className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3"
                    >
                      Continue to Session Setup
                    </Button>
                  </div>
                </form>
              ) : (
                <form onSubmit={handleSessionSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="sessionName">Session Name</Label>
                    <Input
                      id="sessionName"
                      value={sessionData.name}
                      onChange={(e) =>
                        setSessionData({ ...sessionData, name: e.target.value })
                      }
                      placeholder="2024-2025"
                      className="h-12"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label>Start Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full h-12 justify-start text-left font-normal",
                              !sessionData.startDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {sessionData.startDate
                              ? format(sessionData.startDate, "MMMM do, yyyy")
                              : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={sessionData.startDate}
                            onSelect={(date) =>
                              setSessionData({
                                ...sessionData,
                                startDate: date,
                              })
                            }
                            autoFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    <div className="space-y-2">
                      <Label>End Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full h-12 justify-start text-left font-normal",
                              !sessionData.endDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {sessionData.endDate
                              ? format(sessionData.endDate, "MMMM do, yyyy")
                              : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={sessionData.endDate}
                            onSelect={(date) =>
                              setSessionData({ ...sessionData, endDate: date })
                            }
                            autoFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={sessionData.description}
                      onChange={(e) =>
                        setSessionData({
                          ...sessionData,
                          description: e.target.value,
                        })
                      }
                      placeholder="Enter session description"
                      className="min-h-[100px] resize-none"
                    />
                  </div>

                  <div className="flex justify-between pt-6">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleBack}
                      className="px-8 py-3 bg-transparent"
                    >
                      Back
                    </Button>
                    <Button
                      type="submit"
                      className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Setting up..." : "Complete Setup"}
                    </Button>
                  </div>
                </form>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
