import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import Link from "next/link";

type StatisticsProps = {
  counts?: {
    totalStudents: number;
    totalTeachers: number;
    totalCourses: number;
    totalProgramLevels: number;
    totalCodes: number;
  };
  sessionId: string;
};

export default function Statistics({ counts, sessionId }: StatisticsProps) {
  return (
    <div className="space-y-6">
      {!counts ? (
        <div className="flex justify-center items-center py-12 w-full">
          <Loader2 className="animate-spin" />
        </div>
      ) : (
        <div className="space-y-4">
          <StatisticsCard
            title="Total students"
            value={counts.totalStudents || 0}
            link={`/dashboard/${sessionId}/students`}
          />
          <StatisticsCard
            title="Total teachers"
            value={counts.totalTeachers || 0}
            link={`/dashboard/${sessionId}/teachers`}
          />
          <StatisticsCard
            title="Total courses"
            value={counts.totalCourses || 0}
            link={`/dashboard/${sessionId}/courses`}
          />
          <StatisticsCard
            title="Total programs"
            value={counts.totalProgramLevels}
            link={`/dashboard/${sessionId}/programs`}
          />
          <StatisticsCard
            title="Total codes"
            value={counts.totalCodes}
            link={`/dashboard/${sessionId}/codes`}
          />
        </div>
      )}
    </div>
  );
}

function StatisticsCard({
  title,
  value,
  link,
}: {
  title: string;
  value: number;
  link: string;
}) {
  return (
    <Card className="gap-0 py-4">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value.toLocaleString()}</div>
        <Button variant="link" className="p-0 h-auto text-blue-600" asChild>
          <Link href={link}>View details</Link>
        </Button>
      </CardContent>
    </Card>
  );
}
