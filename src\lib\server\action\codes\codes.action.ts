'use server'

import prisma from "@/lib/prisma";
import { TCode } from "./codes.schema";


export async function updateCode (codeId: string, data: Pick<TCode, 'status'>) {
  try {
    const code = await prisma.code.update({
      where: { id: codeId },
      data: {
        status: data.status,
      },
    });

    return { success: true, code };
  } catch (error) {
    console.error("Error updating code:", error);
    return { success: false, error: "Failed to update code" };
  }
};

export const deleteCode = async (codeId: string) => {
  try {
    await prisma.code.delete({
      where: { id: codeId },
    });

    return { success: true };
  } catch (error) {
    console.error("Error deleting code:", error);
    return { success: false, error: "Failed to delete code" };
  }
};

export async function deactivateCode (codeId: string) {
  try {
    const code = await prisma.code.update({
      where: { id: codeId },
      data: {
        status: "INACTIVE",
      },
    });

    return { success: true, code };
  } catch (error) {
    console.error("Error deactivating code:", error);
    return { success: false, error: "Failed to deactivate code" };
  }
}