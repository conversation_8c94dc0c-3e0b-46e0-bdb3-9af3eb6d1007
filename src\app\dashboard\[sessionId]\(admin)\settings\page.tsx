import { Suspense } from "react";
import PageWrapper from "../../_components/layout/PageWrapper";
import Loading from "../../_components/Loading";
import Image from "next/image";
import SettingsFrom from "@/components/form/SettingsForm";
import { getGeneralSettings } from "@/lib/server/action/settings";

const breadcrumbItems = [
  { label: "Home", href: "/dashboard" },
  { label: "Settings" },
];

// const pageAssess = ["super-admin"];

async function SettingsComponent() {
  // await checkAssess(pageAssess);
  const settings = await getGeneralSettings();
  if (!settings) return <div>Settings not found</div>;

  return (
    <div className="flex flex-col md:flex-row gap-6 py-4">
      <SettingsFrom
        generalSettingsData={{
          siteTitle: settings.siteTitle,
          siteName: settings.siteName,
          siteAddress: settings.siteAddress,
          phone1: settings.phone1 ?? "",
          phone2: settings.phone2 ?? "",
          landline1: settings.landline1 ?? "",
          landline2: settings.landline2 ?? "",
          email: settings.email,
          address: settings.address ?? "",
        }}
        id={settings.id}
        socials={JSON.parse(settings.socials as string)}
      />

      <div className="space-y-6">
        <div className="border rounded-lg p-4 flex items-center gap-x-6">
          {/* <FormImageUpload
            name="icon"
            id={settings.id}
            label="Site icon"
            type="icon"
          /> */}
          <div className="flex items-center">
            <Image
              src={settings.iconUrl ?? "/images/logo1.png"}
              alt="logo"
              width={50}
              height={50}
              className="object-contain"
            />
          </div>
        </div>

        <div className="border rounded-lg p-4 flex items-center gap-x-6">
          {/* <FormImageUpload
            name="logo"
            id={settings.id}
            label="Site logo"
            type="logo"
          /> */}
          <div className="flex items-center">
            <Image
              src={settings.logoUrl ?? "/images/logo/full-logo.png"}
              alt="logo"
              width={150}
              height={64}
              className="object-contain"
            />
          </div>
        </div>

        <div className="space-y-2 border rounded-lg p-4">
          {/* <VideoUpload
            name="video"
            id={settings.id}
            label="Landing page video"
          /> */}
          <div className="w-full max-w-[300px] aspect-video bg-gray-200 rounded-md overflow-hidden relative">
            {/* {settings.userIntoVideoUrl ? (
              <NextVideoPlayer src={settings.userIntoVideoUrl} />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="absolute right-4 bottom-4">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M8 5V19L19 12L8 5Z" fill="white" />
                  </svg>
                </div>
              </div>
            )} */}
          </div>
        </div>
      </div>
    </div>
  );
}

const SettingsPage = () => {
  return (
    <PageWrapper
      pgTitle="Settings"
      breadcrumbItems={breadcrumbItems}
      pgHeading="General settings"
    >
      <Suspense fallback={<Loading />}>
        <SettingsComponent />
      </Suspense>
    </PageWrapper>
  );
};

export default SettingsPage;
