"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ActivityStatus } from "@prisma/client";
import ActivityModal from "../../../_components/activity-modal";

type StudentResponse = {
  id: string;
  student: {
    id: string;
    user: {
      firstName: string;
      lastName: string;
    };
  };
  response: string;
  submittedAt: Date;
  status: ActivityStatus;
  score: number | null;
};

export const columns: ColumnDef<StudentResponse>[] = [
  {
    accessorKey: "name",
    header: () => <div className="ml-2">Name</div>,
    cell: ({ row }) => {
      return (
        <div className="ml-2 flex gap-3 items-center">
          <Avatar>
            <AvatarFallback>
              {row.original.student.user.firstName.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div className="font-medium truncate">{`${row.original.student.user.firstName} ${row.original.student.user.lastName}`}</div>
        </div>
      );
    },
  },
  {
    accessorKey: "score",
    header: "Score",
    cell: ({ row }) => row.original.score,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => (
      <div>
        <Badge
          className={`text-white ${
            row.original.status === "GRADED" ? "bg-green-500" : "bg-gray-500"
          }`}
        >
          {row.original.status === "GRADED" ? "Graded" : "Not yet"}
        </Badge>
      </div>
    ),
  },
  {
    accessorKey: "submittedAt",
    header: "Submitted At",
    cell: ({ row }) => row.original.submittedAt.toDateString(),
  },
  {
    accessorKey: "action",
    cell: ({ row }) => {
      const response = row.original;

      return (
        <ActivityModal
          response={{
            id: response.id,
            response: response.response,
            submittedAt: response.submittedAt,
            status: response.status,
            score: response.score,
          }}
        />
      );
    },
  },
];
