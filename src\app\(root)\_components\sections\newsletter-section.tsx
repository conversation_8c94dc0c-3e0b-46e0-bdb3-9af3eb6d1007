import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

type NewsletterSectionProps = {
  newsletterTitle: string;
  newsletterPlaceholder: string;
  newsletterButtonText: string;
};

export function NewsletterSection({
  newsletterTitle,
  newsletterPlaceholder,
  newsletterButtonText,
}: NewsletterSectionProps) {
  return (
    <section className="py-16 bg-primary/60">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-2xl font-bold text-white mb-8">
          {newsletterTitle}
        </h2>

        <div className="max-w-md mx-auto flex gap-2">
          <Input
            type="email"
            placeholder={newsletterPlaceholder}
            className="bg-white"
          />
          <Button
            variant="secondary"
            className="bg-gray-800 text-white hover:bg-gray-700"
          >
            {newsletterButtonText}
          </Button>
        </div>
      </div>
    </section>
  );
}
