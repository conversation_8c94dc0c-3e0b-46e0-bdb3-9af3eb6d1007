'use server'

import { appConfig } from "@/config/app";
import prisma from "@/lib/prisma";
import { Prisma } from "@prisma/client";

export type SchoolWithExtra = Prisma.SchoolGetPayload<{
  include: {
    _count: {
      select: {
        students: true,
        teachers: true,
      },
    },
  };
}>;

export async function getSchools({
  sessionId,
  page,
}: {
  sessionId: string;
  page: number;
}) {
  try {
    const schools = await prisma.school.findMany({
      where: { sessionId },
      include: {
        _count: {
          select: {
            students: true,
            teachers: true,
          },
        },
      },
      orderBy: [{ name: "asc" }],
      skip: (page - 1) * appConfig.ITEMS_PER_PAGE,
      take: appConfig.ITEMS_PER_PAGE,
    })

    const total = await prisma.school.count();

    return { schools, total };
  } catch (error) {
    console.error("Error fetching schools:", error);
    throw new Error("Failed to fetch schools.");
  }
}

export async function getSchoolOptions(sessionId: string) {
  const schools = await prisma.school.findMany({
    where: { sessionId },
    select: {
      id: true,
      name: true,
    },
  });

  const schoolOptions = schools.map((school) => ({
    label: school.name,
    value: school.id,
  }));

  return schoolOptions;
}

export async function getSchool(schoolId: string) {
  try {
    const school = await prisma.school.findUnique({
      where: { id: schoolId },
      include: {
        students: {
          include: {
            user: true,
          }
        },
        teachers: {
          include: {
            user: true,
          }
        },
      },
    });

    return school;
  } catch (error) {
    console.error("Error fetching school:", error);
    throw new Error("Failed to fetch school.");
  }
}
