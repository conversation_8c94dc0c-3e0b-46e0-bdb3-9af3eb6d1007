/* eslint-disable @typescript-eslint/no-explicit-any */
'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"
import { trackModuleProgress } from "../../progress-tracking/progress-tracking.action"

// Types
interface StartAssessmentData {
  assessmentId: string
  studentId: string
}

interface SubmitAnswerData {
  attemptId: string
  questionId: string
  response: any
}

interface CompleteAssessmentData {
  attemptId: string
}

// Assessment Actions
export async function startAssessment(data: StartAssessmentData) {
  try {
    const assessment = await prisma.assessment.findUnique({
      where: { id: data.assessmentId },
      select: {
        attempts: {
          where: { studentId: data.studentId },
          select: {
            id: true,
            status: true,
            questionOrder: true
          },
          orderBy: { attemptNumber: 'desc' },
          take: 1
        },
        moduleId: true,
        maxStudentQuestions: true,
      }
    })
    
    if (!assessment) {
      return { success: false, error: 'Assessment not found' }
    }

    if (assessment.attempts.length > 0 && assessment.attempts[0].status === 'IN_PROGRESS') {
      return { success: true, message: 'Continued assessment', attempt: assessment.attempts[0] }
    }

    const questionOrder = Array.from({ length: assessment.maxStudentQuestions }, (_, i) => i)
      .sort(() => Math.random() - 0.5)

    const attempt = await prisma.assessmentAttempt.create({
      data: {
        studentId: data.studentId,
        assessmentId: data.assessmentId,
        attemptNumber: await getPreviousAttemptNumber(data.assessmentId, data.studentId) + 1,
        status: 'IN_PROGRESS',
        questionOrder: JSON.stringify(questionOrder)
      },
      select: {
        id: true,
        status: true,
        questionOrder: true
      }
    })

    // get passed assessment attempt 
    const passedAttempt = await prisma.assessmentAttempt.findFirst({
      where: {
        studentId: data.studentId,
        assessmentId: data.assessmentId,
        passed: true
      },
      orderBy: { attemptNumber: 'desc' }
    })

    if (!passedAttempt) {
      await trackModuleProgress(data.studentId, assessment.moduleId, 50)
    }
    
    return { success: true, message: 'Assessment started', attempt }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to start assessment' }
  }
}

// export async function getPreviousAnswers(attemptId: string) {
//   const responses = await prisma.questionResponse.findMany({
//     where: { attemptId },
//     // include: { question: true }
//   })
  
//   return responses
// }

async function getPreviousAttemptNumber(assessmentId: string, studentId: string) {
  const lastAttempt = await prisma.assessmentAttempt.findFirst({
    where: { assessmentId, studentId },
    orderBy: { attemptNumber: 'desc' }
  })
  
  return lastAttempt ? lastAttempt.attemptNumber : 0
}

export async function submitAnswer(data: SubmitAnswerData) {
  try {
    const question = await prisma.question.findUnique({
      where: { id: data.questionId }
    })
    
    if (!question) {
      return { success: false, error: 'Question not found' }
    }
    
    // Calculate if answer is correct
    const isCorrect = checkAnswer(question, data.response)
    const pointsEarned = isCorrect ? question.points : 0
    
    const response = await prisma.questionResponse.upsert({
      where: {
        attemptId_questionId: {
          attemptId: data.attemptId,
          questionId: data.questionId
        }
      },
      update: {
        response: data.response,
        isCorrect,
        pointsEarned,
      },
      create: {
        attemptId: data.attemptId,
        questionId: data.questionId,
        response: data.response,
        isCorrect,
        pointsEarned
      }
    })
    
    return { success: true, response }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to submit answer' }
  }
}

export async function completeAssessment(data: CompleteAssessmentData) {
  try {
    // Calculate total score
    const responses = await prisma.questionResponse.findMany({
      where: { attemptId: data.attemptId },
      include: { question: true }
    })
    
    const totalPoints = responses.reduce((sum, r) => sum + r.question.points, 0)
    const earnedPoints = responses.reduce((sum, r) => sum + (r.pointsEarned || 0), 0)
    const score = totalPoints > 0 ? (earnedPoints / totalPoints) * 100 : 0
    
    const attempt = await prisma.assessmentAttempt.findUnique({
      where: { id: data.attemptId },
      include: { assessment: true }
    })
    
    if (!attempt) {
      return { success: false, error: 'Attempt not found' }
    }
    
    const passed = score >= attempt.assessment.passingScore
    const timeSpent = Math.floor((Date.now() - attempt.startedAt.getTime()) / 1000 / 60) // minutes
    
    const updatedAttempt = await prisma.assessmentAttempt.update({
      where: { id: data.attemptId },
      data: {
        status: 'COMPLETED',
        completedAt: new Date(),
        score,
        totalPoints,
        passed,
        timeSpent,
      },
      select: {
        studentId: true,
        assessment: {
          select: {
            moduleId: true
          }
        }
      }
    })
    
    // Update module progress
    if (passed) {
      await trackModuleProgress(updatedAttempt.studentId, updatedAttempt.assessment.moduleId, 100, true)
    }

    revalidatePath('/student/courses')

    return { success: true, message: 'Assessment submitted', attempt: updatedAttempt }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to complete assessment' }
  }
}

// Utility function to check answers
function checkAnswer(question: any, response: any): boolean {
  const correctAnswer = question.correctAnswer
  
  switch (question.questionType) {
    case 'multiple-choice':
      return response.toLowerCase() === correctAnswer.toLowerCase()
    case 'true-false':
      return response === correctAnswer
    default:
      return false
  }
}
