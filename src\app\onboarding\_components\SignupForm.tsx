import { FormInput<PERSON>ield } from "@/components/form-element/input-field";
import { FormComboboxField } from "@/components/form-element/select-search";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { getCourseOptions } from "@/lib/server/action/courses";
import { getProgramOptions } from "@/lib/server/action/programs";
import { getSchoolOptions } from "@/lib/server/action/schools";
import { createStudent } from "@/lib/server/action/students";
import {
  studentSchema,
  TStudentForm,
} from "@/lib/server/action/students/students.schema";
import { createTeacher } from "@/lib/server/action/teachers";
import {
  teacherSchema,
  TTeacherForm,
} from "@/lib/server/action/teachers/teacher.schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

type FormData = TStudentForm | TTeacherForm;

export default function SignupForm({
  handleSubmit,
  handleBack,
  type,
  sessionId,
}: {
  handleSubmit: () => void;
  handleBack: () => void;
  type: "teacher" | "student";
  sessionId: string;
}) {
  const [availableCourses, setAvailableCourses] = useState<
    { label: string; value: string }[]
  >([]);
  const [schools, setSchools] = useState<{ label: string; value: string }[]>(
    []
  );
  const [programs, setPrograms] = useState<{ label: string; value: string }[]>(
    []
  );

  const form = useForm<FormData>({
    resolver: zodResolver(type === "teacher" ? teacherSchema : studentSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      school: "",
      course: "",
      program: "",
    },
  });

  // Watch for program changes to update available courses
  const selectedProgram = form.watch("program");

  useEffect(() => {
    async function getOptions() {
      const [schoolOptions, programOptions] = await Promise.all([
        getSchoolOptions(sessionId as string),
        getProgramOptions(sessionId as string),
      ]);
      setSchools(schoolOptions);
      setPrograms(programOptions);
    }
    getOptions();
  }, [sessionId]);

  useEffect(() => {
    if (selectedProgram) {
      async function fetchCourses() {
        const res = await getCourseOptions(selectedProgram);
        setAvailableCourses(res);
      }
      fetchCourses();
      // form.setValue("course", "");
    } else {
      setAvailableCourses([]);
    }
  }, [selectedProgram, form]);

  async function onSubmit(values: FormData) {
    const res =
      type === "teacher"
        ? await createTeacher(values, sessionId as string, false)
        : await createStudent(
            values as TStudentForm,
            sessionId as string,
            false
          );
    if (res.success) {
      toast.success(res.message || "Successful");
      handleSubmit();
    } else {
      toast.error(res.message || "Failed to create account");
    }
    form.reset();
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">
        {type === "teacher" ? "Teacher" : "Student"} Sign up
      </h2>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInputField
              control={form.control}
              name="firstName"
              label="First Name"
              placeholder="Enter first name"
            />
            <FormInputField
              control={form.control}
              name="lastName"
              label="Last Name"
              placeholder="Enter last name"
            />
            <FormInputField
              control={form.control}
              name="email"
              label="Email"
              type="email"
              placeholder="<EMAIL>"
            />
            <FormInputField
              control={form.control}
              name="phone"
              label="Phone"
              placeholder="(*************"
            />
            <FormComboboxField
              control={form.control}
              name="school"
              label="School"
              placeholder="Select a school"
              options={schools}
            />
            {type === "student" && (
              <FormComboboxField
                control={form.control}
                name="program"
                label="Program"
                placeholder="Select a program"
                options={programs}
              />
            )}
          </div>

          {type === "student" && (
            <FormComboboxField
              control={form.control}
              name="course"
              label="Course"
              placeholder="Select a course"
              options={availableCourses}
              disabled={!selectedProgram}
            />
          )}

          <div className="flex justify-between pt-6">
            <Button
              variant="outline"
              onClick={handleBack}
              className="px-6 bg-transparent"
            >
              Back
            </Button>
            <Button type="submit" disabled={form.formState.isSubmitting}>
              {form.formState.isSubmitting ? (
                <>
                  <Loader />
                </>
              ) : (
                <>Continue</>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
