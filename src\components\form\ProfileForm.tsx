"use client";

import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "../ui/button";
import { Loader } from "lucide-react";
import {
  TUpdateUserForm,
  updateUserSchema,
} from "@/lib/server/action/users/users.schema";
import { updateUser } from "@/lib/server/action/users/user.action";
import { toast } from "sonner";

const ProfileForm = ({
  userData,
  userId,
}: {
  userData: TUpdateUserForm;
  userId: string;
}) => {
  const form = useForm<TUpdateUserForm>({
    resolver: zodResolver(updateUserSchema),
    defaultValues: userData,
    mode: "onChange",
  });

  const onSubmit = async (values: TUpdateUserForm) => {
    const res = await updateUser(userId, values);
    if (res) {
      toast.success("Profile updated successfully");
    } else {
      toast.error("Failed to update profile");
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="John" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Last Name</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Doe" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="email"
                  placeholder="<EMAIL>"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone</FormLabel>
              <FormControl>
                <Input {...field} placeholder="(880) 1234 234" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between border-t pt-6">
          <Button variant="outline">Cancel</Button>
          <Button
            type="submit"
            disabled={form.formState.isSubmitting}
            size="sm"
            className="disabled:bg-primary/70"
          >
            {form.formState.isSubmitting ? (
              <>
                <Loader /> Saving Changes
              </>
            ) : (
              <>Save Changes</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default ProfileForm;
