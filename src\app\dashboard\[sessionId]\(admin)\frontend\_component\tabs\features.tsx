/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

type FeaturesTabProps = {
  features: {
    title: string;
    description?: string;
  };
  newsletter: {
    title: string;
    placeholder: string;
    buttonText: string;
  };
  updateNestedData: (path: string[], value: any) => void;
};

export default function FeaturesTab({
  features,
  newsletter,
  updateNestedData,
}: FeaturesTabProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Features Section</CardTitle>
        <CardDescription>
          Manage features and newsletter content
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="featuresTitle">Features Title</Label>
          <Input
            id="featuresTitle"
            value={features.title}
            onChange={(e) =>
              updateNestedData(["features", "title"], e.target.value)
            }
          />
        </div>
        <div>
          <Label htmlFor="featuresDescription">Features Description</Label>
          <Textarea
            id="featuresDescription"
            value={features.description}
            onChange={(e) =>
              updateNestedData(["features", "description"], e.target.value)
            }
          />
        </div>
        <div>
          <Label htmlFor="newsletterTitle">Newsletter Title</Label>
          <Input
            id="newsletterTitle"
            value={newsletter.title}
            onChange={(e) =>
              updateNestedData(["newsletter", "title"], e.target.value)
            }
          />
        </div>
        <div>
          <Label htmlFor="newsletterPlaceholder">Newsletter Placeholder</Label>
          <Input
            id="newsletterPlaceholder"
            value={newsletter.placeholder}
            onChange={(e) =>
              updateNestedData(["newsletter", "placeholder"], e.target.value)
            }
          />
        </div>
        <div>
          <Label htmlFor="newsletterButton">Newsletter Button Text</Label>
          <Input
            id="newsletterButton"
            value={newsletter.buttonText}
            onChange={(e) =>
              updateNestedData(["newsletter", "buttonText"], e.target.value)
            }
          />
        </div>
      </CardContent>
    </Card>
  );
}
