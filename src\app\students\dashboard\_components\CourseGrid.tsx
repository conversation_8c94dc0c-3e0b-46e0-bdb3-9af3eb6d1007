import Link from "next/link";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Clock, Users, BookOpen } from "lucide-react";
import Image from "next/image";

const courses = [
  {
    id: 1,
    title: "Complete React Development Course",
    description:
      "Master React from basics to advanced concepts with hands-on projects",
    category: "Programming",
    level: "Intermediate",
    duration: "40 hours",
    students: 1250,
    rating: 4.8,
    progress: 65,
    modules: 12,
    image: "/placeholder.svg?height=200&width=300",
    instructor: "<PERSON>",
  },
  {
    id: 2,
    title: "UI/UX Design Fundamentals",
    description:
      "Learn the principles of user interface and user experience design",
    category: "Design",
    level: "Beginner",
    duration: "25 hours",
    students: 890,
    rating: 4.6,
    progress: 30,
    modules: 8,
    image: "/placeholder.svg?height=200&width=300",
    instructor: "<PERSON>",
  },
];

export function CoursesGrid({
  studentId,
  enrollments,
}: {
  studentId: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  enrollments: any;
}) {
  console.log(enrollments);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {courses.map((course) => (
        <Link
          key={course.id}
          href={`/students/${studentId}/courses/${course.id}`}
        >
          <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer py-0 gap-0">
            <CardHeader className="p-0">
              <div className="relative">
                <Image
                  src={"/images/placeholder.svg"}
                  alt={course.title}
                  width={500}
                  height={400}
                  className="w-full h-48 object-cover rounded-t-lg"
                />
                <Badge className="absolute top-3 left-3" variant="secondary">
                  {course.category}
                </Badge>
                <Badge
                  className="absolute top-3 right-3"
                  variant={
                    course.level === "Beginner"
                      ? "default"
                      : course.level === "Intermediate"
                      ? "secondary"
                      : "destructive"
                  }
                >
                  {course.level}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="p-4">
              <h3 className="font-semibold text-lg mb-2 line-clamp-2">
                {course.title}
              </h3>
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                {course.description}
              </p>
              <p className="text-sm text-gray-500 mb-3">
                by {course.instructor}
              </p>

              <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {course.duration}
                </div>
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  {course.students.toLocaleString()}
                </div>
                <div className="flex items-center gap-1">
                  <BookOpen className="h-4 w-4" />
                  {course.modules} modules
                </div>
              </div>

              {course.progress > 0 && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Progress</span>
                    <span className="font-medium">{course.progress}%</span>
                  </div>
                  <Progress value={course.progress} className="h-2" />
                </div>
              )}
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  );
}
