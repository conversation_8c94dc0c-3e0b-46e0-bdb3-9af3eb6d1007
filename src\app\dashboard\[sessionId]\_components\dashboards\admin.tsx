import RecentCourses from "./sections/admin/recent-courses";
import RecentApproved from "./sections/admin/recent-approved";
import Recent from "./sections/admin/recent";
import Statistics from "./sections/admin/statistics";
import { getAdminDashboardData } from "@/lib/server/action/admins";
import { auth } from "@/lib/server/auth";
import { getActiveSession } from "@/lib/server/action/sessions";

export default async function AdminDashboard() {
  const session = await auth();
  const activeSession = await getActiveSession();

  if (!activeSession || !session || !session.user) return null;

  const dashboardData = await getAdminDashboardData(activeSession.id);

  return (
    <>
      {/* Header */}
      <header className="border-b pb-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              Hello {session.user.firstName || "User"}
              <span className="text-2xl">👋</span>
            </h1>
            <p className="text-gray-600 mt-1">
              {"Let's learn something today"}
            </p>
          </div>
        </div>
      </header>

      <div className="py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Column - Recent Courses */}
          <div className="lg:col-span-3">
            <RecentCourses
              recentCourses={dashboardData?.recentCourses}
              sessionId={activeSession.id}
            />

            {/* Recently Approved */}
            <RecentApproved
              recentlyApproved={dashboardData?.recentlyApprovedUsers}
            />

            <Recent
              recentStudents={dashboardData?.recentStudents}
              recentTeachers={dashboardData?.recentTeachers}
              sessionId={activeSession.id}
            />
          </div>

          {/* Right Column - Statistics */}
          <Statistics
            counts={dashboardData?.counts}
            sessionId={activeSession.id}
          />
        </div>
      </div>
    </>
  );
}
