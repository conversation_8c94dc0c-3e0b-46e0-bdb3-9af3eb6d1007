import { z } from "zod";

export const sessionSchema = z.object({
  name: z.string().min(1, { message: "Please provide the session name" }),
  description: z.string().optional(),
  startDate: z.date({ message: "Please provide the session start date" }),
  endDate: z.date({ message: "Please provide the session end date" }),
  isActive: z.string().optional(),
});

export type TSession = z.infer<typeof sessionSchema>;
export type TSessionForm = TSession;
