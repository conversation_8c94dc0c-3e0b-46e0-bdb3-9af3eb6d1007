'use server'

import prisma from "@/lib/prisma";


export async function getGeneralSettings() {
  try {
    const settings = await prisma.generalSettings.findFirst();
    if (!settings) {
      await prisma.generalSettings.create({
        data: {
          siteTitle: "MaPSA-EAPP",
          siteName: "MaPSA E-Learning Application",
          siteAddress: "",
          email: "",
          address: "",
        },
      })
      return await prisma.generalSettings.findFirst();
    }
    return settings;
  } catch (error) {
    console.error("Error fetching general settings:", error);
    return null;
  }
}