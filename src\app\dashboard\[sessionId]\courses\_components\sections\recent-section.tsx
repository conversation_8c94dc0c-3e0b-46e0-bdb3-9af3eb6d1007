"use client";

import { Card, CardContent } from "@/components/ui/card";
import { formatTimeAgo } from "@/lib/formatDate";
import { cn, formatBytes } from "@/lib/utils";
import { CourseResource } from "@prisma/client";

export default function RecentSection({
  recentFiles,
}: {
  recentFiles: CourseResource[];
}) {
  const fileTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "pdf":
        return "bg-red-400 text-red-800";
      case "zip":
        return "bg-yellow-400 text-yellow-800";
      case "docx":
        return "bg-blue-400 text-blue-800";
      case "docs":
        return "bg-blue-400 text-blue-800";
      case "xlsx":
        return "bg-green-400 text-green-800";
      case "image":
        return "bg-red-400 text-red-800";
      default:
        return "bg-gray-100 text-black";
    }
  };

  const fileType = (type: string) => {
    switch (type.toLowerCase()) {
      case "pdf":
        return "PDF";
      case "zip":
        return "ZIP";
      case "docx":
        return "WORD";
      case "docs":
        return "WORD";
      case "xlsx":
        return "XLSX";
      case "image":
        return "IMG";
      default:
        return "OTHER";
    }
  };
  return (
    <div>
      <h3 className="text-lg font-semibold mb-4">Recent files</h3>
      {recentFiles.length === 0 ? (
        <p className="text-sm text-muted-foreground">No recent files found.</p>
      ) : (
        <div className="space-y-2">
          {recentFiles.map((file) => (
            <Card
              key={file.id}
              className="hover:shadow-md transition-shadow cursor-pointer py-0"
              onClick={() => window.open(file.fileUrl!, "_blank")}
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-4 w-full">
                  <div className="p-1 bg-blue-100 rounded-lg flex items-center justify-center">
                    <div
                      className={cn(
                        "h-10 w-12 bg-primary rounded-sm flex items-center justify-center",
                        fileTypeColor(file.type)
                      )}
                    >
                      <span className="text-xs font-bold">
                        {fileType(file.type)}
                      </span>
                    </div>
                  </div>
                  <div className="w-full">
                    <h4 className="font-medium">{file.name}</h4>
                    <p className="text-sm text-gray-500">{file.type}</p>
                    <p className="text-xs text-muted-foreground">
                      Uploaded By:{" "}
                      <span className="text-black">{file.uploadedBy}</span>
                    </p>
                  </div>
                  <div className="ml-auto text-right">
                    <p className="text-sm text-gray-500">
                      {formatBytes(file.fileSize!)}
                    </p>
                    <p className="text-xs text-gray-400">
                      {formatTimeAgo(file.createdAt)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
