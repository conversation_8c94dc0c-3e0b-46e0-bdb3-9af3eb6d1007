"use client";

import { appConfig } from "@/config/app";
import { useRouter } from "next/navigation";

const MAX_VISIBLE_PAGES = 5; // Adjust as needed

const Pagination = ({ page, count }: { page: number; count: number }) => {
  const router = useRouter();

  const totalPages = Math.ceil(count / appConfig.ITEMS_PER_PAGE);
  const hasPrev = page > 1;
  const hasNext = page < totalPages;

  const changePage = (newPage: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set("page", newPage.toString());
    router.push(`${window.location.pathname}?${params}`);
  };

  // Calculate visible page numbers
  let startPage = Math.max(1, page - Math.floor(MAX_VISIBLE_PAGES / 2));
  let endPage = startPage + MAX_VISIBLE_PAGES - 1;
  if (endPage > totalPages) {
    endPage = totalPages;
    startPage = Math.max(1, endPage - MAX_VISIBLE_PAGES + 1);
  }

  const pageNumbers = [];
  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(i);
  }

  return (
    <div className="p-4 flex items-center justify-between text-gray-500">
      <button
        disabled={!hasPrev}
        className="py-2 px-4 rounded-md bg-primary text-primary-foreground cursor-pointer text-xs font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
        onClick={() => changePage(page - 1)}
      >
        Prev
      </button>
      <div className="flex items-center gap-2 text-sm">
        {startPage > 1 && (
          <>
            <button
              className={`px-2 pt-0.5 rounded-sm cursor-pointer ${
                page === 1 ? "bg-primary text-primary-foreground" : ""
              }`}
              onClick={() => changePage(1)}
            >
              1
            </button>
            {startPage > 2 && <span className="px-1">...</span>}
          </>
        )}
        {pageNumbers.map((pageIndex) => (
          <button
            key={pageIndex}
            className={`px-2 pt-0.5 rounded-sm cursor-pointer ${
              page === pageIndex ? "bg-primary text-primary-foreground" : ""
            }`}
            onClick={() => changePage(pageIndex)}
          >
            {pageIndex}
          </button>
        ))}
        {endPage < totalPages && (
          <>
            {endPage < totalPages - 1 && <span className="px-1">...</span>}
            <button
              className={`px-2 pt-0.5 rounded-sm cursor-pointer ${
                page === totalPages ? "bg-primary text-primary-foreground" : ""
              }`}
              onClick={() => changePage(totalPages)}
            >
              {totalPages}
            </button>
          </>
        )}
      </div>
      <button
        className="py-2 px-4 rounded-md bg-primary text-primary-foreground text-xs cursor-pointer font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={!hasNext}
        onClick={() => changePage(page + 1)}
      >
        Next
      </button>
    </div>
  );
};

export default Pagination;
