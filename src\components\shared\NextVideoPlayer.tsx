// import Player from "next-video/player";

// const NextVideoPlayer = ({
//   src,
//   title,
//   description,
//   controls = true,
// }: {
//   src: string;
//   title?: string;
//   description?: string;
//   controls?: boolean;
// }) => {
//   return (
//     <div className="space-y-4 max-w-4xl mx-auto flex-1 w-full">
//       <Player src={src} controls={controls} />
//       {/* Video info */}
//       {(title || description) && (
//         <div className="space-y-2">
//           {title && <h2 className="text-xl font-semibold">{title}</h2>}
//           {description && (
//             <p className="text-muted-foreground">{description}</p>
//           )}
//         </div>
//       )}
//     </div>
//   );
// };

// export default NextVideoPlayer;
