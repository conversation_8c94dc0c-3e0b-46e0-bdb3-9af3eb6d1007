import { Suspense } from "react";
import { CourseHeader } from "@/app/students/dashboard/_components/sections/course-header";
import { ModulesGrid } from "../_components/sections/module-grid";
import { getStudentModules } from "@/lib/server/action/courses/modules";
import { getStudentCourseDetails } from "@/lib/server/action/students";
import { notFound } from "next/navigation";
import { auth } from "../../../../../auth";

async function SuspendedComponent() {
  const session = await auth();
  if (!session || !session.user) {
    return null;
  }
  const studentId = session.user.profileId;
  const courseId = session.user.courseId as string;
  const course = await getStudentCourseDetails(courseId, studentId);
  if (!course) {
    notFound();
  }
  const modules = getStudentModules(courseId, studentId);

  const primaryTeacher = course.teacherAssignments.find(
    (ta) => ta.role === "PRIMARY"
  );

  return (
    <>
      <CourseHeader
        studentId={studentId}
        course={{
          title: course.title,
          program: course.program.name,
          description: course.description || "",
          teacher:
            primaryTeacher?.user.firstName +
            " " +
            primaryTeacher?.user.lastName,
          _count: {
            enrollments: course._count.enrollments,
            modules: course._count.modules,
          },
          progress: course.progress[0]?.progress ?? 0,
          image: course.fileUrl || "/images/placeholder.svg",
        }}
      />
      <Suspense fallback={<div>Loading...</div>}>
        <ModulesGrid studentId={studentId} modules={await modules} />
      </Suspense>
    </>
  );
}

export default async function ModulesPage() {
  return (
    <>
      <Suspense fallback={<div>Loading...</div>}>
        <SuspendedComponent />
      </Suspense>
    </>
  );
}
