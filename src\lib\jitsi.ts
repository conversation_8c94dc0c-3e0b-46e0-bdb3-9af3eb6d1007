/* eslint-disable @typescript-eslint/no-explicit-any */
import jwt from 'jsonwebtoken'
import { v4 as uuidv4 } from 'uuid'

export interface JitsiConfig {
  domain: string
  appId: string
  jwtSecret: string
}

export interface JitsiMeetingOptions {
  roomName: string
  userInfo: {
    displayName: string
    email: string
    avatar?: string
  }
  moderator?: boolean
  config?: {
    startWithAudioMuted?: boolean
    startWithVideoMuted?: boolean
    enableWelcomePage?: boolean
    enableClosePage?: boolean
    requireDisplayName?: boolean
    enableRecording?: boolean
    liveStreamingEnabled?: boolean
    fileRecordingsEnabled?: boolean
    enableNoiseCancellation?: boolean
    enableTalkWhileMuted?: boolean
    disableModeratorIndicator?: boolean
    startScreenSharing?: boolean
    enableEmailInStats?: boolean
    enableCalendarIntegration?: boolean
    enableRemoteControl?: boolean
    enableLipSync?: boolean
    disableSimulcast?: boolean
    enableLayerSuspension?: boolean
    enableStatsID?: boolean
    enableLocalVideoFlip?: boolean
    enableRemoteVideoMenu?: boolean
    enableSharedVideoMenu?: boolean
    enableVideoBlur?: boolean
    enableAudioLevels?: boolean
    enableNoAudioSignal?: boolean
    enableNoisyMicDetection?: boolean
    enableOpusRed?: boolean
    enableMultiStreamSupport?: boolean
    enableUnifiedOnChrome?: boolean
    enableAutomaticUrlCopy?: boolean
    enableWebRtcH264Simulcast?: boolean
    enableAv1Support?: boolean
    enableSaveLogs?: boolean
    enableLocalRecording?: boolean
    localRecording?: {
      enabled: boolean
      format: string
    }
    recordings?: {
      enabled: boolean
      mode: string
    }
    analytics?: {
      scriptURLs: string[]
      googleAnalyticsTrackingId?: string
    }
  }
  interfaceConfig?: {
    TOOLBAR_BUTTONS?: string[]
    SETTINGS_SECTIONS?: string[]
    MOBILE_APP_PROMO?: boolean
    SHOW_JITSI_WATERMARK?: boolean
    SHOW_WATERMARK_FOR_GUESTS?: boolean
    SHOW_BRAND_WATERMARK?: boolean
    BRAND_WATERMARK_LINK?: string
    SHOW_POWERED_BY?: boolean
    SHOW_PROMOTIONAL_CLOSE_PAGE?: boolean
    RANDOM_AVATAR_URL_PREFIX?: boolean
    RANDOM_AVATAR_URL_SUFFIX?: boolean
    FILM_STRIP_MAX_HEIGHT?: number
    ENABLE_FEEDBACK_ANIMATION?: boolean
    DISABLE_VIDEO_BACKGROUND?: boolean
    DISABLE_FOCUS_INDICATOR?: boolean
    DISABLE_DOMINANT_SPEAKER_INDICATOR?: boolean
    DISABLE_TRANSCRIPTION_SUBTITLES?: boolean
    DISABLE_RINGING?: boolean
    AUDIO_LEVEL_PRIMARY_COLOR?: string
    AUDIO_LEVEL_SECONDARY_COLOR?: string
    POLICY_LOGO?: string
    LOCAL_THUMBNAIL_RATIO?: number
    REMOTE_THUMBNAIL_RATIO?: number
    LIVE_STREAMING_HELP_LINK?: string
    MOBILE_DOWNLOAD_LINK_ANDROID?: string
    MOBILE_DOWNLOAD_LINK_IOS?: string
    APP_NAME?: string
    NATIVE_APP_NAME?: string
    PROVIDER_NAME?: string
    LANG_DETECTION?: boolean
    CONNECTION_INDICATOR_AUTO_HIDE_ENABLED?: boolean
    CONNECTION_INDICATOR_AUTO_HIDE_TIMEOUT?: number
    CONNECTION_INDICATOR_DISABLED?: boolean
    VIDEO_LAYOUT_FIT?: string
    filmStripOnly?: boolean
    startWithAudioMuted?: boolean
    startWithVideoMuted?: boolean
    startScreenSharing?: boolean
    startAudioOnly?: boolean
    enableClosePage?: boolean
    enableWelcomePage?: boolean
    enableUserRolesBasedOnToken?: boolean
    enableFeaturesBasedOnToken?: boolean
    enableNoAudioSignal?: boolean
    enableNoisyMicDetection?: boolean
    enableOpusRed?: boolean
    enableSimulcast?: boolean
    enableLayerSuspension?: boolean
    enableStatsID?: boolean
    enableSaveLogs?: boolean
    enableLocalVideoFlip?: boolean
    enableRemoteVideoMenu?: boolean
    enableSharedVideoMenu?: boolean
    enableVideoBlur?: boolean
    enableAudioLevels?: boolean
    enableTalkWhileMuted?: boolean
    disableModeratorIndicator?: boolean
    enableEmailInStats?: boolean
    enableCalendarIntegration?: boolean
    enableRemoteControl?: boolean
    enableLipSync?: boolean
    disableSimulcast?: boolean
    enableMultiStreamSupport?: boolean
    enableUnifiedOnChrome?: boolean
    enableAutomaticUrlCopy?: boolean
    enableWebRtcH264Simulcast?: boolean
    enableAv1Support?: boolean
    enableLocalRecording?: boolean
  }
}

export function generateJitsiJWT(options: {
  appId: string
  jwtSecret: string
  roomName: string
  userInfo: {
    displayName: string
    email: string
    avatar?: string
  }
  moderator?: boolean
  exp?: number
}): string {
  const { appId, jwtSecret, roomName, userInfo, moderator = false, exp } = options
  
  const payload = {
    iss: appId,
    aud: appId,
    sub: process.env.NEXT_PUBLIC_JITSI_DOMAIN || 'localhost:8000',
    exp: exp || Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour from now
    room: roomName,
    context: {
      user: {
        id: uuidv4(),
        name: userInfo.displayName,
        email: userInfo.email,
        avatar: userInfo.avatar,
      },
      features: {
        livestreaming: moderator,
        recording: moderator,
        transcription: moderator,
        'outbound-call': moderator,
      },
    },
    moderator: moderator,
  }

  return jwt.sign(payload, jwtSecret, { algorithm: 'HS256' })
}

export function generateRoomId(): string {
  return uuidv4().replace(/-/g, '')
}

export function createJitsiMeetingUrl(options: {
  domain: string
  roomName: string
  jwt?: string
  config?: Record<string, any>
}): string {
  const { domain, roomName, jwt: jwtToken, config } = options
  
  const baseUrl = `http://${domain}/${roomName}`
  const params = new URLSearchParams()
  
  if (jwtToken) {
    params.append('jwt', jwtToken)
  }
  
  if (config) {
    Object.entries(config).forEach(([key, value]) => {
      params.append(`config.${key}`, String(value))
    })
  }
  
  return params.toString() ? `${baseUrl}?${params.toString()}` : baseUrl
}

export function getJitsiEmbedConfig(options: JitsiMeetingOptions): any {
  const { roomName, userInfo, moderator = false, config = {}, interfaceConfig = {} } = options
  
  const defaultConfig = {
    startWithAudioMuted: false,
    startWithVideoMuted: false,
    enableWelcomePage: false,
    enableClosePage: false,
    requireDisplayName: true,
    enableRecording: moderator,
    liveStreamingEnabled: moderator,
    fileRecordingsEnabled: moderator,
    enableNoiseCancellation: true,
    enableTalkWhileMuted: false,
    disableModeratorIndicator: false,
    startScreenSharing: false,
    enableEmailInStats: false,
    enableCalendarIntegration: false,
    enableRemoteControl: false,
    enableLipSync: false,
    disableSimulcast: false,
    enableLayerSuspension: true,
    enableStatsID: false,
    enableSaveLogs: false,
    enableLocalVideoFlip: true,
    enableRemoteVideoMenu: true,
    enableSharedVideoMenu: true,
    enableVideoBlur: true,
    enableAudioLevels: true,
    enableNoAudioSignal: true,
    enableNoisyMicDetection: true,
    enableOpusRed: false,
    enableMultiStreamSupport: false,
    enableUnifiedOnChrome: false,
    enableAutomaticUrlCopy: false,
    enableWebRtcH264Simulcast: false,
    enableAv1Support: false,
    enableLocalRecording: false,
    ...config
  }
  
  const defaultInterfaceConfig = {
    TOOLBAR_BUTTONS: [
      'microphone', 'camera', 'closedcaptions', 'desktop', 'embedmeeting', 'fullscreen',
      'fodeviceselection', 'hangup', 'profile', 'chat', 'recording',
      'livestreaming', 'etherpad', 'sharedvideo', 'settings', 'raisehand',
      'videoquality', 'filmstrip', 'invite', 'feedback', 'stats', 'shortcuts',
      'tileview', 'videobackgroundblur', 'download', 'help', 'mute-everyone',
      'mute-video-everyone', 'security'
    ],
    SETTINGS_SECTIONS: ['devices', 'language', 'moderator', 'profile', 'calendar'],
    MOBILE_APP_PROMO: false,
    SHOW_JITSI_WATERMARK: false,
    SHOW_WATERMARK_FOR_GUESTS: false,
    SHOW_BRAND_WATERMARK: false,
    SHOW_POWERED_BY: false,
    SHOW_PROMOTIONAL_CLOSE_PAGE: false,
    RANDOM_AVATAR_URL_PREFIX: false,
    RANDOM_AVATAR_URL_SUFFIX: false,
    FILM_STRIP_MAX_HEIGHT: 120,
    ENABLE_FEEDBACK_ANIMATION: false,
    DISABLE_VIDEO_BACKGROUND: false,
    DISABLE_FOCUS_INDICATOR: false,
    DISABLE_DOMINANT_SPEAKER_INDICATOR: false,
    DISABLE_TRANSCRIPTION_SUBTITLES: false,
    DISABLE_RINGING: false,
    AUDIO_LEVEL_PRIMARY_COLOR: 'rgba(255,255,255,0.4)',
    AUDIO_LEVEL_SECONDARY_COLOR: 'rgba(255,255,255,0.2)',
    POLICY_LOGO: null,
    LOCAL_THUMBNAIL_RATIO: 16 / 9,
    REMOTE_THUMBNAIL_RATIO: 1,
    LIVE_STREAMING_HELP_LINK: 'https://jitsi.org/live',
    MOBILE_DOWNLOAD_LINK_ANDROID: 'https://play.google.com/store/apps/details?id=org.jitsi.meet',
    MOBILE_DOWNLOAD_LINK_IOS: 'https://itunes.apple.com/us/app/jitsi-meet/id1165103905',
    APP_NAME: 'Jitsi Meet',
    NATIVE_APP_NAME: 'Jitsi Meet',
    PROVIDER_NAME: 'Jitsi',
    LANG_DETECTION: true,
    CONNECTION_INDICATOR_AUTO_HIDE_ENABLED: true,
    CONNECTION_INDICATOR_AUTO_HIDE_TIMEOUT: 5000,
    CONNECTION_INDICATOR_DISABLED: false,
    VIDEO_LAYOUT_FIT: 'both',
    filmStripOnly: false,
    startWithAudioMuted: false,
    startWithVideoMuted: false,
    startScreenSharing: false,
    startAudioOnly: false,
    enableClosePage: false,
    enableWelcomePage: false,
    enableUserRolesBasedOnToken: true,
    enableFeaturesBasedOnToken: true,
    ...interfaceConfig
  }
  
  return {
    roomName,
    domain: process.env.JITSI_DOMAIN || 'localhost:8000',
    configOverwrite: defaultConfig,
    interfaceConfigOverwrite: defaultInterfaceConfig,
    userInfo: {
      displayName: userInfo.displayName,
      email: userInfo.email,
      avatarURL: userInfo.avatar,
    },
    onload: function() {
      // Custom loading logic if needed
    }
  }
}