import { getCMSData } from "@/lib/server/action/frontend/frontend.action";
import FeaturesBuilder from "../_component/features-builder";
import { But<PERSON> } from "@/components/ui/button";

export default async function FeaturesManagementPage() {
  const cmsData = await getCMSData();

  if (!cmsData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Failed to load CMS data</h2>
          <p className="text-muted-foreground mb-4">
            There was an error loading the content management data.
          </p>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <FeaturesBuilder cmsData={cmsData} />
    </>
  );
}
