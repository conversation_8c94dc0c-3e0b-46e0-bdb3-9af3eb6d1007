import { getCMSData } from "@/lib/server/action/frontend/frontend.action";
import { AboutSection } from "../_components/sections/about-section";
import { NewsletterSection } from "../_components/sections/newsletter-section";

export default async function AboutPage() {
  const data = await getCMSData();

  if (!data) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Failed to load CMS data</h2>
          <p className="text-muted-foreground mb-4">
            There was an error loading the content management data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <AboutSection
        title={data.about.title}
        description={data.about.description}
        brandIdentity={data.about.brandIdentity}
        designElements={data.about.designElements}
      />
      <NewsletterSection
        newsletterTitle={data.newsletter.title}
        newsletterPlaceholder={data.newsletter.placeholder}
        newsletterButtonText={data.newsletter.buttonText}
      />
    </div>
  );
}
