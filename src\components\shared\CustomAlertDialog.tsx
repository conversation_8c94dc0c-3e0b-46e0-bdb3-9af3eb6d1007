"use client";

import {
  Al<PERSON><PERSON><PERSON><PERSON>,
  Alert<PERSON><PERSON>og<PERSON><PERSON>ger,
  AlertD<PERSON>og<PERSON>ontent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";

interface Props {
  title?: string;
  description?: string;
  trigger: React.ReactNode | string;
  asChild?: boolean;
  className?: string;
  onConfirm: () => void;
  normal?: boolean;
}

const CustomAlertDialog = ({
  title = "Are you sure?",
  description = "This action cannot be undone.",
  trigger,
  asChild = true,
  className,
  onConfirm,
  normal = false,
}: Props) => {
  return (
    <AlertDialog>
      <AlertDialogTrigger
        asChild={asChild}
        className={cn(
          !asChild
            ? "hover:text-red-500 cmw-dropdown-child text-start px-2 py-1 text-[14px] hover:bg-muted rounded-sm"
            : "",
          className
        )}
      >
        {trigger}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          {description && (
            <AlertDialogDescription>{description}</AlertDialogDescription>
          )}
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            className={cn(
              normal
                ? "bg-primary/90 hover:bg-primary"
                : "bg-red-400 hover:bg-red-500"
            )}
            onClick={onConfirm}
          >
            Confirm
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default CustomAlertDialog;
