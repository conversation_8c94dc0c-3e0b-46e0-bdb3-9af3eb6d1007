// import { But<PERSON> } from "@/components/ui/button";
import StudentTable from "../../StudentTable";
import StudentFilter from "../../filters/student-filter";
import { getSchoolOptions } from "@/lib/server/action/schools";
import { getCourseStudents } from "@/lib/server/action/courses";
import CustomDialog from "@/components/shared/CustomDialog";
import { Button } from "@/components/ui/button";
import { CloudUpload } from "lucide-react";
import ExportStudentReport from "../../ExportStudentReport";
import { getSiteData } from "@/lib/server/action/frontend/frontend.action";

export async function StudentsTab({
  courseId,
  sessionId,
}: {
  courseId: string;
  sessionId: string;
}) {
  const [courseStudentsData, schools, siteData] = await Promise.all([
    getCourseStudents(courseId),
    getSchoolOptions(sessionId),
    getSiteData(),
  ]);

  return (
    <div className="space-y-6">
      <div className="flex lg:items-center justify-between flex-col lg:flex-row gap-3">
        <h2 className="text-2xl font-bold">List of Students</h2>
        <CustomDialog
          title="Export"
          trigger={
            <Button size="sm">
              <CloudUpload /> Export
            </Button>
          }
        >
          <ExportStudentReport
            courseId={courseId}
            siteName={siteData ? siteData.siteName : ""}
          />
        </CustomDialog>
      </div>

      <StudentFilter schools={schools} />

      <StudentTable courseStudents={courseStudentsData} />
    </div>
  );
}
