"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import Image from "next/image";
import { CMSData } from "@/lib/server/action/frontend/frontend.action";
import { ImageIcon, Loader2, Plus, Trash2, Upload, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { deleteFromBunnyCDN } from "@/lib/server/action/bunny/bunny.action";

type DesignElementProps = {
  data: CMSData;
  setData: React.Dispatch<React.SetStateAction<CMSData | null>>;
  markAsChanged: () => void;
  uploadingStates: { [key: string]: boolean };
  setUploadingStates: React.Dispatch<
    React.SetStateAction<{ [key: string]: boolean }>
  >;
  handleFileChange: (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number,
    type: "feature" | "design"
  ) => void;
  triggerFileInput: (index: number, type: "feature" | "design") => void;
  replaceImage: (
    index: number,
    type: "feature" | "design",
    oldImageUrl: string
  ) => void;
  updateDesignElement: (
    index: number,
    field: string,
    value: string | string[]
  ) => void;
  designFileInputRefs: React.MutableRefObject<{
    [key: number]: HTMLInputElement | null;
  }>;
};

export default function DesignElement({
  data,
  setData,
  markAsChanged,
  uploadingStates,
  handleFileChange,
  triggerFileInput,
  replaceImage,
  updateDesignElement,
  designFileInputRefs,
}: DesignElementProps) {
  const addDesignElement = () => {
    if (!data) return;

    const newData = { ...data };
    newData.about.designElements.push({
      title: "New Design Element",
      details: ["Element detail 1", "Element detail 2"],
      image: "/images/placeholder.svg?height=120&width=120",
    });
    setData(newData);
    markAsChanged();
  };

  const removeDesignElement = async (index: number) => {
    if (!data) return;

    if (confirm("Are you sure you want to remove this element?")) {
      const element = data.about.designElements[index];

      // Delete image from Bunny CDN if it's a CDN URL
      if (
        element.image &&
        element.image.includes(process.env.NEXT_PUBLIC_BUNNY_CDN_URL || "")
      ) {
        try {
          await deleteFromBunnyCDN(element.image);
        } catch (error) {
          console.error("Failed to delete image from CDN:", error);
        }
      }

      const newData = { ...data };
      newData.about.designElements.splice(index, 1);
      setData(newData);
      markAsChanged();
    }
  };

  const addDesignElementDetail = (elementIndex: number) => {
    if (!data) return;

    const newData = { ...data };
    newData.about.designElements[elementIndex].details.push("New detail");
    setData(newData);
    markAsChanged();
  };

  const updateDesignElementDetail = (
    elementIndex: number,
    detailIndex: number,
    value: string
  ) => {
    if (!data) return;

    const newData = { ...data };
    newData.about.designElements[elementIndex].details[detailIndex] = value;
    setData(newData);
    markAsChanged();
  };

  const removeDesignElementDetail = (
    elementIndex: number,
    detailIndex: number
  ) => {
    if (!data) return;

    const newData = { ...data };
    newData.about.designElements[elementIndex].details.splice(detailIndex, 1);
    setData(newData);
    markAsChanged();
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Design Elements</CardTitle>
            <CardDescription>
              Manage the design elements shown on the about page
            </CardDescription>
          </div>
          <Button
            onClick={addDesignElement}
            size="sm"
            className="flex items-center space-x-1"
          >
            <Plus className="w-4 h-4" />
            <span>Add Element</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {data.about.designElements.map((element, index) => (
            <div key={index} className="border rounded-lg p-6 space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-lg">
                  Design Element {index + 1}
                </h3>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => removeDesignElement(index)}
                  className="flex items-center space-x-1"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Remove</span>
                </Button>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor={`element-title-${index}`}>Title</Label>
                    <Input
                      id={`element-title-${index}`}
                      value={element.title}
                      onChange={(e) =>
                        updateDesignElement(index, "title", e.target.value)
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Element Image</Label>
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        <Image
                          src={element.image || "/images/placeholder.svg"}
                          alt={element.title}
                          width={80}
                          height={80}
                          className="rounded-lg border object-cover"
                        />
                        {uploadingStates[`design-${index}`] && (
                          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                            <Loader2 className="w-6 h-6 text-white animate-spin" />
                          </div>
                        )}
                      </div>

                      <div className="space-y-2 flex-1">
                        <input
                          type="file"
                          ref={(el) => {
                            designFileInputRefs.current[index] = el;
                          }}
                          onChange={(e) => handleFileChange(e, index, "design")}
                          accept="image/*"
                          className="hidden"
                        />

                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => triggerFileInput(index, "design")}
                            disabled={uploadingStates[`design-${index}`]}
                            className="flex items-center space-x-2"
                          >
                            {uploadingStates[`design-${index}`] ? (
                              <Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                              <Upload className="w-4 h-4" />
                            )}
                            <span>
                              {uploadingStates[`design-${index}`]
                                ? "Uploading..."
                                : "Upload"}
                            </span>
                          </Button>

                          {element.image &&
                            !element.image.includes("placeholder") && (
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  replaceImage(index, "design", element.image)
                                }
                                disabled={uploadingStates[`design-${index}`]}
                                className="flex items-center space-x-2"
                              >
                                <ImageIcon className="w-4 h-4" />
                                <span>Replace</span>
                              </Button>
                            )}
                        </div>

                        <Input
                          placeholder="Or enter image URL"
                          value={element.image}
                          onChange={(e) =>
                            updateDesignElement(index, "image", e.target.value)
                          }
                          className="text-xs"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label>Element Details</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => addDesignElementDetail(index)}
                      className="flex items-center space-x-1"
                    >
                      <Plus className="w-3 h-3" />
                      <span>Add Detail</span>
                    </Button>
                  </div>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {element.details.map((detail, detailIndex) => (
                      <div
                        key={detailIndex}
                        className="flex items-center space-x-2"
                      >
                        <Input
                          value={detail}
                          onChange={(e) =>
                            updateDesignElementDetail(
                              index,
                              detailIndex,
                              e.target.value
                            )
                          }
                          placeholder="Element detail"
                          className="text-sm"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            removeDesignElementDetail(index, detailIndex)
                          }
                          className="flex-shrink-0"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
