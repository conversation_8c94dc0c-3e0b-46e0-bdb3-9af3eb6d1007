"use client";

import { But<PERSON> } from "../../../../../../components/ui/button";
import { <PERSON>, Loader, Save } from "lucide-react";
import { toast } from "sonner";
import { createCourse } from "@/lib/server/action/courses";
import { TCourseForm } from "@/lib/server/action/courses/course.schema";
import { useEffect, useState } from "react";
import { getProgramOptions } from "@/lib/server/action/programs";
import { getTeacherOptions } from "@/lib/server/action/teachers";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useRouter } from "next/navigation";

export default function CreateCourse({
  sessionId,
  isAdmin,
}: {
  sessionId: string;
  isAdmin: boolean;
}) {
  const router = useRouter();

  const [programs, setPrograms] = useState<{ label: string; value: string }[]>(
    []
  );
  const [teachers, setTeachers] = useState<{ label: string; value: string }[]>(
    []
  );
  // const [preview, setPreview] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<
    TCourseForm & { thumbnail?: string }
  >({
    title: "",
    description: "",
    programId: "",
    teacherId: "",
    thumbnail: "",
  });

  useEffect(() => {
    async function programOptions() {
      const res = await getProgramOptions(sessionId);
      setPrograms(res);
    }
    async function teacherOptions() {
      const res = await getTeacherOptions(sessionId);
      setTeachers(
        res.map((teacher) => ({
          label: teacher.name,
          value: teacher.id,
        }))
      );
    }
    programOptions();
    teacherOptions();
  }, [sessionId]);

  // const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   const file = event.target.files?.[0];
  //   if (file) {
  //     setPreview(file);
  //     setFormData((prev) => ({ ...prev, thumbnail: file.name }));
  //   }
  // };

  const handleSubmit = async (status: "DRAFT" | "PUBLISHED") => {
    try {
      setIsSubmitting(true);
      const res = await createCourse(sessionId, {
        ...formData,
        status,
      });
      if (res.success) {
        toast.success(res.message);
        router.push(
          `/dashboard/${sessionId}/${isAdmin ? "courses" : "my-courses"}`
        );
      } else {
        toast.error(res.error);
      }
    } catch (error) {
      console.error(error);
      toast.error("An error occurred while creating the course");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form>
      <div className="flex lg:items-center justify-between my-2 flex-col lg:flex-row gap-3 mb-6">
        <div className="flex items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Create New Course</h1>
            <p className="text-gray-600">
              Fill in the details to create your course
            </p>
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => handleSubmit("DRAFT")}
            disabled={isSubmitting}
          >
            <Save className="w-4 h-4 mr-2" />
            {isSubmitting ? (
              <>
                <Loader /> Saving
              </>
            ) : (
              <>Save as Draft</>
            )}
          </Button>
          <Button
            onClick={() => handleSubmit("PUBLISHED")}
            disabled={isSubmitting}
          >
            <Eye className="w-4 h-4 mr-2" />
            {isSubmitting ? (
              <>
                <Loader /> Publishing
              </>
            ) : (
              <>Publish Course</>
            )}
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Course Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-x-6 gap-y-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Title</Label>
                  <Input
                    name="title"
                    value={formData.title}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        title: e.target.value,
                      }))
                    }
                    placeholder="Enter course title"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Program</Label>
                  <Select
                    value={formData.programId}
                    onValueChange={(value) =>
                      setFormData((prev) => ({
                        ...prev,
                        programId: value,
                      }))
                    }
                  >
                    <SelectTrigger className="flex-1 w-full">
                      <SelectValue placeholder="Select a program" />
                    </SelectTrigger>
                    <SelectContent>
                      {programs.map((program) => (
                        <SelectItem key={program.value} value={program.value}>
                          {program.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Teacher</Label>
                  <Select
                    value={formData.teacherId}
                    onValueChange={(value) =>
                      setFormData((prev) => ({
                        ...prev,
                        teacherId: value,
                      }))
                    }
                  >
                    <SelectTrigger className="flex-1 w-full">
                      <SelectValue placeholder="Select a teacher" />
                    </SelectTrigger>
                    <SelectContent>
                      {teachers.map((teacher) => (
                        <SelectItem key={teacher.value} value={teacher.value}>
                          {teacher.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label>Description</Label>
                <Textarea
                  name="description"
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Enter course description"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        {/* <div className="space-y-6 lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Course Cover Image</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  {preview ? (
                    <div className="space-y-2">
                      <ImageIcon className="w-8 h-8 mx-auto text-green-600" />
                      <p className="text-sm text-green-600">{preview.name}</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Upload className="w-8 h-8 mx-auto text-gray-400" />
                      <p className="text-sm text-gray-600">
                        Upload course cover image
                      </p>
                    </div>
                  )}
                </div>
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="cursor-pointer"
                />
              </div>
            </CardContent>
          </Card>
        </div> */}
      </div>
    </form>
  );
}
