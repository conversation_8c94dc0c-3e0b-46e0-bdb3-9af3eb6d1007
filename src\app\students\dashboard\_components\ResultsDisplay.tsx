/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState } from "react";
import Link from "next/link";

interface ResultsDisplayProps {
  attemptData: {
    id: string;
    score: number | null;
    totalPoints: number | null;
    passed: boolean | null;
    timeSpent: number | null;
    completedAt: Date | null;
    attemptNumber: number;
    assessment: {
      title: string;
      passingScore: number;
      questions: any[];
    };
    responses: any[];
  };
}

export default function ResultsDisplay({ attemptData }: ResultsDisplayProps) {
  const [showDetails, setShowDetails] = useState(false);

  const { score, passed, timeSpent, assessment, responses } = attemptData;
  const correctAnswers = responses.filter((r) => r.isCorrect).length;
  const totalQuestions = assessment.questions.length;

  return (
    <div className="max-w-4xl mx-auto">
      {/* Results Summary */}
      <div className="bg-white rounded-lg shadow-lg p-8 mb-6">
        <div className="text-center">
          <div
            className={`w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-4 ${
              passed ? "bg-green-100" : "bg-red-100"
            }`}
          >
            <span
              className={`text-4xl ${
                passed ? "text-green-600" : "text-red-600"
              }`}
            >
              {passed ? "✓" : "✗"}
            </span>
          </div>

          <h1 className="text-3xl font-bold mb-2">{assessment.title}</h1>
          <p
            className={`text-xl font-semibold ${
              passed ? "text-green-600" : "text-red-600"
            }`}
          >
            {passed ? "Congratulations! You Passed!" : "You Did Not Pass"}
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-8">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600">
              {score ? score.toFixed(1) : 0}%
            </div>
            <div className="text-gray-600">Your Score</div>
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-gray-700">
              {assessment.passingScore}%
            </div>
            <div className="text-gray-600">Passing Score</div>
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-green-600">
              {correctAnswers}/{totalQuestions}
            </div>
            <div className="text-gray-600">Correct Answers</div>
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600">
              {timeSpent || 0}
            </div>
            <div className="text-gray-600">Minutes</div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center gap-4 mb-6">
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          {showDetails ? "Hide Details" : "Show Details"}
        </button>

        <Link
          href="/student/dashboard"
          className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          Back to Dashboard
        </Link>
      </div>

      {/* Detailed Results */}
      {showDetails && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold mb-6">Detailed Results</h2>

          <div className="space-y-6">
            {assessment.questions.map((question, index) => {
              const response = responses.find(
                (r) => r.questionId === question.id
              );
              const isCorrect = response?.isCorrect || false;

              return (
                <div
                  key={question.id}
                  className="border-b pb-6 last:border-b-0"
                >
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-semibold">
                      Question {index + 1}
                    </h3>
                    <span
                      className={`px-3 py-1 rounded-full text-sm font-medium ${
                        isCorrect
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {isCorrect ? "Correct" : "Incorrect"}
                    </span>
                  </div>

                  <p className="text-gray-700 mb-3">{question.question}</p>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">
                        Your Answer:
                      </h4>
                      <p
                        className={`p-2 rounded ${
                          isCorrect
                            ? "bg-green-50 text-green-800"
                            : "bg-red-50 text-red-800"
                        }`}
                      >
                        {response?.response || "No answer provided"}
                      </p>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">
                        Correct Answer:
                      </h4>
                      <p className="p-2 bg-gray-50 text-gray-800 rounded">
                        {question.correctAnswer}
                      </p>
                    </div>
                  </div>

                  {question.explanation && (
                    <div className="mt-3">
                      <h4 className="font-medium text-gray-900 mb-1">
                        Explanation:
                      </h4>
                      <p className="text-gray-700 bg-blue-50 p-3 rounded">
                        {question.explanation}
                      </p>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
