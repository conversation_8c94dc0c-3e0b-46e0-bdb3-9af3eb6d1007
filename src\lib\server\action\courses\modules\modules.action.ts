'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"
import { TModuleForm } from "./modules.schema"
import { deleteFromBunny, deleteVideo } from "../../bunny/bunny.action"
import { ModulesWithDetails } from "./modules.list"
import { getActiveSession } from "../../sessions"

export async function createModule(courseId: string, data: TModuleForm) {
  try {
    const course = await prisma.course.findUnique({
      where: { id: courseId },
      select: {
        sessionId: true
      }
    })
    
    if (!course) {
      return { success: false, error: 'Course not found' }
    }
    
    await prisma.module.create({
      data: {
        title: data.title,
        description: data.description,
        order: await getNextModuleOrder(courseId),
        course: { connect: { id: courseId } }
      }
    })
    
    revalidatePath(`/dashboard/${course.sessionId}/courses/${courseId}`)
    return { success: true, message: '<PERSON><PERSON><PERSON> created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create module' }
  }
}

export async function updateModule(moduleId: string, data: Partial<TModuleForm>) {
  try {
    const modules = await prisma.module.update({
      where: { id: moduleId },
      data,
      include: { 
        course: {
          select: {
            id: true,
            sessionId: true
          }
        },
      }
    })
    
    revalidatePath(`/dashboard/${modules.course.sessionId}/courses/${modules.course.id}`)
    return { success: true, message: 'Module updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update module' }
  }
}

export async function deleteModule(moduleId: string) {
  try {
    const modules = await prisma.module.findUnique({
      where: { id: moduleId },
      select: {
        course: {
          select: {
            id: true,
            sessionId: true
          }
        },
        lesson: {
          select: {
            fileId: true,
            type: true
          }
        }
      }
    })
    
    if (!modules) {
      return { success: false, error: 'Module not found' }
    }

    if (modules.lesson?.fileId) {
      if (modules.lesson.type === 'VIDEO') {
        await deleteVideo(modules.lesson.fileId)
      } else {
        await deleteFromBunny(modules.lesson.fileId)
      }
    }
    
    await prisma.module.delete({
      where: { id: moduleId }
    })
    
    revalidatePath(`/dashboard/${modules.course.sessionId}/courses/${modules.course.id}`)
    return { success: true }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete module' }
  }
}

async function getNextModuleOrder(courseId: string): Promise<number> {
  const lastModule = await prisma.module.findFirst({
    where: { courseId },
    orderBy: { order: 'desc' }
  })
  return (lastModule?.order || 0) + 1
}

export async function changeModuleOrder(modules: ModulesWithDetails[]) {
  try {
    const activeSession = await getActiveSession()

    if (!activeSession) {
      return { success: false, error: 'Failed to change module order' }
    }

    await prisma.$transaction(async (tx) => {
      const updatePromises = modules.map((module) =>
        tx.module.update({
          where: { id: module.id },
          data: { order: module.order },
        })
      );

      revalidatePath(`/dashboard/${activeSession.id}/courses`)
      return Promise.all(updatePromises);
    });
    
    return { success: true }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to change module order' }
  }
}
