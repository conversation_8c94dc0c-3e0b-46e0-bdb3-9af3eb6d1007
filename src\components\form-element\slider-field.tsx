/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Slider } from "@/components/ui/slider";

interface FormSliderFieldProps {
  control: any;
  name: string;
  label: string;
  description?: string;
  min?: number;
  max?: number;
  step?: number;
}

export function FormSliderField({
  control,
  name,
  label,
  description,
  min = 0,
  max = 100,
  step = 1,
}: FormSliderFieldProps) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Slider
              min={min}
              max={max}
              step={step}
              defaultValue={[field.value]}
              onValueChange={(values) => {
                field.onChange(values[0]);
              }}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
