import { Fragment, Suspense } from "react";
import FolderCard from "../_components/cards/FolderCard";
import RecentFilesSection from "../_components/sections/recent-file";
import {
  getCourseResourceFolders,
  getCourseResources,
} from "@/lib/server/action/courses/resources";
import { auth } from "@/lib/server/auth";

const SuspendedComponent = async () => {
  const session = await auth();
  if (!session || !session.user) {
    return null;
  }

  const courseId = session.user.courseId as string;
  const [folders, resources] = await Promise.all([
    getCourseResourceFolders(courseId),
    getCourseResources(courseId),
  ]);

  return (
    <div className="space-y-6">
      <div className="space-y-2 mb-4">
        <h3 className="text-lg font-semibold">Folders</h3>
        {folders.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No folder found.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {folders.map((folder) => (
              <Fragment key={folder.id}>
                <FolderCard folder={folder} />
              </Fragment>
            ))}
          </div>
        )}
      </div>
      <RecentFilesSection recentFiles={resources} />
    </div>
  );
};

export default function FileManagementPage() {
  return (
    <div className="space-y-4 max-w-7xl mx-auto">
      <div className="flex lg:items-center justify-between flex-col lg:flex-row gap-3">
        <h2 className="text-2xl font-bold tracking-tight">File Management</h2>
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <SuspendedComponent />
      </Suspense>
    </div>
  );
}
