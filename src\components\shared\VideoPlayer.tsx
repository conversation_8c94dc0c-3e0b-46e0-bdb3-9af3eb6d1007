"use client";

import ReactPlayer from "react-player";
import React, { useState, useRef, useEffect } from "react";
import { Button } from "../ui/button";
import { Loader, Pause, Play, Volume2, VolumeX } from "lucide-react";
import { cn } from "@/lib/utils";
import Duration from "./Duration";
import { Slider } from "../ui/slider";

const VideoPlayer = ({
  url,
  videoId,
  title,
  description,
  playing = true,
  controls = false,
  light = false,
  volume = 0.8,
  muted = false,
  playbackRate = 1.0,
  showVolumeButton = true,
  showProceedButton = false,
  onProceed = () => {},
  onCancel = () => {},
  hasWatched = false,
  isProcessing = false,
}: {
  url: string;
  videoId: string;
  title?: string;
  description?: string;
  playing?: boolean;
  controls?: boolean;
  light?: boolean;
  volume?: number;
  muted?: boolean;
  playbackRate?: number;
  showVolumeButton?: boolean;
  showProceedButton?: boolean;
  onProceed?: () => void;
  onCancel?: () => void;
  hasWatched?: boolean;
  isProcessing?: boolean;
}) => {
  const [state, setState] = useState({
    playing,
    controls,
    light,
    volume,
    muted,
    playbackRate,
    seeking: false,
    ended: false,
    played: 0,
    loaded: 0,
    duration: 0,
  });

  const [showControls, setShowControls] = useState(false);
  const [isBuffering, setIsBuffering] = useState(false);
  const playerRef = useRef<ReactPlayer | null>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastSavedTimeRef = useRef<number>(0);
  const hasResumed = useRef(false); // To ensure seekTo is called only once

  const localStorageKey = `video-progress-${videoId}`;

  // Resume playback from saved progress when the player is ready
  const handleReady = () => {
    if (!hasResumed.current) {
      const savedProgress = localStorage.getItem(localStorageKey);
      if (savedProgress) {
        const progress = parseFloat(savedProgress);
        if (playerRef.current) {
          playerRef.current.seekTo(progress, "seconds");
        }
      }
      hasResumed.current = true; // Ensure this runs only once
    }
  };

  // Save progress periodically and on unmount
  useEffect(() => {
    const saveProgress = () => {
      if (playerRef.current && !state.ended) {
        const currentTime = playerRef.current.getCurrentTime();
        if (currentTime !== lastSavedTimeRef.current) {
          localStorage.setItem(localStorageKey, currentTime.toString());
          lastSavedTimeRef.current = currentTime;
        }
      }
    };

    const interval = setInterval(saveProgress, 5000);

    return () => {
      clearInterval(interval);
      saveProgress();
    };
  }, [localStorageKey, state.ended]);

  // Handle video end
  const handleEnded = () => {
    setState((prev) => ({ ...prev, ended: true }));
    localStorage.removeItem(localStorageKey);
  };

  const handlePlayPause = () => {
    setState((prev) => ({ ...prev, playing: !prev.playing }));
  };

  const handleVolumeChange = (value: number[]) => {
    setState((prev) => ({ ...prev, volume: value[0] }));
  };

  const handleToggleMuted = () => {
    setState((prev) => ({ ...prev, muted: !prev.muted }));
  };

  const handleProgress = (progress: { played: number; loaded: number }) => {
    if (!state.seeking) {
      setState((prev) => ({ ...prev, ...progress }));
    }
  };

  const handleDuration = (duration: number) => {
    setState((prev) => ({ ...prev, duration }));
  };

  const handleMouseMove = () => {
    if (!controls) {
      setShowControls(true);
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 3000); // Hide controls after 3 seconds of inactivity
    }
  };

  const handleMouseEnter = () => {
    if (!controls) {
      setShowControls(true);
    }
  };

  const handleMouseLeave = () => {
    if (!controls) {
      setShowControls(false);
    }
  };

  return (
    <div
      className="flex-1 w-full"
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="player-wrapper relative">
        {isBuffering && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10">
            <Loader className="w-12 h-12 text-white animate-spin" />
          </div>
        )}

        <ReactPlayer
          playsinline={true}
          ref={playerRef}
          className="react-player"
          width="100%"
          height="100%"
          url={url}
          playing={state.playing}
          controls={state.controls}
          light={state.light}
          playbackRate={state.playbackRate}
          volume={state.volume}
          muted={state.muted}
          onReady={handleReady} // Resume playback when the player is ready
          onPlay={() => setState((prev) => ({ ...prev, playing: true }))}
          onPause={() => setState((prev) => ({ ...prev, playing: false }))}
          onBuffer={() => setIsBuffering(true)}
          onBufferEnd={() => setIsBuffering(false)}
          onEnded={handleEnded}
          onProgress={handleProgress}
          onDuration={handleDuration}
        />
        {!controls ? (
          <div
            className={cn(
              "absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 transition-opacity duration-300",
              showControls ? "opacity-100" : "opacity-0"
            )}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {/* Play/Pause button */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white hover:bg-white/10"
                  onClick={handlePlayPause}
                >
                  {state.playing ? (
                    <Pause className="w-5 h-5" />
                  ) : (
                    <Play className="w-5 h-5" />
                  )}
                </Button>

                {/* Volume control */}
                {showVolumeButton ? (
                  <div className="flex items-center">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-white hover:bg-white/10"
                      onClick={handleToggleMuted}
                    >
                      {state.muted || state.volume === 0 ? (
                        <VolumeX className="w-5 h-5" />
                      ) : (
                        <Volume2 className="w-5 h-5" />
                      )}
                    </Button>
                    <div className="w-20 hidden sm:block">
                      <Slider
                        value={[state.muted ? 0 : state.volume]}
                        min={0}
                        max={1}
                        step={0.01}
                        onValueChange={handleVolumeChange}
                        className="[&>span:first-child]:h-1 [&>span:first-child]:bg-white/30 [&_[role=slider]]:bg-white [&_[role=slider]]:w-2.5 [&_[role=slider]]:h-2.5 [&_[role=slider]]:border-0 [&>span:first-child_span]:bg-white [&_[role=slider]:focus-visible]:ring-0 [&_[role=slider]:focus-visible]:ring-offset-0"
                      />
                    </div>
                  </div>
                ) : null}

                {/* Time display */}
                <div className="text-white text-sm">
                  <Duration seconds={state.duration * state.played} />/
                  <Duration seconds={state.duration} />
                </div>
              </div>
            </div>
          </div>
        ) : null}
      </div>
      {showProceedButton ? (
        <div className="flex w-full items-center justify-between gap-x-4 p-4">
          <p className="text-sm text-gray-600">{title}</p>
          <div className="flex gap-x-4">
            <Button
              size="sm"
              variant="outline"
              className="border-primary/80"
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button
              onClick={onProceed}
              disabled={(!state.ended && !hasWatched) || isProcessing}
              className="disabled:bg-gray-400 disabled:text-gray-200 disabled:cursor-not-allowed bg-primary text-white"
              size="sm"
            >
              {isProcessing ? (
                <span className="flex items-center gap-2">
                  <Loader className="h-4 w-4 animate-spin" />
                  Processing...
                </span>
              ) : hasWatched ? (
                "Skip"
              ) : (
                "Proceed"
              )}
            </Button>
          </div>
        </div>
      ) : (
        <>
          {/* Video info */}
          {(title || description) && (
            <div className="space-y-2">
              {title && <h2 className="text-xl font-semibold">{title}</h2>}
              {description && (
                <p className="text-muted-foreground">{description}</p>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default VideoPlayer;
