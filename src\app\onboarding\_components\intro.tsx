import { Button } from "@/components/ui/button";
import Image from "next/image";

export default function Introduction({
  handleNext,
  siteLogo,
  siteName,
  welcomeMessage,
}: {
  handleNext: () => void;
  siteName: string;
  siteLogo: string;
  welcomeMessage: string;
}) {
  return (
    <div className="text-center space-y-6">
      <div className="flex justify-center mb-6">
        <Image src={siteLogo} alt="Site Logo" width={100} height={100} />
      </div>

      <div className="space-y-4">
        <h1 className="text-4xl font-bold text-gray-900">WELCOME TO</h1>
        <h2 className="text-2xl font-semibold text-gray-800">{siteName}</h2>
      </div>

      <div className="max-w-2xl mx-auto text-gray-600 leading-relaxed">
        <p>{welcomeMessage}</p>
      </div>

      <div className="pt-6">
        <Button onClick={handleNext} className="px-8 py-2">
          Continue
        </Button>
      </div>
    </div>
  );
}
