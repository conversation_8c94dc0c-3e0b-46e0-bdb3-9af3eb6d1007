'use server';

import prisma from '@/lib/prisma';
import bcrypt from "bcryptjs";
import { adminPasswordSchema, adminSchema, TAdminForm, TAdminPassword, TUpdateAdminForm } from './admins.schema';
import { revalidatePath } from 'next/cache';
import { deleteFromBunny } from '../bunny/bunny.action';
import { adminWelcomeMail } from '@/lib/emails/admin';


export async function createAdmin(unsafeData: TAdminForm) {
  try {
    const {success, data, error } = adminSchema.safeParse(unsafeData);
    if (!success) {
      const errorMessage = error.errors.map((error) => error.message).join(",")
      console.log(errorMessage)
      return { success: false, message: errorMessage };
    }

    const isFirstAdmin = await prisma.adminProfile.count() === 0;

    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      return { success: false, error: "User with this email already exists" };
    }

    const hashedPassword = await bcrypt.hash(data.password!, 10);

    const admin = await prisma.adminProfile.create({
      data: {
        password: hashedPassword,
        isFirst: isFirstAdmin,
        user: {
          create: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            phone: data.phone,
            role: "ADMIN" as const,
            status: "APPROVED" as const,
          },
        },
      },
      include: {
        user: true,
      },
    });

    const siteName = await prisma.cMSConfig.findFirst({
      select: { siteName: true },
    });

    adminWelcomeMail({
      email: admin.user.email,
      firstName: admin.user.firstName,
      password: data.password!,
      siteName: siteName?.siteName || 'Complexus Pathways',
    })

    revalidatePath('/dashboard/admins')

    return { success: true, message: 'Admin created', admin };
  } catch (error) {
    console.error("Error creating admin:", error);
    return { success: false, error: "Failed to create admin" };
  }
}

export async function updateAdmin(adminId: string, unsafeData: Partial<TUpdateAdminForm>) {
  try {
    if (!unsafeData || !adminId) return { success: false, message: "Please provide all required fields" };

    const {success, data, error } = adminSchema.safeParse(unsafeData);
    if (!success) {
      const errorMessage = error.errors.map((error) => error.message).join(",")
      console.log(errorMessage)
      return { success: false, message: errorMessage };
    }

    const admin = await prisma.adminProfile.update({
      where: { id: adminId },
      data: {
        user: {
          update: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            phone: data.phone,
          },
        },
      },
      include: {
        user: true,
      },
    });

    revalidatePath('/dashboard/admins')

    return { success: true, message: 'Admin updated', admin };
  } catch (error) {
    console.error("Error updating admin:", error);
    return { success: false, error: "Failed to update admin" };
  }
}

export async function updateAdminPassword(adminId: string, unsafeData: TAdminPassword) {
  try {
    if (!unsafeData || !adminId) return { success: false, message: "Please provide all required fields" };

    const {success, data, error } = adminPasswordSchema.safeParse(unsafeData);
    if (!success) {
      const errorMessage = error.errors.map((error) => error.message).join(",")
      console.log(errorMessage)
      return { success: false, message: errorMessage };
    }

    const hashedPassword = await bcrypt.hash(data.password, 10);

    const admin = await prisma.adminProfile.update({
      where: { id: adminId },
      data: {
        password: hashedPassword,
      },
      include: {
        user: true,
      },
    });

    revalidatePath('/dashboard/admins')

    return { success: true, admin };
  } catch (error) {
    console.error("Error updating admin password:", error);
    return { success: false, error: "Failed to update admin password" };
  }
}

export const deleteAdmin = async (adminId: string) => {
  try {
    const admin = await prisma.adminProfile.delete({
      where: { id: adminId },
    });
    const user = await prisma.user.delete({
      where: { id: admin.userId },
      select: { fileId: true }
    });

    if (user?.fileId) {
      await deleteFromBunny(user.fileId)
    }

    revalidatePath('/dashboard/admins')

    return { success: true };
  } catch (error) {
    console.error("Error deleting admin:", error);
    return { success: false, error: "Failed to delete admin" };
  }
};
