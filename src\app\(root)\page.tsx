import { getCMSData } from "@/lib/server/action/frontend/frontend.action";
import DashboardPreview from "./_components/sections/dashboard-preview";
import { FeaturesSection } from "./_components/sections/features-section";
import { HeroSection } from "./_components/sections/hero-section";
import { NewsletterSection } from "./_components/sections/newsletter-section";

export default async function HomePage() {
  const data = await getCMSData();

  if (!data) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Failed to load CMS data</h2>
          <p className="text-muted-foreground mb-4">
            There was an error loading the content management data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <HeroSection
        siteName={data.site.name}
        heroTitle={data.hero.title}
        heroSubtitle={data.hero.subtitle}
        heroCtaText={data.hero.ctaText}
        heroCtaLink={data.hero.ctaLink}
      />
      <DashboardPreview heroImage={data.hero.image} />
      <FeaturesSection
        featuresTitle={data.features.title}
        featuresDescription={data.features.description || ""}
        featuresItems={data.features.items}
      />
      <NewsletterSection
        newsletterTitle={data.newsletter.title}
        newsletterPlaceholder={data.newsletter.placeholder}
        newsletterButtonText={data.newsletter.buttonText}
      />
    </div>
  );
}
