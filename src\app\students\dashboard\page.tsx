import CourseDetails from "./_components/sections/dashboard/course-details";
import MeetingCard from "./_components/sections/dashboard/meeting-card";
import UpcomingSchedules from "./_components/sections/dashboard/upcoming-schedules";
import AssessmentEval from "./_components/sections/dashboard/assessment-eval";
import ModuleReport from "./_components/sections/dashboard/module-report";
import ActivitiesReport from "./_components/sections/dashboard/activities-report";
import { auth } from "@/lib/server/auth";
import { getStudentDashboardData } from "@/lib/server/action/students";

export default async function MainDashboard() {
  const session = await auth();
  if (!session || !session.user) {
    return null;
  }
  const data = await getStudentDashboardData(session.user.profileId);

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="flex items-center gap-2">
        <h1 className="text-3xl font-bold">Hello {session.user.firstName}</h1>
        <span className="text-3xl">👋</span>
      </div>
      <p className="text-muted-foreground">{"Let's learn something today"}</p>

      <div className="grid grid-cols-1 xl:grid-cols-5 xl:gap-x-6 gap-y-6">
        {/* My Course */}
        <CourseDetails courseData={data?.courseData} />

        {/* Ongoing Conference */}
        <MeetingCard meeting={data?.meeting} />
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 xl:gap-x-6 gap-y-6">
        {/* Upcoming Schedules */}
        <UpcomingSchedules upcomingSchedules={data?.upcomingSchedules} />

        {/* Assessment Evaluation */}
        <AssessmentEval
          studentId={session.user.profileId}
          completed={data?.assessmentEval?.completed || 0}
          notYet={data?.assessmentEval?.notYet || 0}
        />
      </div>

      {/* Assessment Report */}
      <div className="space-y-6">
        <ModuleReport
          modules={data?.modules}
          courseName={data?.courseData?.title || "My Course"}
        />

        <ActivitiesReport activities={data?.activities} />
      </div>
    </div>
  );
}
