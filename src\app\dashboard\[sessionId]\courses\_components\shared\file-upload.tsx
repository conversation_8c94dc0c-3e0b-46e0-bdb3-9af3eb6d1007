"use client";

import { useRef } from "react";

type FileUploadProps = {
  onSuccess: (files: File[]) => void;
  onError?: (error: string) => void;
  accept?: string;
  multiple?: boolean;
  children: React.ReactNode;
  className?: string;
};

// Generic File Upload Component
export function FileUpload({
  onSuccess,
  // onError,
  accept,
  multiple = false,
  children,
  className = "",
}: FileUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files as FileList) as File[];
    if (files.length > 0) {
      onSuccess(files);
    }
  };

  return (
    <div className={`relative ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileSelect}
        className="hidden"
      />
      <div
        onClick={() => fileInputRef.current?.click()}
        className="cursor-pointer"
      >
        {children}
      </div>
    </div>
  );
}
