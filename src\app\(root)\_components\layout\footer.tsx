import Image from "next/image";

type FooterProps = {
  site: {
    footerLogo: string;
  };
  contact: {
    title: string;
    email: string;
    mobile: string;
    landline: string;
    address: string;
  };
};

export function Footer({ site, contact }: FooterProps) {
  return (
    <footer className="bg-gray-100 py-12">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-8">
          <div className="flex items-start">
            <Image
              src={site.footerLogo || "/images/placeholder.svg"}
              alt="logo"
              width={180}
              height={180}
            />
          </div>

          <div>
            <h4 className="font-semibold text-gray-900 mb-4">
              {contact.title}
            </h4>
            <div className="space-y-2 text-sm text-gray-600">
              <p>
                <span className="font-medium">Email:</span> {contact.email}
              </p>
              <p>
                <span className="font-medium">Mobile:</span> {contact.mobile}
              </p>
              <p>
                <span className="font-medium">Landline:</span>{" "}
                {contact.landline}
              </p>
              <p>
                <span className="font-medium">Address:</span> {contact.address}
              </p>
            </div>
          </div>
        </div>

        <div className="border-t mt-8 pt-8 text-center text-sm text-gray-600">
          © 2025 Complexus Pathways. All Rights Reserved
        </div>
      </div>
    </footer>
  );
}
