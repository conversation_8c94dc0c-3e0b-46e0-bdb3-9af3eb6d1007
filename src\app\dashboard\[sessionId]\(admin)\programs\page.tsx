import { Button } from "@/components/ui/button";
import { Suspense } from "react";
import { DataTable } from "../../_components/table/data-table";
import PageWrapper from "../../_components/layout/PageWrapper";
import Loading from "../../_components/Loading";
import { getPrograms } from "@/lib/server/action/programs";
import { columns } from "./columns";
import { CustomSheet } from "@/components/shared/CustomSheet";
import ProgramForm from "@/components/form/ProgramForm";

const breadcrumbItems = [
  { label: "Home", href: "/dashboard" },
  { label: "Program" },
];

// const pageAssess = ["super-program"];

async function SuspendedComponent({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) {
  const { sessionId } = await params;
  const { programs } = await getPrograms({
    sessionId,
  });

  return (
    <>
      <DataTable columns={columns} data={programs} />
    </>
  );
}

const AllProgramsPAge = ({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) => {
  const RenderButton = () => (
    <CustomSheet
      title="Create Program"
      trigger={<Button size="sm">Add Program</Button>}
    >
      <ProgramForm />
    </CustomSheet>
  );

  return (
    <PageWrapper
      pgTitle="Manage Programs"
      breadcrumbItems={breadcrumbItems}
      headerButton={<RenderButton />}
    >
      <Suspense fallback={<Loading />}>
        <SuspendedComponent params={params} />
      </Suspense>
    </PageWrapper>
  );
};

export default AllProgramsPAge;
