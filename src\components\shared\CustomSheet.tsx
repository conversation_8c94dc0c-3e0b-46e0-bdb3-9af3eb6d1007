"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";

interface SheetProps {
  title: string;
  trigger: React.ReactNode | string;
  children: React.ReactNode;
  asChild?: boolean;
  side?: "top" | "bottom" | "left" | "right";
  description?: string;
}

export function CustomSheet({
  title,
  trigger,
  children,
  asChild = true,
  side = "right",
  description,
}: SheetProps) {
  return (
    <Sheet>
      {!asChild ? (
        <SheetTrigger className="hover:text-primary cmw-dropdown-child text-start px-2 py-1 text-[14px] hover:bg-muted rounded-sm">
          {trigger}
        </SheetTrigger>
      ) : (
        <SheetTrigger asChild>{trigger}</SheetTrigger>
      )}
      <SheetContent side={side} className="overflow-y-auto no-scrollbar pb-4">
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
        </SheetHeader>
        {description ? (
          <p className="text-xs font-medium text-muted-foreground mb-2">
            {description}
          </p>
        ) : null}
        <div className="px-5">{children}</div>
      </SheetContent>
    </Sheet>
  );
}
