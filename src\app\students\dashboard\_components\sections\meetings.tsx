"use client";

import {
  joinMeeting,
  Meeting,
} from "@/lib/server/action/courses/virtual-classrooms";
import { toast } from "sonner";
import { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, Copy, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import JoinMeetingModal from "@/components/shared/JoinMeetingModal";
import { userTimezone } from "@/lib/formatDate";

export default function MeetingsSection({
  meetings,
  userId,
}: {
  meetings: Meeting[];
  userId: string;
}) {
  const [filter, setFilter] = useState<"active" | "upcoming" | "live" | "past">(
    "active"
  );
  const [showJoinModal, setShowJoinModal] = useState(false);

  const filteredMeetings = meetings.filter((meeting) => {
    const now = new Date();
    const startTime = new Date(meeting.scheduledAt);
    const endTime = new Date(meeting.endedAt);

    switch (filter) {
      case "upcoming":
        return startTime > now;
      case "live":
        return startTime <= now && now <= endTime;
      case "past":
        return endTime <= now;
      case "active":
        return startTime > now || (startTime <= now && now <= endTime);
      default:
        return true;
    }
  });

  const handleJoinMeeting = async (meetingId: string) => {
    const res = await joinMeeting(meetingId);
    if (res.joinUrl) {
      window.open(res.joinUrl, "_blank");
    } else if (res.error) {
      toast.error(res.error);
    }
  };

  return (
    <>
      <div className="py-5 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Course Meetings
          </h3>
          <div className="flex space-x-2">
            <button
              onClick={() => setFilter("active")}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                filter === "active"
                  ? "bg-indigo-100 text-indigo-700"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Active
            </button>
            <button
              onClick={() => setFilter("upcoming")}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                filter === "upcoming"
                  ? "bg-indigo-100 text-indigo-700"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Upcoming
            </button>
            <button
              onClick={() => setFilter("live")}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                filter === "live"
                  ? "bg-indigo-100 text-indigo-700"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Live
            </button>
            <button
              onClick={() => setFilter("past")}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                filter === "past"
                  ? "bg-indigo-100 text-indigo-700"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Past
            </button>
          </div>
        </div>
      </div>

      {filteredMeetings.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No meeting found.</p>
        </div>
      ) : (
        <ul className="grid grid-cols-1 md:grid-cols-2 2xl:grid-cols-3 gap-6">
          {filteredMeetings.slice(0, 9).map((meeting) => (
            <MeetingCard
              key={meeting.id}
              meeting={meeting}
              userId={userId}
              onJoin={handleJoinMeeting}
              setShowJoinModal={setShowJoinModal}
            />
          ))}
        </ul>
      )}

      {showJoinModal && (
        <JoinMeetingModal onClose={() => setShowJoinModal(false)} />
      )}
    </>
  );
}

function MeetingCard({
  meeting,
  userId,
  onJoin,
  setShowJoinModal,
}: {
  meeting: Meeting;
  userId: string;
  onJoin: (meetingId: string) => void;
  setShowJoinModal: (value: boolean) => void;
}) {
  const isCreator = meeting.creator.id === userId;
  const now = new Date();
  const startTime = new Date(meeting.scheduledAt);
  const endTime = new Date(
    meeting.scheduledAt.getTime() + meeting.duration * 60 * 1000
  );
  const canJoin =
    now >= new Date(startTime.getTime() - 10 * 60 * 1000) && now <= endTime;
  const creatorName =
    meeting.creator.firstName + " " + meeting.creator.lastName;

  const RenderCopyButton = ({
    value,
    size,
  }: {
    value: string;
    size: number;
  }) => (
    <button
      onClick={() => {
        navigator.clipboard.writeText(value);
        toast.success("Copied to clipboard");
      }}
      className="cursor-pointer"
    >
      <Copy size={size} className="text-muted-foreground" />
    </button>
  );

  return (
    <Card className="hover:shadow-lg transition-shadow h-fit">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div>
              <CardTitle className="text-lg">{meeting.title}</CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge
                  variant={
                    canJoin
                      ? "default"
                      : startTime > now
                      ? "secondary"
                      : "destructive"
                  }
                >
                  {canJoin ? "Live" : startTime > now ? "Scheduled" : "Ended"}
                </Badge>
                <p className="flex items-center text-sm text-gray-500">
                  <CalendarIcon className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" />
                  {userTimezone(meeting.scheduledAt, "PPP, p")} -{" "}
                  {userTimezone(meeting.endedAt, "p")}
                </p>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Created by: {isCreator ? "You" : creatorName}
              </p>
              {canJoin ? (
                <div>
                  <div className="flex gap-2 mt-4">
                    <p className="text-xs">
                      Join URL:{" "}
                      <span className="text-primary">
                        {meeting.zoomJoinUrl}
                      </span>
                    </p>
                    <RenderCopyButton value={meeting.zoomJoinUrl!} size={16} />
                  </div>
                  <p className="text-xs flex items-center gap-2 mt-2">
                    ZoomId:{" "}
                    <span className="text-primary">
                      {meeting.zoomMeetingId}
                    </span>
                    <RenderCopyButton
                      value={meeting.zoomMeetingId!}
                      size={13}
                    />
                  </p>
                  <p className="text-xs flex items-center gap-2">
                    Zoom Password:{" "}
                    <span className="text-primary">{meeting.zoomPassword}</span>
                    <RenderCopyButton value={meeting.zoomPassword!} size={13} />
                  </p>
                </div>
              ) : null}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-gray-600 text-sm">{meeting.description}</p>

        {canJoin && (
          <div className="flex gap-4 w-full">
            <Button onClick={() => onJoin(meeting.id)} className="flex-1">
              <Users className="w-4 h-4 mr-2" />
              Join
            </Button>
            <Button
              variant="outline"
              className="flex-1"
              onClick={() => setShowJoinModal(true)}
            >
              Manual Join
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
