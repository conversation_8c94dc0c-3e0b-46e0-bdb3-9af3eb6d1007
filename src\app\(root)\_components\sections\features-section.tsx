import Image from "next/image";
import SmallTitle from "../shared/small-title";

type FeaturesSectionProps = {
  featuresTitle: string;
  featuresDescription: string;
  featuresItems: Array<{
    title: string;
    details: string[];
    image: string;
  }>;
};

export function FeaturesSection({
  featuresTitle,
  featuresDescription,
  featuresItems,
}: FeaturesSectionProps) {
  return (
    <section className="w-full py-16">
      <div className="container px-4 md:px-6 max-w-7xl mx-auto">
        <div className="flex justify-center mb-8">
          <SmallTitle logo="⚡" title="Top Features" />
        </div>

        <div className="text-center mb-12">
          <h2 className="text-4xl lg:text-5xl font-bold tracking-tight mb-4">
            {featuresTitle}
          </h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
            {featuresDescription}
          </p>
        </div>

        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuresItems.map((feature, index) => (
            <div
              key={index}
              className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow h-fit"
            >
              <div className="flex justify-center mb-4">
                <div className="w-40 h-40">
                  <Image
                    src={feature.image || "/placeholder.svg?height=80&width=80"}
                    alt={feature.title}
                    width={160}
                    height={160}
                    className="rounded-lg object-contain"
                    placeholder="blur"
                    blurDataURL="/images/placeholder-blur.jpg"
                  />
                </div>
              </div>
              <h3 className="font-semibold mb-2 text-xl">{feature.title}</h3>
              <ul className="space-y-2">
                {feature.details.map((detail, detailIndex) => (
                  <li
                    key={detailIndex}
                    className="flex items-center space-x-2 text-sm text-muted-foreground"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-rose-600"
                    >
                      <polyline points="20 6 9 17 4 12" />
                    </svg>
                    <span className="font-medium">{detail}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
