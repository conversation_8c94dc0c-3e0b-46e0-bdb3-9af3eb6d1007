"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { Button } from "../ui/button";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import {
  programSchema,
  TProgramForm,
} from "@/lib/server/action/programs/program.schema";
import { FormInputField } from "../form-element/input-field";
import { FormTextareaField } from "../form-element/text-area";
import { createProgram, updateProgram } from "@/lib/server/action/programs";
import { useParams } from "next/navigation";

export default function ProgramForm({
  programId,
  programData,
}: {
  programId?: string;
  programData?: TProgramForm;
}) {
  // get session id from url
  const params = useParams<{ sessionId: string }>();
  if (!params.sessionId) throw new Error("Session ID is required");

  const form = useForm<TProgramForm>({
    resolver: zod<PERSON><PERSON><PERSON>ver(programSchema),
    defaultValues: programData ?? {
      name: "",
      description: "",
    },
    mode: "onChange",
  });

  const onSubmit = async (values: TProgramForm) => {
    const res = programData
      ? await updateProgram(programId as string, values)
      : await createProgram(params.sessionId, values);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormInputField
          control={form.control}
          name="name"
          label="Name"
          placeholder="Enter program name"
        />
        <FormTextareaField
          control={form.control}
          name="description"
          label="Description"
          placeholder="Enter program description"
        />
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader /> {programData ? "Updating Program" : "Create Program"}
              </>
            ) : (
              <>{programData ? "Update Program" : "Create Program"}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
