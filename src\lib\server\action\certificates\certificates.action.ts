'use server'

import { deleteFromBunny, generateFileUploadUrl } from '../bunny/bunny.action';
import prisma from '@/lib/prisma';
import { revalidatePath } from 'next/cache';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib'
import axios from 'axios';

export async function getCertificate(certificateId: string) {
  try {
    const certificate = await prisma.studentCertificate.findUnique({
      where: { id: certificateId },
      include: {
        student: true,
        template: true,
      },
    });

    return certificate;
  } catch (error) {
    console.error("Error fetching certificate:", error);
    throw new Error("Failed to fetch certificate.");
  }
}

export async function saveCertificate(studentId: string) {
  try {
    const studentData = await prisma.studentProfile.findUnique({
      where: { id: studentId },
      select: {
        user: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        enrollment: {
          select: {
            course: {
              select: {
                title: true,
                certificateTemplate: {
                  select: {
                    template: {
                      select: {
                        id: true,
                        fileId: true,
                        fileUrl: true,
                        fields: true,
                      },
                    }
                  },
                }
              },
            },
          },
        },
      },
    });

    if (!studentData || !studentData.enrollment || !studentData.enrollment.course) {
      return { success: false, error: 'Student not found' };
    }

    let courseCertificate = studentData.enrollment.course.certificateTemplate?.template;

    if (!courseCertificate) {
      const certTemplate = await prisma.certificateTemplate.findFirst({
        select: {
          id: true,
          fileId: true,
          fileUrl: true,
          fields: true,
        },
      });

      if (!certTemplate) {
        return { success: false, error: 'No certificate template found' };
      }

      courseCertificate = certTemplate;
    }

    const data = {
      name: `${studentData.user.firstName} ${studentData.user.lastName}`,
      fields: courseCertificate.fields as { name: string; x: number; y: number; position: "left" | "center" | "right" }[],
      courseName: studentData.enrollment.course.title,
      templateId: courseCertificate.id,
      templateFileUrl: courseCertificate.fileUrl,
    }

    const cert = await prisma.studentCertificate.findUnique({
      where: { studentId_templateId: { studentId, templateId: data.templateId } },
      select: { fileId: true, previewId: true },
    })

    if (cert?.fileId) {
      await deleteFromBunny(cert.fileId)
    }

    const certificate = await generateCertificate({ fileUrl: data.templateFileUrl, studentName: data.name, courseName: data.courseName, fields: data.fields })
    if (!certificate.success || !certificate.fileBuffer || !certificate.fileName) {
      return { success: false, error: certificate.error };
    }

    const { fileBuffer, fileName } = certificate    

    const { fileId, uploadUrl, accessKey, cdnUrl } = await generateFileUploadUrl(fileName);

    const res = await axios.put(uploadUrl, fileBuffer, {
      headers: {
        'AccessKey': accessKey,
        'Content-Type': 'application/pdf',
        'Content-Length': fileBuffer.length,
      },
      maxBodyLength: Infinity,
      maxContentLength: Infinity,
    });

    if (!res.status.toString().startsWith('2')) {
      throw new Error(`Failed to upload file. Status: ${res.status}`);
    }

    await prisma.studentCertificate.upsert({
      where: { studentId_templateId: { studentId, templateId: data.templateId } },
      update: {
        previewId: '',
        previewUrl: '',
        fileId,
        fileUrl: cdnUrl,
        mimeType: 'image/svg+xml',
        originalName: fileName,
        fileSize: Buffer.byteLength(fileBuffer),
      },
      create: {
        previewId: '',
        previewUrl: '',
        fileId,
        fileUrl: cdnUrl,
        mimeType: 'image/svg+xml',
        originalName: fileName,
        fileSize: Buffer.byteLength(fileBuffer),
        studentId,
        templateId: data.templateId
      },
    })
    
    revalidatePath('/students/dashboard/certificate')

    return { success: true, message: 'Certificate created', cdnUrl }
  } catch (error) {
    console.error(error)
    return { success: false, error: 'Failed to create certificate' }
  }
}

export async function deleteCertificate(certificateId: string) {
  try {
    const cert = await prisma.studentCertificate.findUnique({
      where: { id: certificateId },
      select: { fileId: true }
    })

    if (cert?.fileId) {
      await deleteFromBunny(cert.fileId)
    }

    await prisma.studentCertificate.delete({
      where: { id: certificateId }
    })
    
    revalidatePath('/admin/certificates')

    return { success: true }
  } catch (error) {
    console.error(error)
    return { success: false, error: 'Failed to delete certificate' }
  }
}

export async function generateCertificate({fileUrl, studentName, courseName, fields}: {fileUrl: string, studentName: string, courseName: string, fields: { name: string; x: number; y: number; position: "left" | "center" | "right" }[]}) {
  if (!fileUrl) {
    throw new Error('Please provide a valid file Url');
  }
  
  try {
    const today = new Date();
    const completionDate = today.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: '2-digit',
    });

    // const uploadDir = path.join(process.cwd(), 'src', 'uploads', 'certificates');
    // const fileName = `${studentName.replace(/\s+/g, '_')}_${Math.random()}_certificate.pdf`;
    const fileName = `${studentName.replace(/\s+/g, '_')}_certificate.pdf`;

    const response = await axios.get(fileUrl, {
      responseType: 'arraybuffer',
    });

    const pdfBuffer = Buffer.from(response.data);
    const pdfDoc = await PDFDocument.load(pdfBuffer);
    const font = await pdfDoc.embedFont(StandardFonts.TimesRomanItalic);

    fields.forEach(field => {
      const page = pdfDoc.getPage(0);
      const { width, height } = page.getSize();
      const { name, x, y, position } = field;

      let nameValue: string;
      let fontSize: number;

      switch (name) {
        case 'name':
          nameValue = studentName;
          fontSize = 22;
          break;
        case 'courseName':
          nameValue = courseName;
          fontSize = 15;
          break;
        case 'date':
          nameValue = completionDate;
          fontSize = 14;
          break;
        default:
          nameValue = '';
          fontSize = 22;
      }
      
      const textWidth = font.widthOfTextAtSize(nameValue, fontSize);
      let textX = x;

      if (position === "center") {
        textX = (width - textWidth) / 2;
      } else if (position === "right") {
        textX = width - textWidth - x;
      }

      page.drawText(nameValue, {
        x: textX,
        y: height - y,
        font,
        size: fontSize,
        color: rgb(0, 0, 0),
      });
    });
    const pdfBytes = await pdfDoc.save();

    // const outputPath = path.join(uploadDir, 'output', fileName);
    // await fs.mkdir(path.dirname(outputPath), { recursive: true });
    // await fs.writeFile(outputPath, pdfBytes);

    return { success: true, fileBuffer: pdfBytes, fileName };
  } catch (error) {
    console.error("Error generating certificate:", error)
    return { success: false, error: "Failed to generate certificate" }
  }
}

export async function getStudentCertificate(studentId: string) {
  try {
    const certificate = await prisma.studentCertificate.findFirst({
      where: { studentId },
      include: {
        student: {
          select: {
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
            enrollment: {
              select: {
                course: {
                  select: {
                    title: true,
                  },
                },
              },
            }
          }
        },
      },
    });

    if (!certificate) {
      return null;
    }

    const site = await prisma.cMSConfig.findFirst({
      select: { siteName: true },
    });

    return {
      id: certificate.id,
      fullName: `${certificate.student.user.firstName} ${certificate.student.user.lastName}`,
      courseName: certificate.student.enrollment?.course.title || 'Unknown Course',
      fileSize: certificate.fileSize ? `${(certificate.fileSize / 1024).toFixed(2)} KB` : 'N/A',
      issueDate: new Date(certificate.createdAt).toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit',
      }),
      certificateId: certificate.fileId,
      issuingOrganization: site?.siteName || 'Unknown Organization',
      previewUrl: certificate.previewUrl,
      fileUrl: certificate.fileUrl,
    };
  } catch (error) {
    console.error("Failed to fetch student certificate:", error)
    throw new Error("Failed to fetch student certificate.");
  }
}