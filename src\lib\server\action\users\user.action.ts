'use server'

import { comparePassword } from "@/lib/passwordHandler"
import prisma from "@/lib/prisma"
import { deleteFromBunny } from "../bunny/bunny.action"
import { revalidatePath } from "next/cache"
import { auth, signIn, signOut } from "@/lib/server/auth";

export async function authenticateAdmin (email: string, password: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { email, role: "ADMIN" },
      include: {
        adminProfile: {
          select: {
            id: true,
            password: true,
            isFirst: true,
          }
        },
      },
    })
    
    if (!user || !user.adminProfile) {
      return null
    }

    const isPasswordValid = await comparePassword(password, user.adminProfile.password)

    if (!isPasswordValid) {
      return null
    }

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      isFirst: user.adminProfile.isFirst,
      role: user.role,
      fileUrl: user.fileUrl,
      profileId: user.adminProfile.id,
    }
  } catch (error) {
    console.log(error)
    return null
  }
}

export async function authenticateLoginCode (loginCode: string) {
  try {
    const code = await prisma.code.findUnique({
      where: { code: loginCode },
      include: {
        user: {
          select: {
            id: true,
            role: true,
            email: true,
            firstName: true,
            lastName: true,
            fileUrl: true,
          },
        },
      },
    })
    
    if (!code || !code.user) {
      return null
    }

    let profile: { profileId: string; courseId?: string }

    if (code.user.role === "TEACHER") {
      const teacherProfile = await prisma.teacherProfile.findUnique({
        where: { userId: code.userId, user: {status: "APPROVED"} },
        select: {
          id: true,
        },
      })
      
      if (!teacherProfile) {
        return null
      }

      profile = {
        profileId: teacherProfile.id,
      }
    } else if (code.user.role === "STUDENT") {
      const studentProfile = await prisma.studentProfile.findUnique({
        where: { userId: code.userId, user: {status: "APPROVED"} },
        select: {
          id: true,
          courseId: true,
        },
      })
      
      if (!studentProfile) {
        return null
      }

      profile = {
        profileId: studentProfile.id,
        courseId: studentProfile.courseId,
      }
    } else {
      return null
    }

    return {
      id: code.userId,
      email: code.user.email,
      firstName: code.user.firstName,
      lastName: code.user.lastName,
      role: code.user.role,
      fileUrl: code.user.fileUrl,
      ...profile, // teacherId, studentId, courseId
    }
  } catch (error) {
    console.log(error)
    return null
  }
}

export async function signInWithCredentials({type, email, password, loginCode}: {type: "ep" | "lc"; email?: string; password?: string, loginCode?: string}) {
  try {
    if ((type === "ep" && (!email || !password)) || (type === "lc" && !loginCode)) {
      return {  success: false, error: "Please fill all the required fields" }
    }

    await signIn('credentials', {type, email, password, loginCode, redirect: false})
    return { success: true, message: "Login successful"}
  } catch (error) {
    console.log(error);
    return { success: false, error: "Invalid login credentials."}
  }
}

export async function logout() {
  await signOut();
}

export async function uploadPhoto (userId: string, data: { fileId: string; fileUrl: string }) {
  try {  
    const activeSession = await prisma.session.findFirst({
      where: { isActive: true },
      select: {id: true}
    })

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { fileId: true }
    })
    
    if (user?.fileId) {
      await deleteFromBunny(user.fileId)
    }

    await prisma.user.update({
      where: { id: userId },
      data: {
        fileId: data.fileId,
        fileUrl: data.fileUrl,
      },
    });

    revalidatePath(`/dashboard/${activeSession?.id}/profile`)

    return { success: true, message: "Photo updated" };
  } catch (error) {
    console.log(error);
    return { success: false, error: "Failed to update photo" };
  }
}

export async function deletePhoto (userId: string) {
  try {  
    const activeSession = await prisma.session.findFirst({
      where: { isActive: true },
      select: {id: true}
    })

    
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { fileId: true }
    })
    
    if (user?.fileId) {
      await deleteFromBunny(user.fileId)
    }

    await prisma.user.update({
      where: { id: userId },
      data: {
        fileId: null,
        fileUrl: null,
      },
    });

    revalidatePath(`/dashboard/${activeSession?.id}/profile`)
    
    return { success: true, message: "Photo deleted" };
  } catch (error) {
    console.log(error);
    return { success: false, error: "Failed to delete photo" };
  }
}

export async function checkUser (userId: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        fileUrl: true
      },
    })
    
    return user
  } catch (error) {
    console.log(error)
    return null
  }
}

export async function checkActiveUser () {
  try {
    const user = await prisma.user.findFirst({
      select: {
        id: true,
      },
    })
    
    return user
  } catch (error) {
    console.log(error)
    return null
  }
}

export async function getUser (userId: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        fileUrl: true,
        createdAt: true,
      },
    })
    
    return user
  } catch (error) {
    console.log(error)
    return null
  }
}

export async function updateUser (userId: string, data: { firstName: string; lastName: string; email: string; phone?: string }) {
  try {
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data,
    })
    
    return { success: true, message: "User updated successfully", user: updatedUser }
  } catch (error) {
    console.log(error)
    return { success: false, error: "Failed to update user" }
  }
}

export async function searchUser (query: string) {
  const session = await auth()

  if (!session) {
    return { success: false, message: 'Unauthorized', users: [] }
  }

  if (!query || typeof query !== 'string') {
    return { success: false, message: 'Search query is required', users: [] }
  }

  try {
    const users = await prisma.user.findMany({
      where: {
        AND: [
          {
            id: {
              not: session.user.id // Exclude current user
            }
          },
          {
            OR: [
              { firstName: { contains: query } },
              { lastName: { contains: query } },
              { email: { contains: query } }
            ]
          }
        ]
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        fileUrl: true,
      },
      take: 10
    })
    
    return { success: true, message: 'Users found', users}
  } catch (error) {
    console.log(error)
    return { success: false, message: 'Failed to search users', users: [] }
  }
}