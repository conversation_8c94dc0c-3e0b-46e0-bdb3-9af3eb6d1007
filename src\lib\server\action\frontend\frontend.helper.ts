/* eslint-disable @typescript-eslint/no-explicit-any */

import { CMSData, getCMSData, updateCMSData } from "./frontend.action";

// Client-side data fetching with error handling
export async function fetchCMSData(): Promise<CMSData> {
  try {
    return await getCMSData();
  } catch (error) {
    console.error("Error fetching CMS data:", error);
    throw new Error("Failed to fetch CMS data");
  }
}

// Client-side data updating with error handling
export async function saveCMSData(
  data: Partial<CMSData>,
): Promise<void> {
  try {
    await updateCMSData(data);
  } catch (error) {
    console.error("Error saving CMS data:", error);
    throw new Error("Failed to save CMS data");
  }
}

// Utility function to update specific nested properties
export function updateNestedProperty(
  data: CMSData,
  path: string[],
  value: any
): CMSData {
  const newData = JSON.parse(JSON.stringify(data)); // Deep clone
  let current: any = newData;

  for (let i = 0; i < path.length - 1; i++) {
    if (!current[path[i]]) {
      current[path[i]] = {};
    }
    current = current[path[i]];
  }

  current[path[path.length - 1]] = value;
  return newData;
}

// Type-safe navigation item management
export function addNavigationItem(
  data: CMSData,
  item: { label: string; href: string }
): CMSData {
  const newData = { ...data };
  newData.navigation.items.push(item);
  return newData;
}

export function removeNavigationItem(data: CMSData, index: number): CMSData {
  const newData = { ...data };
  newData.navigation.items.splice(index, 1);
  return newData;
}

export function updateNavigationItem(
  data: CMSData,
  index: number,
  item: { label: string; href: string }
): CMSData {
  const newData = { ...data };
  newData.navigation.items[index] = item;
  return newData;
}

// Feature management utilities
export function addFeatureItem(
  data: CMSData,
  feature: { title: string; details: string[]; image: string }
): CMSData {
  const newData = { ...data };
  newData.features.items.push(feature);
  return newData;
}

export function removeFeatureItem(data: CMSData, index: number): CMSData {
  const newData = { ...data };
  newData.features.items.splice(index, 1);
  return newData;
}

export function updateFeatureItem(
  data: CMSData,
  index: number,
  updates: Partial<{ title: string; details: string[]; image: string }>
): CMSData {
  const newData = { ...data };
  newData.features.items[index] = {
    ...newData.features.items[index],
    ...updates,
  };
  return newData;
}

// Design element management utilities
export function addDesignElement(
  data: CMSData,
  element: { title: string; details: string[]; image: string }
): CMSData {
  const newData = { ...data };
  newData.about.designElements.push(element);
  return newData;
}

export function removeDesignElement(data: CMSData, index: number): CMSData {
  const newData = { ...data };
  newData.about.designElements.splice(index, 1);
  return newData;
}

export function updateDesignElement(
  data: CMSData,
  index: number,
  updates: Partial<{ title: string; details: string[]; image: string }>
): CMSData {
  const newData = { ...data };
  newData.about.designElements[index] = {
    ...newData.about.designElements[index],
    ...updates,
  };
  return newData;
}

// Validation utilities
export function validateCMSData(data: Partial<CMSData>): string[] {
  const errors: string[] = [];

  if (data.site?.name && data.site.name.trim().length === 0) {
    errors.push("Site name is required");
  }

  if (data.hero?.title && data.hero.title.trim().length === 0) {
    errors.push("Hero title is required");
  }

  if (data.contact?.email && !isValidEmail(data.contact.email)) {
    errors.push("Invalid email address");
  }

  if (data.features?.items) {
    data.features.items.forEach((item, index) => {
      if (!item.title || item.title.trim().length === 0) {
        errors.push(`Feature ${index + 1} title is required`);
      }
      if (!item.details || item.details.length === 0) {
        errors.push(`Feature ${index + 1} must have at least one detail`);
      }
    });
  }

  return errors;
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Data transformation utilities
export function transformCMSDataForAPI(data: CMSData): any {
  // Transform data for API calls if needed
  return {
    ...data,
    // Add any specific transformations here
  };
}

export function transformAPIDataToCMS(apiData: any): CMSData {
  // Transform API response back to CMS format
  return apiData as CMSData;
}