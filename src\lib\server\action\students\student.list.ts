'use server'

import { appConfig } from "@/config/app";
import prisma from "@/lib/prisma";
import { Prisma, UserStatus } from "@prisma/client";


export type StudentWithUserAndSchool = Prisma.StudentProfileGetPayload<{
  include: {
    user: {
      select: {
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        status: true,
        fileUrl: true,
      }
    };
    school: {
      select: {
        name: true,
        id: true
      }
    };
    program: {
      select: {
        name: true,
        id: true
      }
    };
    enrollment: {
      select: {
        id: true,
        course: {
          select: {
            title: true,
            id: true
          }
        }
      }
    }
  };
}>;


export async function getStudents({
  sessionId,
  page,
  search,
  sortby,
  status = "APPROVED",
}: {
  sessionId: string;
  page: number;
  search?: string;
  sortby?: string;
    status?: UserStatus;
}): Promise<{
  students: StudentWithUserAndSchool[]; // Corrected type here
  total: number;
}> {
  const whereClause: Prisma.StudentProfileWhereInput = {};
  const orderByClause: Prisma.StudentProfileOrderByWithRelationInput[] = [];

  whereClause.user = { status };
  whereClause.sessionId = sessionId;

  if (search) {
    whereClause.OR = [
      { user: { firstName: { contains: search } } },
      { user: { lastName: { contains: search } } },
      { user: { email: { contains: search } } },
      { school: { name: { contains: search } } },
      { program: { name: { contains: search } } },
      { enrollment: {  course: { title: { contains: search } } } },
    ];
  }

  if (sortby) {
    switch (sortby) {
      case "name":
        orderByClause.push({ user: { firstName: "asc" } });
        break;
      case "email":
        orderByClause.push({ user: { email: "asc" } });
        break;
      case "status":
        orderByClause.push({ user: { status: "asc" } });
        break;
      case "school":
        orderByClause.push({ school: { name: "asc" } });
        break;
      case "program":
        orderByClause.push({ program: { name: "asc" } });
        break;
      default:
        orderByClause.push({ user: { firstName: "asc" } });
        break;
    }
  } else {
    orderByClause.push({ user: { firstName: "asc" } });
  }

  const queryOptions: Prisma.StudentProfileFindManyArgs = {
    include: {
      user: {
        select: {
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          status: true,
          fileUrl: true,
        }
      },
      school: {
        select: {
          name: true,
          id: true
        }
      },
      program: {
        select: {
          name: true,
          id: true
        }
      },
      enrollment: {
        select: {
          id: true,
          course: {
            select: {
              title: true,
              id: true
            }
          }
        }
      }
    },
    where: whereClause,
    orderBy: orderByClause.length > 0 ? orderByClause : [{ user: { firstName: "asc" } }],
    skip: (page - 1) * appConfig.ITEMS_PER_PAGE,
    take: appConfig.ITEMS_PER_PAGE,
  };

  try {
    const students = await prisma.studentProfile.findMany(queryOptions) as StudentWithUserAndSchool[];

    const total = await prisma.studentProfile.count({
      where: whereClause,
    });

    return { students, total };
  } catch (error) {
    console.error("Error fetching students:", error);
    throw new Error("Failed to fetch students.");
  }
}

export async function getStudentProfile(studentId: string) {
  return await prisma.studentProfile.findUnique({
    where: { id: studentId },
    include: {
      user: {
        select: {
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          status: true,
          fileUrl: true
        }
      },
      program: {
        select: {
          name: true,
          id: true
        }
      },
      school: {
        select: {
          name: true,
          id: true
        }
      },
      enrollment: {
        include: {
          course: true
        }
      }
    }
  })
}

export async function getStudentCourseDetails(courseId: string, studentId: string) {
  const course = await prisma.course.findUnique({
    where: { id: courseId },
    include: {
      _count: {
        select: {
          enrollments: true,
          modules: true
        }
      },
      program: {
        select: {
          name: true,
        }
      },
      modules: {
        include: {
          resources: true,
          assessment: {
            include: {
              attempts: {
                where: { studentId },
                orderBy: { attemptNumber: 'desc' }
              }
            }
          },
          progress: {
            where: { studentId }
          }
        },
        orderBy: { order: 'asc' }
      },
      teacherAssignments: {
        select: {
          role: true,
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          },
        }
      },
      progress: {
        where: { studentId },
        select: {
          id: true,
          score: true,
          progress: true,
        }
      }
    },
  })
  
  return course
}

export async function checkStudent(studentId: string) {
  try {
    const student = await prisma.studentProfile.findUnique({
      where: { id: studentId },
      select: {
        id: true
      }
    })
    
    return student
  } catch (error) {
    console.error(error)
    return null
  }
}

export async function checkStudentLogin(studentId: string, courseId: string) {
  try {
    const activeSession = await prisma.session.findFirst({
      where: { isActive: true }
    })
    if (!activeSession) {
      return null
    }

    const student = await prisma.studentProfile.findUnique({
      where: { id: studentId, sessionId: activeSession.id, enrollment: { courseId } },
      select: {
        id: true
      }
    })

    return student
  } catch (error) {
    console.error(error)
    return null
  }
}



// Student Dashboard
export async function getStudentDashboardData(studentId: string) {
  try {
    const [courseData, meeting, upcomingSchedules, modules, activities, totalAssessments, totalCompletedAssessment] = await Promise.all([
      prisma.studentProfile.findFirst({
        where: { id: studentId }, 
        select: {
          enrollment: {
            select : {
              course: {
                select: {
                  title: true,
                  description: true,
                  fileUrl: true,
                  _count: {
                    select: {
                      enrollments: true
                    }
                  },
                  teacherAssignments: {
                    where: { role: "PRIMARY" },
                    select: {
                      role: true,
                      user: {
                        select: {
                          firstName: true,
                          lastName: true
                        }
                      }
                    }
                  },
                  progress: {
                    where: { studentId },
                    select: {
                      score: true
                    }
                  }
                }
              }
            }
          },
          program: {
            select: {
              name: true
            }
          }
        }
      }),
      prisma.meeting.findFirst({
        where: { course: { enrollments: { some: { studentId } } }, scheduledAt: { lte: new Date() }, endedAt: { gte: new Date() } },
        select: {
          id: true,
          title: true,
          description: true,
          scheduledAt: true,
          duration: true,
          creator: {
            select: {
              firstName: true,
              lastName: true
            }
          }
        } 
      }),
      prisma.meeting.findMany({
        where: { course: { enrollments: { some: { studentId: studentId } } }, scheduledAt: { gt: new Date() },  },
        select: {
          id: true,
          title: true,
          description: true,
          scheduledAt: true,
        },
        orderBy: { scheduledAt: 'asc' },
        take: 5
      }),
      prisma.module.findMany({ 
        where: { course: { enrollments: { some: { studentId } } } },
        select: {
          id: true,
          title: true,
          assessment: {
            select: {
              _count: {
                select: {
                  attempts: {
                    where: {studentId: studentId, status: 'COMPLETED'}
                  }
                }
              },
              attempts: {
                where: { studentId },
                select: {
                  score: true,
                },
                orderBy: { score: 'desc' },
                take: 1
              }
            }
          }
        },
        orderBy: { order: 'asc' }
      }),
      prisma.activity.findMany({
        where: { course: { enrollments: { some: { studentId } } } },
        select: {
          id: true,
          question: true,
          score: true,
          deadline: true,
          responses: {
            where: { studentId },
            select: {
              score: true,
              isGraded: true,
              status: true
            },
            orderBy: { submittedAt: 'desc' },
            take: 1
          }
        },
        orderBy: { createdAt: 'asc' }
      }),
      prisma.module.count({
        where: {
          course: { enrollments: { some: { studentId } } },
          assessment: { isNot: null }
        }
      }),
      prisma.assessmentAttempt.count({
        where: { studentId, status: 'COMPLETED', assessment: { module: { course: { enrollments: { some: { studentId } } } } }, attemptNumber: 1 },
      })
    ])

    const courseTeacher = courseData?.enrollment?.course.teacherAssignments.filter(t => t.role === "PRIMARY")[0]?.user

    return {
      courseData: courseData && courseData.enrollment ? {
        title: courseData.enrollment.course.title,
        description: courseData.enrollment.course.description || '',
        image: courseData.enrollment.course.fileUrl,
        totalStudents: courseData.enrollment.course._count.enrollments,
        program: courseData.program.name,
        teacher: courseTeacher?.firstName + " " + courseTeacher?.lastName || '',
      } : null,
      meeting: meeting ? {
        id: meeting.id,
        title: meeting.title,
        description: meeting.description || '',
        startTime: meeting.scheduledAt,
        endTime: new Date(meeting.scheduledAt.getTime() + meeting.duration * 60000),
        creator: meeting.creator
      } : null,
      upcomingSchedules: upcomingSchedules ? upcomingSchedules.map(schedule => ({
        id: schedule.id,
        title: schedule.title,
        description: schedule.description || '',
        startTime: schedule.scheduledAt,
      })) : [],
      modules: modules ? modules.filter(module => module.assessment && module.assessment.attempts.length > 0).map(module => ({
        id: module.id,
        title: module.title,
        attempts: module.assessment?._count.attempts ?? 0,
        score: (module.assessment?._count.attempts ?? 0) > 0 ? module.assessment?.attempts[0]?.score : null
      })) : [],
      activities: activities ? activities.map(activity => ({
        id: activity.id,
        passScore: activity.score,
        question: activity.question,
        deadline: activity.deadline,
        score: activity.responses?.[0]?.score || 0,
        isGraded: activity.responses?.[0]?.isGraded || false,
        status: activity.responses?.[0]?.status
      })) : [],
      assessmentEval: {
        completed: totalCompletedAssessment,
        notYet: totalAssessments - totalCompletedAssessment,
        initialGrade: courseData?.enrollment?.course.progress[0]?.score || 0
      }
    }
  } catch (error) {
    console.error("Error fetching student dashboard data:", error);
    return null
  }
}