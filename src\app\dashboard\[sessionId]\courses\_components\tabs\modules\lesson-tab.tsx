import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import LessonForm from "../../forms/LessonForm";
import { getLesson } from "@/lib/server/action/courses/modules/lessons";
import IframeVideoPlayer from "../../shared/IframePlayer";
import ViewLessonDocument from "../../shared/view-lesson-document";

export default async function LessonTab({ moduleId }: { moduleId: string }) {
  const lessonData = await getLesson(moduleId);

  return (
    <div className="pb-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Lesson Management</h1>
        <p className="text-gray-600">Create and manage your lesson content</p>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Lesson Preview */}
        <Card className="h-fit gap-3">
          <CardHeader>
            <CardTitle>Lesson Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {!lessonData ? (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">No lesson found.</p>
              </div>
            ) : (
              <>
                {/* Video Player */}
                <div className="relative aspect-video overflow-hidden rounded-lg bg-black">
                  {lessonData.type !== "VIDEO" ? (
                    <div className="flex flex-col gap-2 h-full items-center justify-center bg-gray-900">
                      <p className="text-sm opacity-75 text-center text-white">
                        {lessonData.originalName}
                      </p>
                      <ViewLessonDocument fileUrl={lessonData.fileUrl!} />
                    </div>
                  ) : (
                    <IframeVideoPlayer videoId={lessonData.fileId} />
                  )}
                  {lessonData.duration && (
                    <div className="absolute bottom-2 right-2 rounded bg-black/75 px-2 py-1 text-xs text-white">
                      {new Date(lessonData.duration * 1000)
                        .toISOString()
                        .slice(14, 19)}
                    </div>
                  )}
                </div>

                {/* Lesson Info */}
                <div className="space-y-3">
                  <h2 className="text-xl font-semibold text-gray-900">
                    {lessonData.title}
                  </h2>
                  <p className="text-gray-600 leading-relaxed">
                    {lessonData.description}
                  </p>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Lesson Form */}
        <Card className="h-fit">
          <CardHeader>
            <CardTitle>Lesson Form</CardTitle>
          </CardHeader>
          <CardContent>
            <LessonForm
              lessonData={{
                title: lessonData?.title ?? "",
                description: lessonData?.description ?? "",
                type: lessonData?.type ?? "VIDEO",
                fileId: lessonData?.fileId ?? "",
                fileUrl: lessonData?.fileUrl ?? "",
                duration: lessonData?.duration ?? 0,
                mimeType: lessonData?.mimeType ?? "",
                originalName: lessonData?.originalName ?? "",
                fileSize: lessonData?.fileSize ?? 0,
              }}
              lessonId={lessonData?.id}
              moduleId={moduleId}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
