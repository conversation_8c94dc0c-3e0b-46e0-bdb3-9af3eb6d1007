import { transporter } from "../mailTransporter";

export async function welcomeMail(email: string, firstName: string, siteName: string) {
  await transporter.sendMail({
    from: process.env.SMTP_USER,
    to: email,
    subject: `Welcome to ${siteName}`,
    text: `Dear ${firstName},\n\nThank you for registering with ${siteName}! We are pleased to inform you that we have received your registration request.\n\nOur admin team is currently reviewing your submission. You will receive an email notification once your registration has been approved.\n\nIf you have any questions in the meantime, feel free to reach out.\n\nBest regards,\nThe ${siteName} Team`,
  });
}

export async function approvedMail({email, firstName, loginCode, link, siteName}: {email: string, firstName: string, loginCode: string, link: string, siteName: string}) {
  await transporter.sendMail({
    from: process.env.SMTP_USER,
    to: email,
    subject: `${siteName} Approval - Your Registration is Complete`,
    text: `Dear ${firstName},\n\nCongratulations! Your registration with ${siteName} has been successfully approved.\n\nYou can now log in using the following code: ${loginCode}\n\nPlease visit the link below to access your account and complete your login:\n\n${link}\n\nIf you encounter any issues or need assistance, feel free to contact us.\n\nBest regards,\nThe ${siteName} Team`,
  });  
}

export async function rejectedMail({email, firstName, siteName}: {email: string, firstName: string, siteName: string}) {
  await transporter.sendMail({
    from: process.env.SMTP_USER,
    to: email,
    subject: `${siteName} Registration Status - Application Rejected`,
    text: `Dear ${firstName},\n\nWe regret to inform you that your registration for ${siteName} has not been approved at this time.\n\nWe encourage you to review your application and feel free to submit a new request if you wish to try again.\n\nIf you have any questions or need assistance, please don’t hesitate to contact us.\n\nBest regards,\nThe ${siteName} Team`,
  });  
}