/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import {
  completeAssessment,
  submitAnswer,
} from "@/lib/server/action/students/modules/assessments/assessments.action";

interface AssessmentInterfaceProps {
  attemptData: {
    id: string;
    startedAt: Date;
    assessment: {
      title: string;
      instructions: string | null;
      questions: any[];
    };
    responses: any[];
  };
}

export default function AssessmentInterface({
  attemptData,
}: AssessmentInterfaceProps) {
  const router = useRouter();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  // const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { assessment, responses } = attemptData;
  const questions = assessment.questions;
  const currentQuestion = questions[currentQuestionIndex];

  // Initialize answers from existing responses
  useEffect(() => {
    const initialAnswers: Record<string, any> = {};
    responses.forEach((response) => {
      initialAnswers[response.questionId] = response.response;
    });
    setAnswers(initialAnswers);
  }, [responses]);

  // Timer logic
  // useEffect(() => {
  //   if (!assessment.timeLimit) return;

  //   const startTime = new Date(attemptData.startedAt).getTime();
  //   const timeLimit = assessment.timeLimit * 60 * 1000; // Convert to milliseconds
  //   const endTime = startTime + timeLimit;

  //   const updateTimer = () => {
  //     const now = Date.now();
  //     const remaining = endTime - now;

  //     if (remaining <= 0) {
  //       setTimeRemaining(0);
  //       handleSubmitAssessment(); // Auto-submit when time expires
  //     } else {
  //       setTimeRemaining(Math.floor(remaining / 1000)); // Convert to seconds
  //     }
  //   };

  //   updateTimer();
  //   const interval = setInterval(updateTimer, 1000);

  //   return () => clearInterval(interval);
  // }, [assessment.timeLimit, attemptData.startedAt]);

  const handleAnswerChange = async (questionId: string, answer: any) => {
    setAnswers((prev) => ({ ...prev, [questionId]: answer }));

    // Auto-save answer
    await submitAnswer({
      attemptId: attemptData.id,
      questionId,
      response: answer,
    });
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleSubmitAssessment = async () => {
    if (
      !confirm(
        "Are you sure you want to submit your assessment? You cannot change your answers after submission."
      )
    ) {
      return;
    }

    setIsSubmitting(true);
    const result = await completeAssessment({ attemptId: attemptData.id });

    if (result.success) {
      toast.success("Assessment submitted successfully!");
      router.push(`/student/results/${attemptData.id}`);
    } else {
      toast.error(result.error || "Failed to submit assessment");
      setIsSubmitting(false);
    }
  };

  // const formatTime = (seconds: number) => {
  //   const minutes = Math.floor(seconds / 60);
  //   const remainingSeconds = seconds % 60;
  //   return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  // };

  const answeredQuestions = Object.keys(answers).length;
  const progress = (answeredQuestions / questions.length) * 100;

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold mb-2">{assessment.title}</h1>
            <p className="text-gray-600">
              Question {currentQuestionIndex + 1} of {questions.length}
            </p>
          </div>

          <div className="text-right">
            {/* {timeRemaining !== null && (
              <div
                className={`text-lg font-mono ${
                  timeRemaining < 300 ? "text-red-600" : "text-gray-700"
                }`}
              >
                Time Remaining: {formatTime(timeRemaining)}
              </div>
            )} */}

            <div className="text-sm text-gray-500 mt-1">
              Progress: {answeredQuestions}/{questions.length} answered
            </div>

            <div className="w-48 bg-gray-200 rounded-full h-2 mt-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Question */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="mb-4">
          <h2 className="text-xl font-semibold mb-3">
            {currentQuestion.question}
          </h2>

          {currentQuestion.questionType === "multiple_choice" && (
            <div className="space-y-3">
              {(currentQuestion.options as string[]).map((option, index) => (
                <label
                  key={index}
                  className="flex items-center space-x-3 cursor-pointer"
                >
                  <input
                    type="radio"
                    name={`question-${currentQuestion.id}`}
                    value={option}
                    checked={answers[currentQuestion.id] === option}
                    onChange={(e) =>
                      handleAnswerChange(currentQuestion.id, e.target.value)
                    }
                    className="w-4 h-4 text-blue-600"
                  />
                  <span>{option}</span>
                </label>
              ))}
            </div>
          )}

          {currentQuestion.questionType === "true_false" && (
            <div className="space-y-3">
              {["True", "False"].map((option) => (
                <label
                  key={option}
                  className="flex items-center space-x-3 cursor-pointer"
                >
                  <input
                    type="radio"
                    name={`question-${currentQuestion.id}`}
                    value={option}
                    checked={answers[currentQuestion.id] === option}
                    onChange={(e) =>
                      handleAnswerChange(currentQuestion.id, e.target.value)
                    }
                    className="w-4 h-4 text-blue-600"
                  />
                  <span>{option}</span>
                </label>
              ))}
            </div>
          )}

          {currentQuestion.questionType === "short_answer" && (
            <textarea
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={4}
              placeholder="Enter your answer..."
              value={answers[currentQuestion.id] || ""}
              onChange={(e) =>
                handleAnswerChange(currentQuestion.id, e.target.value)
              }
            />
          )}
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center">
          <button
            onClick={handlePreviousQuestion}
            disabled={currentQuestionIndex === 0}
            className="px-6 py-2 bg-gray-500 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 transition-colors"
          >
            Previous
          </button>

          <div className="flex space-x-2">
            {questions.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentQuestionIndex(index)}
                className={`w-10 h-10 rounded-full text-sm font-medium transition-colors ${
                  index === currentQuestionIndex
                    ? "bg-blue-600 text-white"
                    : answers[questions[index].id]
                    ? "bg-green-100 text-green-800"
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
              >
                {index + 1}
              </button>
            ))}
          </div>

          {currentQuestionIndex === questions.length - 1 ? (
            <button
              onClick={handleSubmitAssessment}
              disabled={isSubmitting}
              className="px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {isSubmitting ? "Submitting..." : "Submit Assessment"}
            </button>
          ) : (
            <button
              onClick={handleNextQuestion}
              className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Next
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
