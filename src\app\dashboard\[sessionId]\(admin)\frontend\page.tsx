import { <PERSON><PERSON> } from "@/components/ui/button";
import PageBuilder from "./_component/page-builder";
import { getCMSData } from "@/lib/server/action/frontend/frontend.action";

export default async function FrontendPage() {
  const cmsData = await getCMSData();

  if (!cmsData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Failed to load CMS data</h2>
          <p className="text-muted-foreground mb-4">
            There was an error loading the content management data.
          </p>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  return <PageBuilder cmsData={cmsData} />;
}
