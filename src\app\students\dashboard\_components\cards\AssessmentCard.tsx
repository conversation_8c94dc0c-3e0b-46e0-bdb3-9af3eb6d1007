/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState } from "react";
import Link from "next/link";
import { toast } from "sonner";
import { startAssessment } from "@/lib/server/action/students/modules/assessments/assessments.action";

interface AssessmentCardProps {
  assessment: {
    id: string;
    title: string;
    description: string | null;
    type: string;
    timeLimit: number | null;
    maxAttempts: number;
    passingScore: number;
    attempts: any[];
  };
  studentId: string;
  moduleId: string;
}

export default function AssessmentCard({
  assessment,
  studentId,
}: AssessmentCardProps) {
  const [isStarting, setIsStarting] = useState(false);

  const latestAttempt = assessment.attempts[0];
  const attemptsRemaining = assessment.maxAttempts - assessment.attempts.length;
  const hasPassedAssessment = assessment.attempts.some(
    (attempt) => attempt.passed
  );

  const handleStartAssessment = async () => {
    if (attemptsRemaining <= 0) {
      toast.error("No attempts remaining");
      return;
    }

    setIsStarting(true);
    const result = await startAssessment({
      assessmentId: assessment.id,
      studentId,
    });

    if (result.success) {
      // Redirect to assessment page
      window.location.href = `/student/assessment/${result.attempt?.id}`;
    } else {
      toast.error(result.error || "Failed to start assessment");
    }
    setIsStarting(false);
  };

  const getAssessmentTypeColor = (type: string) => {
    switch (type) {
      case "DIAGNOSTIC":
        return "bg-blue-100 text-blue-800";
      case "MODULE_ASSESSMENT":
        return "bg-yellow-100 text-yellow-800";
      case "ACHIEVEMENT_TEST":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="bg-white border rounded-lg p-4">
      <div className="flex justify-between items-start mb-3">
        <div>
          <h5 className="font-semibold text-lg">{assessment.title}</h5>
          {assessment.description && (
            <p className="text-gray-600 text-sm mt-1">
              {assessment.description}
            </p>
          )}
        </div>

        <span
          className={`px-2 py-1 text-xs rounded-full ${getAssessmentTypeColor(
            assessment.type
          )}`}
        >
          {assessment.type.replace("_", " ")}
        </span>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-4">
        <div>
          <span className="font-medium">Time Limit:</span>{" "}
          {assessment.timeLimit ? `${assessment.timeLimit} min` : "No limit"}
        </div>
        <div>
          <span className="font-medium">Passing Score:</span>{" "}
          {assessment.passingScore}%
        </div>
        <div>
          <span className="font-medium">Attempts Used:</span>{" "}
          {assessment.attempts.length}/{assessment.maxAttempts}
        </div>
        <div>
          <span className="font-medium">Status:</span>
          <span
            className={`ml-1 ${
              hasPassedAssessment ? "text-green-600" : "text-red-600"
            }`}
          >
            {hasPassedAssessment ? "Passed" : "Not Passed"}
          </span>
        </div>
      </div>

      {latestAttempt && (
        <div className="bg-gray-50 rounded p-3 mb-4">
          <h6 className="font-medium text-sm mb-2">Latest Attempt</h6>
          <div className="grid grid-cols-3 gap-2 text-sm">
            <div>
              <span className="text-gray-500">Score:</span>{" "}
              {latestAttempt.score?.toFixed(1)}%
            </div>
            <div>
              <span className="text-gray-500">Status:</span>
              <span
                className={`ml-1 ${
                  latestAttempt.passed ? "text-green-600" : "text-red-600"
                }`}
              >
                {latestAttempt.passed ? "Passed" : "Failed"}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Time:</span>{" "}
              {latestAttempt.timeSpent || 0} min
            </div>
          </div>
        </div>
      )}

      <div className="flex gap-2">
        {attemptsRemaining > 0 && (
          <button
            onClick={handleStartAssessment}
            disabled={isStarting}
            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {isStarting ? "Starting..." : "Start Assessment"}
          </button>
        )}

        {assessment.attempts.length > 0 && (
          <Link
            href={`/student/results?assessment=${assessment.id}`}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
          >
            View Results
          </Link>
        )}
      </div>
    </div>
  );
}
