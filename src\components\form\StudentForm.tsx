"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Loader } from "lucide-react";
import { useState, useEffect } from "react";

import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { toast } from "sonner";
import Link from "next/link";
import { FormInputField } from "../form-element/input-field";
import { FormComboboxField } from "../form-element/select-search";
import {
  studentSchema,
  TStudentForm,
} from "@/lib/server/action/students/students.schema";
import { createStudent, updateStudent } from "@/lib/server/action/students";
import { getSchoolOptions } from "@/lib/server/action/schools";
import { getCourseOptions } from "@/lib/server/action/courses";
import { getProgramOptions } from "@/lib/server/action/programs";

export function StudentForm({
  sessionId,
  enrollmentId,
  isAdmin = false,
  studentId,
  studentData,
}: {
  sessionId?: string;
  enrollmentId?: string;
  isAdmin?: boolean;
  studentId?: string;
  studentData?: TStudentForm;
}) {
  const [availableCourses, setAvailableCourses] = useState<
    { label: string; value: string }[]
  >([]);
  const [schools, setSchools] = useState<{ label: string; value: string }[]>(
    []
  );
  const [programs, setPrograms] = useState<{ label: string; value: string }[]>(
    []
  );

  const form = useForm<TStudentForm>({
    resolver: zodResolver(studentSchema),
    defaultValues: studentData ?? {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      school: "",
      course: "",
      program: "",
    },
  });

  // Watch for program changes to update available courses
  const selectedProgram = form.watch("program");

  useEffect(() => {
    async function getOptions() {
      const [schoolOptions, programOptions] = await Promise.all([
        getSchoolOptions(sessionId as string),
        getProgramOptions(sessionId as string),
      ]);
      setSchools(schoolOptions);
      setPrograms(programOptions);
    }
    getOptions();
  }, [sessionId]);

  useEffect(() => {
    if (selectedProgram) {
      async function fetchCourses() {
        const res = await getCourseOptions(selectedProgram);
        setAvailableCourses(res);
      }
      fetchCourses();
      // form.setValue("course", "");
    } else {
      setAvailableCourses([]);
    }
  }, [selectedProgram, form]);

  async function onSubmit(values: TStudentForm) {
    const res = studentData
      ? await updateStudent(studentId as string, enrollmentId as string, values)
      : await createStudent(values, sessionId as string, isAdmin);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.message);
    }
    form.reset();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid gap-6 md:grid-cols-2">
          <FormInputField
            control={form.control}
            name="firstName"
            label="First Name"
            placeholder="John"
          />
          <FormInputField
            control={form.control}
            name="lastName"
            label="Last Name"
            placeholder="Doe"
          />
          <FormInputField
            control={form.control}
            name="email"
            label="Email"
            type="email"
            placeholder="<EMAIL>"
          />
          <FormInputField
            control={form.control}
            name="phone"
            label="Phone"
            placeholder="(*************"
          />
          <FormComboboxField
            control={form.control}
            name="school"
            label="School"
            placeholder="Select a school"
            options={schools}
          />
          <FormComboboxField
            control={form.control}
            name="program"
            label="Program"
            placeholder="Select a program"
            options={programs}
          />
          <FormComboboxField
            control={form.control}
            name="course"
            label="Course"
            placeholder="Select a course"
            options={availableCourses}
            disabled={!selectedProgram}
          />
        </div>
        <div className="flex justify-end gap-2">
          {!studentData ? (
            <Button type="button" variant="outline">
              <Link href={`/dashboard/${sessionId}/students`}>Cancel</Link>
            </Button>
          ) : null}
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader />{" "}
                {studentData ? "Updating Student" : "Creating Student"}
              </>
            ) : (
              <>{studentData ? "Update Student" : "Create Student"}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
