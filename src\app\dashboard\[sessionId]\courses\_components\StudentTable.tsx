"use client";

import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Eye } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { getCourseStudents } from "@/lib/server/action/courses";
import AcademicReport from "@/components/shared/AcademicReport";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { DialogTrigger } from "@radix-ui/react-dialog";

export default function StudentTable({
  courseStudents,
}: {
  courseStudents: Awaited<ReturnType<typeof getCourseStudents>>;
}) {
  return (
    <div className="bg-white rounded-lg border overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>School</TableHead>
            <TableHead>Module Progress</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {courseStudents.total === 0 ? (
            <TableRow>
              <TableCell colSpan={5} className="h-24 text-center">
                No students found.
              </TableCell>
            </TableRow>
          ) : (
            <>
              {courseStudents.students.map((student) => {
                const progress = student.student.progressTracking;

                return (
                  <TableRow key={student.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="w-8 h-8">
                          {/* <AvatarImage src={"/images/placeholder.svg"} /> */}
                          <AvatarFallback>
                            {student.student.user.firstName.charAt(0) +
                              student.student.user.lastName.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">
                          {student.student.user.firstName}{" "}
                          {student.student.user.lastName}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-gray-600">
                      {student.student.user.email}
                    </TableCell>
                    <TableCell className="text-gray-600">
                      {student.student.school.name}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {progress?.completedAt ? (
                          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                            Passed
                          </Badge>
                        ) : (
                          <div className="flex items-center gap-2">
                            <Progress
                              value={progress?.progress || 0}
                              className="w-20"
                            />
                            <span className="text-sm text-gray-600">
                              {progress?.progress.toFixed(0) || 0}%
                            </span>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Dialog>
                        <DialogTrigger>
                          <Button variant="ghost" size="sm">
                            <Eye className="w-4 h-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogTitle className="mb-4 text-lg font-semibold">
                            <span className="text-muted-foreground">
                              Student -
                            </span>{" "}
                            {student.student.user.firstName}{" "}
                            {student.student.user.lastName}
                          </DialogTitle>
                          <AcademicReport studentId={student.student.id} />
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                );
              })}
            </>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
