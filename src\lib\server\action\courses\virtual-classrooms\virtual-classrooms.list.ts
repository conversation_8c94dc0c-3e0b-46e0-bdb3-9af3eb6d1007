'use server'

import prisma from "@/lib/prisma"
import { auth } from "@/lib/server/auth";
import { MeetingStatus } from "@prisma/client";

export type Meeting = {
  id: string;
  title: string;
  description?: string | null;
  status: MeetingStatus;
  scheduledAt: Date;
  endedAt: Date;
  duration: number;
  maxParticipants: number;
  chatEnabled: boolean;
  recordingEnabled: boolean;
  courseId: string;
  creator: {
    id: string;
    firstName: string;
    lastName: string;
    fileUrl?: string | null;
  };
  _count: {
    participants: number;
  };
  zoomStartUrl?: string | null;
  zoomJoinUrl?: string | null;
  zoomPassword?: string | null;
  zoomMeetingId?: string | null;
  zoomEmail: string;
  waitingRoomEnabled: boolean;
}

export async function getMeetings(courseId: string) {
  const session = await auth()

  if (!session) {
    throw new Error("Unauthorized")
  }

  try {
    const meetings = await prisma.meeting.findMany({
      where: {
        OR: [
            { creatorId: session.user.id, courseId },
            {
              participants: {
                some: {
                  userId: session.user.id
                }
              }, 
              courseId
            },
            { courseId }
          ]
      },
      select: {
        id: true,
        title: true,
        description: true,
        status: true,
        scheduledAt: true,
        endedAt: true,
        duration: true,
        zoomJoinUrl: true,
        zoomStartUrl: true,
        zoomPassword: true,
        zoomMeetingId: true,
        waitingRoomEnabled: true,
        maxParticipants: true,  
        chatEnabled: true,
        recordingEnabled: true,
        courseId: true,
        zoomEmail: true,
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            fileUrl: true
          }
        },
        participants: {
          select: { user: { select: { firstName: true, lastName: true, email: true } } }
        },
        _count: {
          select: { participants: true }
        }
      },
      orderBy: { scheduledAt: 'desc' }
    });

    return meetings;
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch meetings.")
  }
}

export async function getMeeting (meetingId: string) {
  try {
    const meeting = await prisma.meeting.findUnique({
      where: { id: meetingId },
      include: {
        course: {
          select: { title: true }
        },
        participants: {
          include: { user: { select: { firstName: true, lastName: true, email: true } } }
        },
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            fileUrl: true
          }
        },
        _count: {
          select: { participants: true }
        }
      }
    })

    return meeting;
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch meeting.")
  }
}

export type SingleMeeting = Awaited<ReturnType<typeof getMeeting>>;