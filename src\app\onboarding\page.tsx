import { getActiveSession } from "@/lib/server/action/sessions";
import OnboardingFlow from "./_components/onboarding-flow";
import { redirect } from "next/navigation";
import { getOnboardingData } from "@/lib/server/action/frontend/frontend.action";

export const dynamic = "force-dynamic";

export default async function OnboardingPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const [session, onboardingData] = await Promise.all([
    getActiveSession(),
    getOnboardingData(),
  ]);
  if (!session || !onboardingData) {
    redirect("/");
  }
  const { type } = await searchParams;
  return (
    <OnboardingFlow
      type={type as "teacher" | "student"}
      sessionId={session.id}
      onboardingData={onboardingData}
    />
  );
}
