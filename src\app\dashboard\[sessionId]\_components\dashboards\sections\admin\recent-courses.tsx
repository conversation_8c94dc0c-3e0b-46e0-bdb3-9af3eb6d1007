"use client";

import { Ava<PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Loader2, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";

type RecentCoursesProps = {
  recentCourses?: {
    id: string;
    title: string;
    description: string;
    instructor: string;
    students: number;
    image: string | null;
  }[];
  sessionId: string;
};

export default function RecentCourses({
  recentCourses,
  sessionId,
}: RecentCoursesProps) {
  const router = useRouter();
  return (
    <section className="mb-6">
      <h2 className="text-lg font-semibold mb-4">Recent courses</h2>
      {!recentCourses ? (
        <div className="flex justify-center items-center py-12 w-full">
          <Loader2 className="animate-spin" />
        </div>
      ) : recentCourses.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No recent courses found.</p>
        </div>
      ) : (
        <>
          <div className="space-y-4">
            {recentCourses.map((course) => (
              <Card
                key={course.id}
                className="overflow-hidden py-0 cursor-pointer hover:shadow-md"
                onClick={() =>
                  router.push(`/dashboard/${sessionId}/courses/${course.id}`)
                }
              >
                <div className="flex">
                  <div className="w-32 h-auto relative">
                    <Image
                      src={course.image || "/images/placeholder.svg"}
                      alt="Course thumbnail"
                      fill
                      className="object-cover size-full"
                    />
                  </div>
                  <div className="flex-1 p-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <Badge
                          variant="secondary"
                          className="bg-green-100 text-green-700 mb-2"
                        >
                          {course.title}
                        </Badge>
                        <p className="text-sm text-gray-600 mb-2">
                          {course.description}
                        </p>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Avatar className="w-5 h-5">
                            <AvatarFallback className="text-xs">
                              {course.instructor
                                .split(" ")
                                .filter(Boolean)
                                .filter(
                                  (_, i, arr) => i === 0 || i === arr.length - 1
                                )
                                .map((word) => word[0])
                                .join("")
                                .toLocaleUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <span>{course.instructor}</span>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500 flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        {course.students} student
                        {course.students === 1 ? "" : "s"}
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          <div className="text-center mt-4">
            <Button variant="link" asChild>
              <Link href={`/dashboard/${sessionId}/courses`}>
                View all courses
              </Link>
            </Button>
          </div>
        </>
      )}
    </section>
  );
}
