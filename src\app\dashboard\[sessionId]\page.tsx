import { auth } from "@/lib/server/auth";
import { redirect } from "next/navigation";
import TeacherDashboard from "./_components/dashboards/teacher";
import AdminDashboard from "./_components/dashboards/admin";

export default async function Dashboard() {
  const session = await auth();

  if (!session || !session.user) {
    redirect("/sign-in");
  }

  if (session.user.role === "ADMIN") {
    return <AdminDashboard />;
  } else if (session.user.role === "TEACHER") {
    return <TeacherDashboard />;
  }
}
