'use server'

import prisma from "@/lib/prisma"

export async function getInitialGradeByStudentId(studentId: string) {
  try {
    const [ assessmentData, activitiesData ] = await Promise.all([
      prisma.assessment.findMany({
        where: { module: { course: { enrollments: { some: { studentId } } } } },
        select: {
          _count: {
            select: {
              attempts: {
                where: {studentId}
              }
            }
          },
          attempts: {
            where: { studentId },
            select: {
              score: true,
            },
            orderBy: { score: 'desc' },
            take: 1
          },
        },
        orderBy: { module: { order: 'asc' }}
      }),
      prisma.activity.findMany({
        where: { course: { enrollments: { some: { studentId } } } },
        select: {
          score: true,
          deadline: true,
          responses: {
            where: { studentId },
            select: {
              score: true,
              isGraded: true
            },
            orderBy: { submittedAt: 'desc' },
            take: 1
          }
        },
        orderBy: { createdAt: 'asc' }
      }),
    ])

    const totalAssessmentScore = assessmentData.reduce((sum, assessment) => sum + (assessment?.attempts?.[0]?.score ?? 0), 0)

    const modulesWithAttempts = assessmentData.filter(
      assessment => assessment.attempts && assessment.attempts.length > 0
    );

    const totalModuleWithAssessment = modulesWithAttempts.length;

    const totalAssessmentPercentage = totalModuleWithAssessment > 0
      ? (totalAssessmentScore / (totalModuleWithAssessment * 100)) * 100
      : 0;

    const now = new Date();

    let totalActivityResponseScore = 0;
    let totalPassingActivityScore = 0;

    activitiesData.forEach((activity) => {
      const response = activity.responses?.[0];
      const deadline = new Date(activity.deadline);

      const isGraded = response?.isGraded ?? false;
      const isDeadlinePassed = deadline <= now;

      if (isGraded) {
        totalActivityResponseScore += response?.score ?? 0;
        totalPassingActivityScore += activity.score;
      } else if (isDeadlinePassed) {
        totalActivityResponseScore += 0; // missed and ungraded = 0
        totalPassingActivityScore += activity.score;
      }
    });

    const totalActivityPercentage =
      totalPassingActivityScore > 0
        ? (totalActivityResponseScore / totalPassingActivityScore) * 100
        : 0;

    const initialGrade =
    ((totalAssessmentPercentage ?? 0) +
      (totalActivityPercentage ?? 0)) /
    2;
    return Math.round(initialGrade)
  } catch (error) {
    console.log(error)
    return 0
  }
}

export async function getStudentReport(studentId: string) {
  try {
    const enrollment = await prisma.enrollment.findUnique({
      where: { studentId },
      select: {
        progress: true,
        completedAt: true,
        course: {
          select: {
            id: true,
            title: true,
          }
        }
      }
    })
    
    if (!enrollment) return null


    const [ assessmentData, activitiesData ] = await Promise.all([
      prisma.assessment.findMany({
        where: { module: { courseId: enrollment.course.id } },
        select: {
          id: true,
          _count: {
            select: {
              attempts: {
                where: {studentId}
              }
            }
          },
          attempts: {
            where: { studentId },
            select: {
              id: true,
              studentId: true,
              score: true,
              completedAt: true
            },
            orderBy: { score: 'desc' },
            take: 1
          },
          module: {
            select: {
              title: true
            }
          }
        },
        orderBy: { module: { order: 'asc' }}
      }),
      prisma.activity.findMany({
        where: { courseId: enrollment.course.id },
        select: {
          id: true,
          question: true,
          score: true,
          deadline: true,
          responses: {
            where: { studentId },
            select: {
              score: true,
              isGraded: true
            },
            orderBy: { submittedAt: 'desc' },
            take: 1
          }
        },
        orderBy: { createdAt: 'asc' }
      }),
    ])    

    const totalAssessmentScore = assessmentData.reduce((sum, assessment) => sum + (assessment?.attempts?.[0]?.score ?? 0), 0)

    const modulesWithAttempts = assessmentData.filter(
      assessment => assessment.attempts && assessment.attempts.length > 0
    );

    const modules = modulesWithAttempts.map(assessment => ({
      name: assessment.module.title,
      attempts: assessment._count?.attempts ?? 0,
      score: assessment.attempts?.[0]?.score ?? 0
    }));

    const totalModuleWithAssessment = modulesWithAttempts.length;

    const totalAssessmentPercentage = totalModuleWithAssessment > 0
      ? (totalAssessmentScore / (totalModuleWithAssessment * 100)) * 100
      : 0;

    const now = new Date();

    let totalActivityResponseScore = 0;
    let totalPassingActivityScore = 0;

    activitiesData.forEach((activity) => {
      const response = activity.responses?.[0];
      const deadline = new Date(activity.deadline);

      const isGraded = response?.isGraded ?? false;
      const isDeadlinePassed = deadline <= now;

      if (isGraded) {
        totalActivityResponseScore += response?.score ?? 0;
        totalPassingActivityScore += activity.score;
      } else if (isDeadlinePassed) {
        totalActivityResponseScore += 0; // missed and ungraded = 0
        totalPassingActivityScore += activity.score;
      }
    });

    const totalActivityPercentage =
      totalPassingActivityScore > 0
        ? (totalActivityResponseScore / totalPassingActivityScore) * 100
        : 0;
    
    return {
      courseName: enrollment.course.title,
      modules,
      totalAssessmentScore,
      totalAssessmentPercentage,
      activityTest: activitiesData.map(activity => ({
        name: activity.question,
        passScore: activity.score,
        deadline: activity.deadline,
        isGraded: activity.responses?.[0]?.isGraded ?? false,
        score: activity.responses?.[0]?.score ?? 0
      })),
      totalActivityScore: totalActivityResponseScore,
      totalActivityPercentage
    }
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch student report.")
  }
}

export type StudentReport = Awaited<ReturnType<typeof getStudentReport>>

export async function getStudentsReport(courseId: string) {
  try {
    const enrollment = await prisma.enrollment.findMany({
      where: { courseId },
      select: {
        progress: true,
        completedAt: true,
        student: {
          select: {
            id: true,
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        course: {
          select: {
            id: true,
            title: true,
          }
        }
      }
    })
    
    if (!enrollment) return null

    const reportData = enrollment.map(async enrollment => {
      const [ assessmentData, activitiesData ] = await Promise.all([
        prisma.assessment.findMany({
          where: { module: { courseId } },
          select: {
            id: true,
            _count: {
              select: {
                attempts: {
                  where: {studentId: enrollment.student.id}
                }
              }
            },
            attempts: {
              where: { studentId: enrollment.student.id },
              select: {
                id: true,
                studentId: true,
                score: true,
                completedAt: true
              },
              orderBy: { score: 'desc' },
              take: 1
            },
            module: {
              select: {
                title: true
              }
            }
          }
        }),
        prisma.activity.findMany({
          where: { courseId },
          select: {
            id: true,
            question: true,
            score: true,
            deadline: true,
            responses: {
              where: { studentId: enrollment.student.id },
              select: {
                score: true,
                isGraded: true
              },
              orderBy: { submittedAt: 'desc' },
              take: 1
            }
          }
        }),
      ])    

      const totalAssessmentScore = assessmentData.reduce((sum, assessment) => sum + (assessment?.attempts?.[0]?.score ?? 0), 0)

      const modulesWithAttempts = assessmentData.filter(
        assessment => assessment.attempts && assessment.attempts.length > 0
      );

      const modules = modulesWithAttempts.map(assessment => ({
        name: assessment.module.title,
        attempts: assessment._count?.attempts ?? 0,
        score: assessment.attempts?.[0]?.score ?? 0
      }));

      const totalModuleWithAssessment = modulesWithAttempts.length;

      const totalAssessmentPercentage = totalModuleWithAssessment > 0
        ? (totalAssessmentScore / (totalModuleWithAssessment * 100)) * 100
        : 0;

      const now = new Date();

      let totalActivityResponseScore = 0;
      let totalPassingActivityScore = 0;

      activitiesData.forEach((activity) => {
        const response = activity.responses?.[0];
        const deadline = new Date(activity.deadline);

        const isGraded = response?.isGraded ?? false;
        const isDeadlinePassed = deadline <= now;

        if (isGraded) {
          totalActivityResponseScore += response?.score ?? 0;
          totalPassingActivityScore += activity.score;
        } else if (isDeadlinePassed) {
          totalActivityResponseScore += 0; // missed and ungraded = 0
          totalPassingActivityScore += activity.score;
        }
      });

      const totalActivityPercentage =
        totalPassingActivityScore > 0
          ? (totalActivityResponseScore / totalPassingActivityScore) * 100
          : 0;

      
      return {
        courseName: enrollment.course.title,
        studentName: enrollment.student.user.firstName + " " + enrollment.student.user.lastName,
        modules,
        totalAssessmentScore,
        totalAssessmentPercentage,
        activityTest: activitiesData.map(activity => ({
          name: activity.question,
          passScore: activity.score,
          deadline: activity.deadline,
          isGraded: activity.responses?.[0]?.isGraded ?? false,
          score: activity.responses?.[0]?.score ?? 0
        })),
        totalActivityScore: totalActivityResponseScore,
        totalActivityPercentage
      }
    })

    return await Promise.all(reportData)
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch student report.")
  }
}

export type StudentsReport = Awaited<ReturnType<typeof getStudentsReport>>