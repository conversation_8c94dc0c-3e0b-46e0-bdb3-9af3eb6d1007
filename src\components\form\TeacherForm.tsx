"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { Button } from "../ui/button";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import {
  teacherSchema,
  TTeacherForm,
} from "@/lib/server/action/teachers/teacher.schema";
import { FormInputField } from "../form-element/input-field";
import Link from "next/link";
import { createTeacher, updateTeacher } from "@/lib/server/action/teachers";
import { getSchoolOptions } from "@/lib/server/action/schools";
import { useEffect, useState } from "react";
import { FormComboboxField } from "../form-element/select-search";

const TeacherForm = ({
  sessionId,
  isAdmin = false,
  teacherId,
  teacherData,
}: {
  sessionId?: string;
  isAdmin?: boolean;
  teacherId?: string;
  teacherData?: Omit<TTeacherForm, "password">;
}) => {
  const [schools, setSchools] = useState<{ label: string; value: string }[]>(
    []
  );

  useEffect(() => {
    async function schoolOptions() {
      const res = await getSchoolOptions(sessionId as string);
      setSchools(res);
    }
    schoolOptions();
  }, [sessionId]);

  const form = useForm<TTeacherForm>({
    resolver: zodResolver(teacherSchema),
    defaultValues: teacherData ?? {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      school: "",
    },
    mode: "onChange",
  });

  const onSubmit = async (values: TTeacherForm) => {
    const res = teacherData
      ? await updateTeacher(teacherId as string, values)
      : await createTeacher(values, sessionId as string, isAdmin);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.message);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid gap-6 md:grid-cols-2">
          <FormInputField
            control={form.control}
            name="firstName"
            label="First Name"
            placeholder="John"
          />
          <FormInputField
            control={form.control}
            name="lastName"
            label="Last Name"
            placeholder="Doe"
          />
          <FormInputField
            control={form.control}
            name="email"
            label="Email"
            type="email"
            placeholder="<EMAIL>"
          />
          <FormInputField
            control={form.control}
            name="phone"
            label="Phone"
            placeholder="(*************"
          />
          <FormComboboxField
            control={form.control}
            name="school"
            label="School"
            placeholder="Select a school"
            options={schools}
          />
        </div>
        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline">
            <Link href={`/dashboard/${sessionId}/teachers`}>Cancel</Link>
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader />{" "}
                {teacherData ? "Updating Teacher" : "Creating Teacher"}
              </>
            ) : (
              <>{teacherData ? "Update Teacher" : "Create Teacher"}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default TeacherForm;
