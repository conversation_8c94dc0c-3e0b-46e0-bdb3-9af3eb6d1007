"use client";

import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { CustomSheet } from "@/components/shared/CustomSheet";
import { CustomDropdown } from "@/components/shared/Dropdown";
import { Button } from "@/components/ui/button";
import {
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { ColumnDef } from "@tanstack/react-table";
import { EllipsisVertical } from "lucide-react";
import { toast } from "sonner";
import {
  deleteProgram,
  ProgramWithCourses,
} from "@/lib/server/action/programs";
import ProgramForm from "@/components/form/ProgramForm";

export const columns: ColumnDef<ProgramWithCourses>[] = [
  {
    accessorKey: "program.name",
    header: "name",
    cell: ({ row }) => row.original.name,
  },
  {
    accessorKey: "program.description",
    header: "description",
    cell: ({ row }) => (
      <div className="w-40 truncate">{row.original.description}</div>
    ),
  },
  {
    accessorKey: "program.courses",
    header: "No. Courses",
    cell: ({ row }) => row.original._count.courses,
  },
  {
    accessorKey: "action",
    cell: ({ row }) => {
      const program = row.original;

      return (
        <CustomDropdown
          trigger={
            <Button size="icon" variant="ghost">
              <EllipsisVertical />
            </Button>
          }
        >
          <DropdownMenuItem asChild>
            <CustomSheet title="Edit Program" trigger="Edit" asChild={false}>
              <ProgramForm
                programData={{
                  name: program.name,
                  description: program.description || "",
                }}
                programId={program.id}
              />
            </CustomSheet>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <CustomAlertDialog
              asChild={false}
              trigger="Delete"
              onConfirm={async () => {
                const res = await deleteProgram(program.id);
                if (res.success) {
                  toast.success("Program deleted");
                } else {
                  toast.error("Failed to delete program");
                }
              }}
            />
          </DropdownMenuItem>
        </CustomDropdown>
      );
    },
  },
];
