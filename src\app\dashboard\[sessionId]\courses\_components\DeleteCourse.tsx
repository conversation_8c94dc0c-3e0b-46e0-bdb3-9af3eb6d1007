"use client";

import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { Button } from "@/components/ui/button";
import { deleteCourse } from "@/lib/server/action/courses";
import { Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export default function DeleteCourse({
  courseId,
  sessionId,
  isAdmin,
}: {
  courseId: string;
  sessionId: string;
  isAdmin: boolean;
}) {
  const router = useRouter();

  return (
    <CustomAlertDialog
      trigger={
        <Button variant="destructive" size="sm">
          <Trash2 className="h-4 w-4 mr-1" />
          Delete
        </Button>
      }
      title="Delete Course"
      description="This action will remove course and question data for this course. Are you sure you want to delete this course? This action cannot be undone."
      onConfirm={async () => {
        const res = await deleteCourse(courseId as string);
        if (res.success) {
          toast.success("Course deleted");
          router.push(
            `/dashboard/${sessionId}/${isAdmin ? "courses" : "my-courses"}`
          );
        } else {
          toast.error("Failed to delete course");
        }
      }}
    />
  );
}
