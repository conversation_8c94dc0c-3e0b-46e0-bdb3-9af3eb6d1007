// hooks/useUnsavedChanges.ts
import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';

interface UseUnsavedChangesOptions {
  hasUnsavedChanges: boolean;
  message?: string;
}

export function useUnsavedChanges({ 
  hasUnsavedChanges, 
  message = 'You have unsaved changes. Are you sure you want to leave?' 
}: UseUnsavedChangesOptions) {
  const router = useRouter();
  const hasUnsavedRef = useRef(hasUnsavedChanges);

  // Update ref when hasUnsavedChanges changes
  useEffect(() => {
    hasUnsavedRef.current = hasUnsavedChanges;
  }, [hasUnsavedChanges]);

  useEffect(() => {
    // Handle browser navigation/refresh
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedRef.current) {
        e.preventDefault();
        e.returnValue = message;
        return message;
      }
    };

    // Handle browser back/forward/refresh
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Handle programmatic navigation (popstate)
    const handlePopState = () => {
      if (hasUnsavedRef.current) {
        const userConfirmed = confirm(message);
        if (!userConfirmed) {
          // Push the current state back to prevent navigation
          window.history.pushState(null, '', window.location.href);
        }
      }
    };

    window.addEventListener('popstate', handlePopState);

    // Cleanup
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('popstate', handlePopState);
    };
  }, [message]);

  // Function to programmatically navigate with confirmation
  const navigateWithConfirmation = (url: string) => {
    if (hasUnsavedChanges) {
      const userConfirmed = confirm(message);
      if (userConfirmed) {
        router.push(url);
      }
    } else {
      router.push(url);
    }
  };

  // Function to reset unsaved changes (call after successful save)
  const resetUnsavedChanges = () => {
    hasUnsavedRef.current = false;
  };

  return {
    navigateWithConfirmation,
    resetUnsavedChanges,
  };
}