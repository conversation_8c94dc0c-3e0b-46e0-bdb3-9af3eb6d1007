import crypto from 'crypto';

interface ZoomMeetingConfig {
  topic: string;
  type: number;
  start_time: string;
  duration: number;
  timezone: string;
  password?: string;
  waiting_room: boolean;
  settings: {
    host_video: boolean;
    participant_video: boolean;
    cn_meeting: boolean;
    in_meeting: boolean;
    join_before_host: boolean;
    mute_upon_entry: boolean;
    watermark: boolean;
    use_pmi: boolean;
    approval_type: number;
    audio: string;
    auto_recording: string;
  };
}

export class ZoomService {
  private clientId: string;
  private clientSecret: string;
  private accountId: string;
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;
  private baseUrl = 'https://api.zoom.us/v2';

  constructor() {
    this.clientId = process.env.ZOOM_CLIENT_ID!;
    this.clientSecret = process.env.ZOOM_CLIENT_SECRET!;
    this.accountId = process.env.ZOOM_ACCOUNT_ID!;
  }

  private async fetchAccessToken(): Promise<string | null> {
    const now = Date.now();
    if (this.accessToken && now < this.tokenExpiry - 60000) {
      return this.accessToken;
    }

    const basicAuth = Buffer.from(`${this.clientId}:${this.clientSecret}`).toString('base64');

    const res = await fetch('https://zoom.us/oauth/token', {
      method: 'POST',
      headers: {
        Authorization: `Basic ${basicAuth}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'account_credentials',
        account_id: this.accountId,
      }),
    });

    if (!res.ok) {
      throw new Error(`Failed to get access token: ${res.statusText}`);
    }

    const data = await res.json();
    this.accessToken = data.access_token;
    this.tokenExpiry = now + data.expires_in * 1000;

    return this.accessToken;
  }

  private async getAuthHeaders() {
    const token = await this.fetchAccessToken();
    return {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  async createMeeting(config: ZoomMeetingConfig) {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/users/me/meetings`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(config)
    });

    if (!response.ok) {
      throw new Error(`Zoom API error: ${response.statusText}`);
    }

    return response.json();
  }

  async startMeeting(meetingId: string) {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/meetings/${meetingId}/status`, {
      method: 'PATCH',
      headers: headers,
      body: JSON.stringify({ action: 'start' })
    });

    if (!response.ok) {
      throw new Error(`Zoom API error: ${response.statusText}`);
    }

    return response.json();
  }

  async updateMeeting(meetingId: string, config: Partial<ZoomMeetingConfig>) {
    const headers = await this.getAuthHeaders();
    
    const response = await fetch(`${this.baseUrl}/meetings/${meetingId}`, {
      method: 'PATCH',
      headers: headers,
      body: JSON.stringify(config)
    });

    if (!response.ok) {
      throw new Error(`Zoom API error: ${response.statusText}`);
    }

    return response.json();
  }

  async deleteMeeting(meetingId: string) {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/meetings/${meetingId}`, {
      method: 'DELETE',
      headers,
    });

    if (!response.ok) {
      throw new Error(`Zoom API error: ${response.statusText}`);
    }
  }

  async getMeeting(meetingId: string) {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}/meetings/${meetingId}`, {
      headers,
    });

    if (!response.ok) {
      throw new Error(`Zoom API error: ${response.statusText}`);
    }

    return response.json();
  }

  verifyWebhook(rawBody: string, signature: string, timestamp: string): boolean {
    const secret = process.env.ZOOM_WEBHOOK_SECRET!;
    const message = `v0:${timestamp}:${rawBody}`;

    const computedHash = crypto
      .createHmac('sha256', secret)
      .update(message)
      .digest('hex');

    const expectedSignature = `v0=${computedHash}`;

    return expectedSignature === signature;
  }
}

export const zoomService = new ZoomService();