"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Award, Calendar, Eye, FileText, User } from "lucide-react";

interface CertificateData {
  id: string;
  fullName: string;
  courseName: string;
  fileSize: string;
  issueDate: string;
  issuingOrganization: string;
  certificateId: string;
  status: "completed" | "pending" | "expired";
  fileUrl: string;
}

export default function CertificateCard({
  certificate,
}: {
  certificate: CertificateData;
}) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "expired":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const handleDownload = () => {
    // Open the certificate PDF in a new tab
    console.log("Opening certificate:", certificate.certificateId);

    if (certificate?.fileUrl) {
      window.open(certificate.fileUrl, "_blank"); // Opens in a new tab or window
    } else {
      console.error("No file URL found for the certificate.");
    }
  };

  return (
    <div className="w-full max-w-xl mx-auto">
      <Card className="overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Award className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-lg leading-tight">
                  Certificate
                </h3>
                <p className="text-sm text-muted-foreground">
                  #{certificate.certificateId}
                </p>
              </div>
            </div>
            <Badge className={getStatusColor(certificate.status)}>
              {certificate.status.charAt(0).toUpperCase() +
                certificate.status.slice(1)}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Certificate Preview Thumbnail */}
          <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-4 border-2 border-dashed border-blue-200">
            <div className="text-center">
              <FileText className="h-12 w-12 text-blue-500 mx-auto mb-2" />
              <p className="text-sm font-medium text-blue-700">
                Certificate Preview
              </p>
              <p className="text-xs text-blue-600">{certificate.fileSize}</p>
            </div>
          </div>

          {/* Certificate Details */}
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <User className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900">
                  {certificate.fullName}
                </p>
                <p className="text-xs text-gray-500">Certificate Recipient</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <Award className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900">
                  {certificate.courseName}
                </p>
                <p className="text-xs text-gray-500">Course Completed</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <Calendar className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900">
                  {certificate.issueDate}
                </p>
                <p className="text-xs text-gray-500">Issue Date</p>
              </div>
            </div>

            <Separator />

            <div className="text-center">
              <p className="text-sm font-medium text-gray-900">
                {certificate.issuingOrganization}
              </p>
              <p className="text-xs text-gray-500">Issuing Organization</p>
            </div>
          </div>
        </CardContent>

        <CardFooter className="flex justify-center pt-4">
          <Button onClick={handleDownload} className="w-32">
            <Eye className="h-4 w-4 mr-1" />
            View
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
