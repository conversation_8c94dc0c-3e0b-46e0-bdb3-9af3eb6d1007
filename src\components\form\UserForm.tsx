// "use client";

// import { Input } from "@/components/ui/input";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { useForm } from "react-hook-form";
// import {
//   Form,
//   FormControl,
//   FormField,
//   FormItem,
//   FormLabel,
//   FormMessage,
// } from "@/components/ui/form";
// import { Button } from "../ui/button";
// import { Separator } from "../ui/separator";
// import { Loader } from "lucide-react";
// import { createUser, updateUser } from "@/lib/server/user/user.action";
// import { toast } from "sonner";
// import { TUserForm, userSchema } from "@/lib/server/user/user.schema";
// import { useRouter } from "next/navigation";
// import { socket } from "@/socket";
// import { NotificationSocket } from "@/lib/server/notification/notification.schema";

// const UserForm = ({
//   userData,
//   userId,
//   onboarding = false,
// }: {
//   userData?: TUserForm;
//   userId?: string;
//   onboarding?: boolean;
// }) => {
//   const router = useRouter();

//   const defaultValues = {
//     firstName: "",
//     middleName: "",
//     lastName: "",
//     email: "",
//     specialization: "",
//     school: "",
//     // userPhoto: "",
//   };

//   const form = useForm<TUserForm>({
//     resolver: zodResolver(userSchema),
//     defaultValues: userData ?? defaultValues,
//     mode: "onChange",
//   });

//   const onSubmit = async (values: TUserForm) => {
//     let res;

//     if (userData) {
//       res = await updateUser(userId!, values);
//     } else {
//       res = await createUser(onboarding, values);
//       if (res.success && onboarding) {
//         socket.emit("user_registered", {
//           title: "Registration Alert",
//           message: `${values.firstName} ${values.lastName} just registered!`,
//           identifier: `${values.firstName}-${
//             values.lastName
//           }-${new Date().toISOString()}`,
//         } satisfies NotificationSocket);
//         router.push("/onboarding/completed");
//       }
//     }

//     toast(res.message);
//   };

//   return (
//     <Form {...form}>
//       <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
//         <h2 className="text-md">User Form</h2>
//         <p className="text-xs max-w-2xl text-gray-400">
//           Provide all the information required to create your account
//         </p>
//         <Separator className="mt-4 h-0.5" />

//         <div className="grid lg:grid-cols-2 gap-4 mt-5">
//           <FormField
//             control={form.control}
//             name="firstName"
//             render={({ field }) => (
//               <FormItem>
//                 <FormLabel>First Name</FormLabel>
//                 <FormControl>
//                   <Input {...field} />
//                 </FormControl>
//                 <FormMessage />
//               </FormItem>
//             )}
//           />
//           <FormField
//             control={form.control}
//             name="middleName"
//             render={({ field }) => (
//               <FormItem>
//                 <FormLabel>Middle Name</FormLabel>
//                 <FormControl>
//                   <Input {...field} />
//                 </FormControl>
//                 <FormMessage />
//               </FormItem>
//             )}
//           />
//           <FormField
//             control={form.control}
//             name="lastName"
//             render={({ field }) => (
//               <FormItem>
//                 <FormLabel>Last Name</FormLabel>
//                 <FormControl>
//                   <Input {...field} />
//                 </FormControl>
//                 <FormMessage />
//               </FormItem>
//             )}
//           />
//           <FormField
//             control={form.control}
//             name="email"
//             render={({ field }) => (
//               <FormItem>
//                 <FormLabel>Email</FormLabel>
//                 <FormControl>
//                   <Input
//                     {...field}
//                     type="email"
//                     disabled={userData ? true : false}
//                   />
//                 </FormControl>
//                 <FormMessage />
//               </FormItem>
//             )}
//           />
//           <FormField
//             control={form.control}
//             name="specialization"
//             render={({ field }) => (
//               <FormItem>
//                 <FormLabel>Position/Specialization</FormLabel>
//                 <FormControl>
//                   <Input {...field} />
//                 </FormControl>
//                 <FormMessage />
//               </FormItem>
//             )}
//           />
//           <FormField
//             control={form.control}
//             name="school"
//             render={({ field }) => (
//               <FormItem>
//                 <FormLabel>School</FormLabel>
//                 <FormControl>
//                   <Input {...field} />
//                 </FormControl>
//                 <FormMessage />
//               </FormItem>
//             )}
//           />
//           {/* <FormField
//             control={form.control}
//             name="userPhoto"
//             render={({ field }) => (
//               <FormItem>
//                 <FormLabel>Upload Photo</FormLabel>
//                 <FormControl>
//                   <Input
//                     {...field}
//                     type="file"
//                     onChange={(e) => field.onChange(e.target.files)}
//                     value={field.value?.fileName}
//                   />
//                 </FormControl>
//                 <FormMessage />
//               </FormItem>
//             )}
//           /> */}
//         </div>
//         <div className="flex justify-center items-center mt-6">
//           <Button
//             type="submit"
//             disabled={form.formState.isSubmitting}
//             size="sm"
//             className="button w-full sm:w-fit sm:px-32 disabled:bg-primary/70"
//           >
//             {form.formState.isSubmitting ? (
//               <>
//                 <Loader /> {userData ? "Updating User" : "Creating User"}
//               </>
//             ) : (
//               <>{userData ? "Update User" : "Create User"}</>
//             )}
//           </Button>
//         </div>
//       </form>
//     </Form>
//   );
// };

// export default UserForm;
