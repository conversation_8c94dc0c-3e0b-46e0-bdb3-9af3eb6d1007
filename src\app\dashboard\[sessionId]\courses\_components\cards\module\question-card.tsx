"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Edit, Trash2 } from "lucide-react";
import { Question } from "@/lib/server/action/courses/modules/assessments/questions";

export default function QuestionCard({
  index,
  question,
  handleEditQuestion,
  handleDeleteQuestion,
}: {
  index: number;
  question: Question;
  handleEditQuestion: () => void;
  handleDeleteQuestion: () => void;
}) {
  return (
    <Card key={question.id} className="border-l-4 border-l-blue-500">
      <CardContent className="pt-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center gap-2">
            <Badge variant="outline">Q{index + 1}</Badge>
            <Badge
              variant={
                question.type === "multiple-choice" ? "default" : "secondary"
              }
            >
              {question.type === "multiple-choice"
                ? "Multiple Choice"
                : "True/False"}
            </Badge>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleEditQuestion}>
              <Edit className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleDeleteQuestion}>
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <p className="font-medium mb-3">{question.question}</p>

        {question.type === "multiple-choice" && question.options && (
          <div className="space-y-2">
            {question.options.map((option, optionIndex) => (
              <div
                key={optionIndex}
                className={`p-2 rounded border ${
                  ["A", "B", "C", "D"][optionIndex] === question.correctAnswer
                    ? "bg-green-50 border-green-200"
                    : "bg-gray-50"
                }`}
              >
                <span className="text-sm font-medium mr-2">
                  {String.fromCharCode(65 + optionIndex)}.
                </span>
                {option}
                {["A", "B", "C", "D"][optionIndex] ===
                  question.correctAnswer && (
                  <Badge className="ml-2" variant="default">
                    Correct
                  </Badge>
                )}
              </div>
            ))}
          </div>
        )}

        {question.type === "true-false" && (
          <div className="flex gap-4">
            <div
              className={`p-2 rounded border ${
                question.correctAnswer === "true"
                  ? "bg-green-50 border-green-200"
                  : "bg-gray-50"
              }`}
            >
              True{" "}
              {question.correctAnswer === "true" && (
                <Badge className="ml-2">Correct</Badge>
              )}
            </div>
            <div
              className={`p-2 rounded border ${
                question.correctAnswer === "false"
                  ? "bg-green-50 border-green-200"
                  : "bg-gray-50"
              }`}
            >
              False{" "}
              {question.correctAnswer === "false" && (
                <Badge className="ml-2">Correct</Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
