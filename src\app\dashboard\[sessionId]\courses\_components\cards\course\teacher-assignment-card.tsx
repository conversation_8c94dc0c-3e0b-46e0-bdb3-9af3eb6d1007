"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Users } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { getTeacherOptions } from "@/lib/server/action/teachers";
import {
  assignCourseAssistantTeacher,
  assignCoursePrimaryTeacher,
  CourseAssignedTeachers,
  removeCourseTeacher,
} from "@/lib/server/action/courses";
import { toast } from "sonner";
// import { Badge } from "@/components/ui/badge";

export default function TeacherAssignmentCard({
  sessionId,
  courseId,
  courseTeachers,
  isAdmin,
}: {
  sessionId: string;
  courseId: string;
  courseTeachers: CourseAssignedTeachers[];
  isAdmin: boolean;
}) {
  const primaryTeacher = courseTeachers.find((ta) => ta.role === "PRIMARY");
  const assistantTeachers = courseTeachers.filter(
    (ta) => ta.role === "ASSISTANT"
  );

  const [selectedPrimary, setSelectedPrimary] = useState<string>(
    primaryTeacher?.userId || ""
  );
  const [selectedAssistant, setSelectedAssistant] = useState<string>("");
  const [availableTeachers, setAvailableTeachers] = useState<
    { id: string; name: string; email: string }[]
  >([]);

  useEffect(() => {
    async function fetchTeachers() {
      const res = await getTeacherOptions(sessionId);
      setAvailableTeachers(res);
    }
    fetchTeachers();
  }, [sessionId]);

  const assignedTeacherIds = courseTeachers.map((t) => t.userId);
  const availableForPrimary = availableTeachers.filter(
    (t) => !assignedTeacherIds.includes(t.id)
  );
  const availableForAssistant = availableTeachers.filter(
    (t) => !assignedTeacherIds.includes(t.id)
  );

  const handleAssignPrimary = async () => {
    if (!selectedPrimary) return;

    const res = await assignCoursePrimaryTeacher(courseId, selectedPrimary);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  const handleAssignAssistant = async () => {
    if (!selectedAssistant) return;

    const res = await assignCourseAssistantTeacher(courseId, selectedAssistant);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
    setSelectedAssistant("");
  };

  const handleRemoveTeacher = async (teacherId: string) => {
    const res = await removeCourseTeacher(teacherId);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Assign Teachers to Course
        </CardTitle>
        <CardDescription>
          Assign a primary teacher and teaching assistants to this course
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Assignment Form */}
        <div className="grid gap-4">
          {/* Primary Teacher Assignment */}
          {isAdmin ? (
            <div className="space-y-2">
              <Label htmlFor="primary-teacher">Primary Teacher</Label>
              <div className="flex gap-2">
                <Select
                  value={selectedPrimary}
                  onValueChange={setSelectedPrimary}
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="Select primary teacher" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableForPrimary.map((teacher) => (
                      <SelectItem key={teacher.id} value={teacher.id}>
                        <div className="flex flex-col">
                          <span>{teacher.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {teacher.email}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  onClick={handleAssignPrimary}
                  disabled={!selectedPrimary}
                  size="sm"
                >
                  Assign
                </Button>
              </div>
            </div>
          ) : null}

          {/* Assistant Teacher Assignment */}
          <div className="space-y-2">
            <Label htmlFor="assistant-teacher">Teaching Assistant</Label>
            <div className="flex gap-2">
              <Select
                value={selectedAssistant}
                onValueChange={setSelectedAssistant}
              >
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Select teaching assistant" />
                </SelectTrigger>
                <SelectContent>
                  {availableForAssistant.map((teacher) => (
                    <SelectItem key={teacher.id} value={teacher.id}>
                      <div className="flex flex-col">
                        <span>{teacher.name}</span>
                        <span className="text-xs text-muted-foreground">
                          {teacher.email}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                onClick={handleAssignAssistant}
                disabled={!selectedAssistant}
                size="sm"
              >
                Add
              </Button>
            </div>
          </div>
        </div>

        {/* Assigned Teachers List */}
        <div className="space-y-3">
          <Label className="text-base font-medium">Assigned Teachers</Label>
          {courseTeachers.length === 0 ? (
            <p className="text-sm text-muted-foreground">
              No teachers assigned yet
            </p>
          ) : (
            <div className="space-y-2">
              {courseTeachers.map((teacher) => (
                <div
                  key={teacher.id}
                  className="flex items-center justify-between p-3 border rounded-lg bg-muted/50"
                >
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      {teacher.role === "PRIMARY" ? (
                        <UserCheck className="h-4 w-4 text-blue-600" />
                      ) : (
                        <Users className="h-4 w-4 text-green-600" />
                      )}
                      <div>
                        <p className="font-medium">
                          {teacher.user.firstName + " " + teacher.user.lastName}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {teacher.user.email}
                        </p>
                      </div>
                    </div>
                    {/* <Badge
                      variant={
                        teacher.role === "primary" ? "default" : "secondary"
                      }
                      className="ml-2"
                    >
                      {teacher.role === "primary"
                        ? "Primary Teacher"
                        : "Teaching Assistant"}
                    </Badge> */}
                  </div>
                  {teacher.role !== "PRIMARY" ? (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveTeacher(teacher.id)}
                      className="h-8 w-8 p-0 hover:bg-destructive hover:text-destructive-foreground"
                    >
                      <X className="h-4 w-4" />
                      <span className="sr-only">
                        Remove{" "}
                        {teacher.user.firstName + " " + teacher.user.lastName}
                      </span>
                    </Button>
                  ) : null}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Summary */}
        <div className="pt-4 border-t">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">
              Total: {courseTeachers.length} teacher
              {courseTeachers.length !== 1 ? "s" : ""} assigned
            </span>
            <div className="flex gap-4">
              <span className="flex items-center gap-1">
                <UserCheck className="h-3 w-3 text-blue-600" />
                Primary: {primaryTeacher ? "1" : "0"}
              </span>
              <span className="flex items-center gap-1">
                <Users className="h-3 w-3 text-green-600" />
                Assistants: {assistantTeachers.length}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
