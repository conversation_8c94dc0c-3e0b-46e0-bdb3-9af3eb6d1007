import { ChangeEvent, useRef, useState } from "react";

export const useFileInput = (maxSize: number) => {
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [duration, setDuration] = useState<number | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.[0]) {
      const selectedFile = e.target.files[0];

      if (selectedFile.size > maxSize) {
        // You might want to add some user feedback here, e.g., a toast notification
        console.warn(`File size exceeds maximum limit of ${maxSize} bytes.`);
        return;
      }

      // Clear previous preview URL if it exists
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }

      setFile(selectedFile);
      setDuration(null); // Reset duration for new file

      // Determine if a preview URL should be created
      if (
        selectedFile.type.startsWith("image/") ||
        selectedFile.type.startsWith("video/")
      ) {
        const objectUrl = URL.createObjectURL(selectedFile);
        setPreviewUrl(objectUrl);

        // Extract duration for video files
        if (selectedFile.type.startsWith("video/")) {
          const video = document.createElement("video");
          video.preload = "metadata";
          video.onloadedmetadata = () => {
            if (isFinite(video.duration) && video.duration > 0) {
              setDuration(Math.round(video.duration));
            } else {
              setDuration(null);
            }
            // Revoke URL after duration is extracted
            URL.revokeObjectURL(video.src);
          };
          video.src = objectUrl;
        }
      } else {
        // For documents, we don't generate a visual preview URL for direct display
        setPreviewUrl(null);
      }
    }
  };

  const resetFile = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setFile(null);
    setPreviewUrl(null);
    setDuration(null);
    if (inputRef.current) {
      inputRef.current.value = ""; // Clear the input element's value
    }
  };

  return { file, previewUrl, duration, inputRef, handleFileChange, resetFile };
};
