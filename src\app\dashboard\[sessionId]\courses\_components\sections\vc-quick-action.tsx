"use client";

import { CustomSheet } from "@/components/shared/CustomSheet";
import { Card, CardContent } from "@/components/ui/card";
import { Plus, Users, Video } from "lucide-react";
import React, { useState } from "react";
import CreateMeetingForm from "../forms/create-meeting-form";
import JoinMeetingModal from "../../../../../../components/shared/JoinMeetingModal";

export default function VCQuickAction({
  courseId,
  userEmail,
}: {
  courseId: string;
  userEmail: string;
}) {
  const [showJoinModal, setShowJoinModal] = useState(false);

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
        <CustomSheet
          title="Create Meeting"
          trigger={
            <Card className="hover:shadow-md transition-shadow cursor-pointer py-0">
              <CardContent className="p-6 text-center">
                <Video className="w-8 h-8 mx-auto mb-3 text-primary" />
                <h3 className="font-semibold mb-2">Start Instant Meeting</h3>
                <p className="text-sm text-gray-600">
                  Begin a meeting right now
                </p>
              </CardContent>
            </Card>
          }
        >
          <CreateMeetingForm
            courseId={courseId}
            userEmail={userEmail}
            instant
          />
        </CustomSheet>

        <CustomSheet
          title="Create Meeting"
          trigger={
            <Card className="hover:shadow-md transition-shadow cursor-pointer py-0">
              <CardContent className="p-6 text-center">
                <Plus className="w-8 h-8 mx-auto mb-3 text-green-600" />
                <h3 className="font-semibold mb-2">Schedule Meeting</h3>
                <p className="text-sm text-gray-600">
                  Plan a meeting for later
                </p>
              </CardContent>
            </Card>
          }
        >
          <CreateMeetingForm courseId={courseId} userEmail={userEmail} />
        </CustomSheet>

        <Card
          className="hover:shadow-md transition-shadow cursor-pointer py-0"
          onClick={() => setShowJoinModal(true)}
        >
          <CardContent className="p-6 text-center">
            <Users className="w-8 h-8 mx-auto mb-3 text-purple-600" />
            <h3 className="font-semibold mb-2">Join Meeting</h3>
            <p className="text-sm text-gray-600">Enter a meeting ID</p>
          </CardContent>
        </Card>
      </div>

      {showJoinModal && (
        <JoinMeetingModal onClose={() => setShowJoinModal(false)} />
      )}
    </>
  );
}
