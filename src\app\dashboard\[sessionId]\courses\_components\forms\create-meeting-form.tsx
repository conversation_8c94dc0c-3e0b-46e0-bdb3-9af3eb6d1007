"use client";

import React, { useState } from "react";
import { Calendar, Settings } from "lucide-react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  createMeeting,
  updateMeeting,
} from "@/lib/server/action/courses/virtual-classrooms";
import { Button } from "@/components/ui/button";
import {
  meetingSchema,
  MeetingFormData,
} from "@/lib/server/action/courses/virtual-classrooms/virtual-classrooms.schema";

interface CreateMeetingModalProps {
  meetingId?: string;
  meeting?: MeetingFormData;
  courseId: string;
  instant?: boolean;
  userEmail: string;
}

export default function CreateMeetingModal({
  meetingId,
  meeting,
  courseId,
  instant = false,
  userEmail,
}: CreateMeetingModalProps) {
  const [isInstant, setIsInstant] = useState(instant);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<MeetingFormData>({
    resolver: zod<PERSON><PERSON><PERSON>ver(meetingSchema),
    defaultValues: meeting || {
      title: "",
      description: "",
      scheduledAt: "",
      duration: 60,
      maxParticipants: 100,
      waitingRoomEnabled: true,
      recordingEnabled: false,
      chatEnabled: true,
      zoomEmail: userEmail,
    },
  });

  const onSubmit = async (data: MeetingFormData) => {
    const meetingData = {
      ...data,
      scheduledAt: isInstant
        ? new Date().toISOString()
        : new Date(data.scheduledAt!).toISOString(),
    };

    const res = meetingId
      ? await updateMeeting(meetingId, meetingData)
      : await createMeeting(courseId, meetingData);

    if (res.success) {
      toast.success(res.message || "Meeting created");
    } else {
      toast.error(res.error || "Failed to create meeting");
    }
  };

  // Get minimum datetime for scheduling (current time)
  const minDateTime = new Date().toISOString().slice(0, 16);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Meeting Type Toggle */}
      <div className="flex gap-4">
        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="radio"
            checked={isInstant}
            onChange={() => setIsInstant(true)}
            className="text-primary"
          />
          <span className="text-sm">Start Instant Meeting</span>
        </label>
        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="radio"
            checked={!isInstant}
            onChange={() => setIsInstant(false)}
            className="text-primary"
          />
          <span className="text-sm">Schedule for Later</span>
        </label>
      </div>

      {/* Basic Info */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Meeting Title *
          </label>
          <input
            type="text"
            {...register("title")}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent ${
              errors.title ? "border-red-500" : "border-gray-300"
            }`}
            placeholder="Enter meeting title"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            {...register("description")}
            rows={3}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent ${
              errors.description ? "border-red-500" : "border-gray-300"
            }`}
            placeholder="Add meeting description (optional)"
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-600">
              {errors.description.message}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Duration (minutes)
          </label>
          <input
            type="number"
            {...register("duration", { valueAsNumber: true })}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent ${
              errors.duration ? "border-red-500" : "border-gray-300"
            }`}
          />
          {errors.duration && (
            <p className="mt-1 text-sm text-red-600">
              {errors.duration.message}
            </p>
          )}
        </div>
      </div>

      {/* Schedule */}
      {!isInstant && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Calendar className="inline w-4 h-4 mr-1" />
            Schedule Date & Time *
          </label>
          <input
            type="datetime-local"
            {...register("scheduledAt", {
              required: !isInstant
                ? "Schedule date and time is required"
                : false,
            })}
            min={minDateTime}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent ${
              errors.scheduledAt ? "border-red-500" : "border-gray-300"
            }`}
          />
          {errors.scheduledAt && (
            <p className="mt-1 text-sm text-red-600">
              {errors.scheduledAt.message}
            </p>
          )}
        </div>
      )}

      {/* Settings */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
          <Settings className="w-5 h-5" />
          Meeting Settings
        </h3>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Maximum Participants
            </label>
            <input
              type="number"
              {...register("maxParticipants", { valueAsNumber: true })}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent ${
                errors.maxParticipants ? "border-red-500" : "border-gray-300"
              }`}
            />
            {errors.maxParticipants && (
              <p className="mt-1 text-sm text-red-600">
                {errors.maxParticipants.message}
              </p>
            )}
          </div>

          <div className="space-y-3">
            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                {...register("waitingRoomEnabled")}
                className="rounded text-primary focus:ring-primary"
              />
              <div>
                <span className="text-sm font-medium text-gray-700">
                  Enable Waiting Room
                </span>
                <p className="text-xs text-gray-500">
                  Participants wait for host approval
                </p>
              </div>
            </label>

            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                {...register("recordingEnabled")}
                className="rounded text-primary focus:ring-primary"
              />
              <div>
                <span className="text-sm font-medium text-gray-700">
                  Auto Recording
                </span>
                <p className="text-xs text-gray-500">
                  Automatically record the meeting
                </p>
              </div>
            </label>

            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                {...register("chatEnabled")}
                className="rounded text-primary focus:ring-primary"
              />
              <div>
                <span className="text-sm font-medium text-gray-700">
                  Enable Chat
                </span>
                <p className="text-xs text-gray-500">
                  Allow participants to chat during meeting
                </p>
              </div>
            </label>
          </div>
        </div>
      </div>

      {/* Hidden field for zoomEmail */}
      <input type="hidden" {...register("zoomEmail")} />

      {/* Actions */}
      <div className="flex gap-3 pt-4 border-t border-gray-200">
        <Button type="submit" disabled={isSubmitting} className="w-full">
          {isInstant
            ? isSubmitting
              ? "Starting..."
              : "Start Meeting"
            : isSubmitting
            ? "Scheduling..."
            : "Schedule Meeting"}
        </Button>
      </div>
    </form>
  );
}
