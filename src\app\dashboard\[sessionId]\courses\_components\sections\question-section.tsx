"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Plus, <PERSON>Circle, AlertTriangle, X } from "lucide-react";
import { useState } from "react";
import { QuestionModal } from "../question-modal";
import QuestionCard from "../cards/module/question-card";
import {
  createQuestion,
  deleteQuestion,
  Question,
  updateQuestion,
} from "@/lib/server/action/courses/modules/assessments/questions";
import { toast } from "sonner";

export default function QuestionSection({
  assessmentId,
  questions,
  maxQuestionCount,
}: {
  questions: Question[];
  assessmentId?: string;
  maxQuestionCount?: number;
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  const [dismissedAlerts, setDismissedAlerts] = useState<Set<string>>(
    new Set()
  );

  const handleAddQuestion = async (question: Question) => {
    setIsModalOpen(false);
    const action = editingQuestion
      ? () => updateQuestion(editingQuestion!.id, question)
      : () => createQuestion(assessmentId as string, question);

    const res = await action();

    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }

    setEditingQuestion(null);
  };

  const handleEditQuestion = (question: Question) => {
    setEditingQuestion(question);
    setIsModalOpen(true);
  };

  const handleDeleteQuestion = async (questionId: string) => {
    const res = await deleteQuestion(questionId);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  const dismissAlert = (alertType: string) => {
    setDismissedAlerts((prev) => new Set(prev).add(alertType));
  };

  const showEmptyQuestionsAlert =
    assessmentId && questions.length === 0 && !dismissedAlerts.has("empty");
  const showInsufficientQuestionsAlert =
    assessmentId &&
    maxQuestionCount &&
    questions.length > 0 &&
    questions.length < maxQuestionCount &&
    !dismissedAlerts.has("insufficient");

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Questions</CardTitle>
              <CardDescription>
                Add and manage your assessment questions
              </CardDescription>
            </div>
            {assessmentId ? (
              <Button onClick={() => setIsModalOpen(true)} size="sm">
                <Plus className="h-4 w-4" />
                Add Question
              </Button>
            ) : null}
          </div>
        </CardHeader>
        <CardContent>
          {/* Warning Alerts */}
          {showEmptyQuestionsAlert && (
            <Alert className="mb-4 border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <div className="flex justify-between items-start">
                <AlertDescription className="text-red-800">
                  <strong>Assessment Incomplete:</strong> Questions cannot be
                  left empty after creating assessment settings. Please add
                  questions to complete your assessment or delete the assessment
                  settings if no longer needed.
                </AlertDescription>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-red-600 hover:text-red-800"
                  onClick={() => dismissAlert("empty")}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </Alert>
          )}

          {showInsufficientQuestionsAlert && (
            <Alert className="mb-4 border-amber-200 bg-amber-50">
              <AlertTriangle className="h-4 w-4 text-amber-600" />
              <div className="flex justify-between items-start">
                <AlertDescription className="text-amber-800">
                  <strong>Insufficient Questions:</strong> Your assessment
                  currently has {questions.length} question
                  {questions.length !== 1 ? "s" : ""} but requires a minimum of{" "}
                  {maxQuestionCount} questions for optimal evaluation. Consider
                  adding at least {maxQuestionCount - questions.length} more
                  question
                  {maxQuestionCount - questions.length !== 1 ? "s" : ""} to meet
                  the recommended count.
                </AlertDescription>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-amber-600 hover:text-amber-800"
                  onClick={() => dismissAlert("insufficient")}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </Alert>
          )}

          {!assessmentId ? (
            <div className="text-center py-12 text-gray-500">
              <XCircle className="size-12 mx-auto mb-3 text-red-400" />
              <h2 className="text-lg font-semibold">
                No assessment settings found.
              </h2>
              <p className="text-sm">
                Create assessment settings to get started.
              </p>
            </div>
          ) : (
            <>
              {questions.length === 0 ? (
                <div className="text-center py-12 text-gray-500">
                  <p>No questions added yet.</p>
                  <p className="text-sm">
                    Click &quot;Add Question&quot; to get started.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {questions.map((question, index) => (
                    <QuestionCard
                      key={question.id}
                      index={index}
                      question={question}
                      handleEditQuestion={() => handleEditQuestion(question)}
                      handleDeleteQuestion={() =>
                        handleDeleteQuestion(question.id)
                      }
                    />
                  ))}
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      <QuestionModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingQuestion(null);
        }}
        onSave={handleAddQuestion}
        editingQuestion={editingQuestion}
      />
    </>
  );
}
