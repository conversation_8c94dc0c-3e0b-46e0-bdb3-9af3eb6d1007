"use client";

import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form, FormLabel } from "@/components/ui/form";
import { Button } from "../ui/button";
import { Loader, PlusCircle, Trash2 } from "lucide-react";
import { redirect } from "next/navigation";
import {
  generalSettingsFormSchema,
  SocialMediaEntry,
  TGeneralSettingsForm,
} from "@/lib/server/action/settings/settings.schema";
import { updateGeneralSettings } from "@/lib/server/action/settings";
import { useState } from "react";
import { toast } from "sonner";
import { FormInputField } from "../form-element/input-field";

const SettingsFrom = ({
  generalSettingsData,
  id,
  socials,
}: {
  generalSettingsData: TGeneralSettingsForm;
  id: string;
  socials?: SocialMediaEntry[];
}) => {
  const form = useForm<TGeneralSettingsForm>({
    resolver: zodResolver(generalSettingsFormSchema),
    defaultValues: generalSettingsData ?? {
      siteTitle: "CEAP-NCR",
      siteName: "CEAP-NCR Virtue Series",
      siteAddress: "https://ceapncrvirtueseries.com/",
      phone1: "",
      phone2: "",
      landline1: "",
      landline2: "",
      email: "",
      address: "",
    },
  });

  const [socialMedia, setSocialMedia] = useState<SocialMediaEntry[]>(
    socials ?? [
      { id: "1", platform: "X", link: "" },
      { id: "2", platform: "LinkedIn", link: "" },
      { id: "3", platform: "Facebook", link: "" },
    ]
  );

  const handleSocialMediaChange = (
    id: string,
    field: "platform" | "link",
    value: string
  ) => {
    setSocialMedia((prev) =>
      prev.map((item) => (item.id === id ? { ...item, [field]: value } : item))
    );
  };

  const addSocialMedia = () => {
    const newId = Date.now().toString();
    setSocialMedia((prev) => [...prev, { id: newId, platform: "", link: "" }]);
  };

  const removeSocialMedia = (id: string) => {
    setSocialMedia((prev) => prev.filter((item) => item.id !== id));
  };

  const onSubmit = async (values: TGeneralSettingsForm) => {
    // Create the final data structure with social media as JSON
    const finalData = {
      ...values,
      socials: JSON.stringify(socialMedia),
    };

    const res = await updateGeneralSettings(id as string, finalData);
    toast(res.message);
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-4 flex-1 border p-6 rounded-lg"
      >
        <div className="space-y-4 grid lg:grid-cols-2 gap-4 w-full">
          <FormInputField
            control={form.control}
            name="siteTitle"
            label="Site title"
            placeholder="Enter site title"
          />
          <FormInputField
            control={form.control}
            name="siteName"
            label="Site Name"
            placeholder="Enter site name"
          />
          <FormInputField
            control={form.control}
            name="siteAddress"
            label="Site address"
            placeholder="Enter site address"
          />
          <FormInputField
            control={form.control}
            name="email"
            label="Contact email"
            type="email"
            placeholder="<EMAIL>"
          />
          <FormInputField
            control={form.control}
            name="phone1"
            label="Phone 1"
            placeholder="0966 123 4567"
          />
          <FormInputField
            control={form.control}
            name="phone2"
            label="Phone 2"
            placeholder="0966 940 0123"
          />
          <FormInputField
            control={form.control}
            name="landline1"
            label="Landline 1"
            placeholder="(02) 8123-4567"
          />
          <FormInputField
            control={form.control}
            name="landline2"
            label="Landline 2"
            placeholder="(02) 8123-7890"
          />
          <FormInputField
            control={form.control}
            name="address"
            label="Address"
            placeholder="Manila, Philippines"
          />
        </div>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <FormLabel className="text-sm font-medium">Social Media</FormLabel>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addSocialMedia}
              className="flex items-center gap-1 text-xs h-7 px-3"
            >
              <PlusCircle size={14} />
              Add
            </Button>
          </div>

          <div className="space-y-3">
            {socialMedia.map((item) => (
              <div key={item.id} className="flex gap-3">
                <div className="flex-1">
                  <Input
                    placeholder="Platform"
                    value={item.platform}
                    onChange={(e) =>
                      handleSocialMediaChange(
                        item.id,
                        "platform",
                        e.target.value
                      )
                    }
                    className="h-10 bg-gray-50"
                  />
                </div>
                <div className="flex-[2]">
                  <Input
                    placeholder="Link"
                    value={item.link}
                    onChange={(e) =>
                      handleSocialMediaChange(item.id, "link", e.target.value)
                    }
                    className="h-10 bg-gray-50"
                  />
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => removeSocialMedia(item.id)}
                  className="h-10 w-10 text-gray-500 hover:text-red-500"
                >
                  <Trash2 size={16} />
                </Button>
              </div>
            ))}
          </div>
        </div>
        <div className="flex justify-between border-t pt-6">
          <Button variant="outline" onClick={() => redirect("/admin")}>
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={form.formState.isSubmitting}
            size="sm"
            className="disabled:bg-primary/70"
          >
            {form.formState.isSubmitting ? (
              <>
                <Loader /> Saving Changes
              </>
            ) : (
              <>Save Changes</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default SettingsFrom;
