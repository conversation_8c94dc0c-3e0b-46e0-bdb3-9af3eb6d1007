'use server'

import prisma from "@/lib/prisma";
import { Prisma } from "@prisma/client";


export type ProgramWithCourses = Prisma.ProgramGetPayload<{
  include: {
    courses: {
      select: {
        id: true,
        title: true
      }
    },
    _count: {
      select: {
        courses: true
      }
    }
  };
}>;

export async function getPrograms({ sessionId }: { sessionId: string }) {
  try {
    const programs = await prisma.program.findMany({
      where: { sessionId },
      include: {
        courses: {
          select: {
            id: true,
            title: true
          }
        },
        _count: {
          select: {
            courses: true
          }
        }
      },
      orderBy: { order: 'asc' }
    });

    const total = await prisma.program.count();

    return { programs, total };
  } catch (error) {
    console.error("Error fetching programs:", error);
    throw new Error("Failed to fetch programs.");
  }
}

export async function getProgram(programId: string) {
  try {
    const program = await prisma.program.findUnique({
      where: { id: programId },
      include: {
        courses: true
      },
    });

    return program;
  } catch (error) {
    console.error("Error fetching program:", error);
    throw new Error("Failed to fetch program.");
  }
}

export async function getProgramOptions(sessionId: string) {
  try {
    const programs = await prisma.program.findMany({
      where: { sessionId },
      select: {
        id: true,
        name: true
      },
      orderBy: { order: 'asc' }
    })
    
    return programs.map(program => ({ label: program.name, value: program.id }))
  } catch (error) {
    console.log(error)
    return []
  }
}