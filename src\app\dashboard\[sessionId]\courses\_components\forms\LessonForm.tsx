"use client";

import type React from "react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { Loader } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Form } from "@/components/ui/form";
import { FormSelectField } from "@/components/form-element/select-field";
import { FormInputField } from "@/components/form-element/input-field";
import { FormTextareaField } from "@/components/form-element/text-area";
import FileInput from "@/components/form-element/file-input";
import CustomAlertDialog from "@/components/shared/CustomAlertDialog";

import {
  createLesson,
  deleteLesson,
  deleteLessonFile,
  updateLesson,
} from "@/lib/server/action/courses/modules/lessons";
import {
  LessonAction,
  lessonSchema,
  TL<PERSON>onForm,
} from "@/lib/server/action/courses/modules/lessons/lesson.schema";
import {
  generateFileUploadUrl,
  getVideoUploadUrl,
} from "@/lib/server/action/bunny/bunny.action";
import { uploadFileToBunny } from "@/lib/bunny";
import { useFileInput } from "@/hooks/use-file-input";
import { appConfig } from "@/config/app";
import { formatBytes } from "@/lib/utils";

interface LessonFormProps {
  lessonId?: string;
  lessonData?: LessonAction;
  moduleId: string;
}

export default function LessonForm({
  lessonId,
  lessonData,
  moduleId,
}: LessonFormProps) {
  const [uploadState, setUploadState] = useState({
    progress: 0,
    isUploading: false,
    uploadedBytes: 0,
    totalSize: 0,
  });
  const [videoDuration, setVideoDuration] = useState<number | null>(null);

  const video = useFileInput(appConfig.MAX_VIDEO_SIZE_MB);
  const document = useFileInput(appConfig.MAX_DOCUMENT_SIZE_MB);

  const form = useForm<TLessonForm>({
    resolver: zodResolver(lessonSchema),
    defaultValues: lessonData ?? {
      title: "",
      description: "",
      type: "VIDEO",
      duration: 0,
      mimeType: "",
      originalName: "",
      fileSize: 0,
    },
    mode: "onChange",
  });

  const type = form.watch("type");
  const isVideo = type === "VIDEO";

  useEffect(() => {
    if (video.duration !== null) setVideoDuration(video.duration);
  }, [video.duration]);

  const updateUploadProgress = (
    percentage: number,
    loaded: number,
    total: number
  ) => {
    setUploadState({
      progress: Math.round(percentage),
      isUploading: true,
      uploadedBytes: loaded,
      totalSize: total,
    });
  };

  const processUpload = async (file: File, fileType: string) => {
    const isVideoType = fileType === "VIDEO";
    const uploadData = isVideoType
      ? await getVideoUploadUrl()
      : await generateFileUploadUrl(file.name);

    const { fileId, uploadUrl, accessKey, cdnUrl } = uploadData;

    if (!uploadUrl || !accessKey || !fileId) {
      throw new Error(
        `Failed to get ${fileType.toLowerCase()} upload credentials.`
      );
    }

    await uploadFileToBunny(file, uploadUrl, accessKey, updateUploadProgress);
    return { fileId, uploadUrl: isVideoType ? uploadUrl : cdnUrl, accessKey };
  };

  const createFileData = (
    file: File,
    fileData: { fileId: string; uploadUrl: string },
    duration = 0
  ) => ({
    mimeType: file.type,
    originalName: file.name,
    fileSize: file.size,
    fileId: fileData.fileId,
    fileUrl: fileData.uploadUrl,
    duration,
  });

  const onSubmit = async (values: TLessonForm) => {
    setUploadState((prev) => ({
      ...prev,
      isUploading: true,
      progress: 0,
      uploadedBytes: 0,
      totalSize: 0,
    }));

    try {
      const currentFile = isVideo ? video.file : document.file;

      if (lessonId) {
        let finalData: LessonAction = { ...values, fileId: "", fileUrl: "" };

        if (currentFile) {
          await deleteLessonFile(lessonId);
          const fileData = await processUpload(currentFile, type);
          const duration = isVideo ? videoDuration || 0 : 0;
          finalData = {
            ...finalData,
            ...createFileData(currentFile, fileData, duration),
          };
        } else if (lessonData) {
          const {
            mimeType,
            originalName,
            fileSize,
            fileId,
            fileUrl,
            duration,
          } = lessonData;
          finalData = {
            ...finalData,
            mimeType,
            originalName,
            fileSize,
            fileId,
            fileUrl,
            duration,
          };
        } else {
          toast.error("Please select a file for the lesson.");
          return;
        }

        await updateLesson(lessonId, finalData);
      } else {
        if (!currentFile) {
          toast.error("Please select a file for the lesson.");
          return;
        }

        const fileData = await processUpload(currentFile, type);
        const duration = isVideo ? videoDuration || 0 : 0;
        const lessonData = {
          ...values,
          ...createFileData(currentFile, fileData, duration),
        };

        await createLesson(moduleId, lessonData);
      }
    } catch (error) {
      console.error(error);
      toast.error("Upload failed. Please try again.");
    } finally {
      setUploadState((prev) => ({ ...prev, isUploading: false }));
    }
  };

  const handleDelete = async () => {
    const res = await deleteLesson(lessonId as string);
    toast[res.success ? "success" : "error"](
      res.success ? res.message : res.error
    );
  };

  const { progress, isUploading, uploadedBytes, totalSize } = uploadState;
  const { isSubmitting } = form.formState;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormSelectField
          control={form.control}
          name="type"
          label="Type"
          placeholder="Select a type"
          options={[
            { value: "VIDEO", label: "Video" },
            { value: "DOC", label: "Document" },
          ]}
        />
        <FormInputField
          control={form.control}
          name="title"
          label="Title"
          placeholder="Enter lesson title"
        />
        <FormTextareaField
          control={form.control}
          name="description"
          label="Description"
          placeholder="Enter lesson description"
        />

        <div className="space-y-2">
          <Label htmlFor="file">File Upload</Label>
          <FileInput
            id={isVideo ? "video" : "doc"}
            label={isVideo ? "Video" : "Document"}
            accept={isVideo ? "video/*" : ".pdf"}
            file={isVideo ? video.file : document.file}
            previewUrl={isVideo ? video.previewUrl : document.previewUrl}
            inputRef={isVideo ? video.inputRef : document.inputRef}
            onChange={
              isVideo ? video.handleFileChange : document.handleFileChange
            }
            onReset={isVideo ? video.resetFile : document.resetFile}
            type={isVideo ? "video" : "document"}
          />

          {isUploading && (
            <div className="space-y-2 mt-4">
              <div className="flex justify-between text-sm">
                <span>Uploading File...</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="w-full" />
              <div className="text-xs text-muted-foreground">
                {formatBytes(uploadedBytes)} / {formatBytes(totalSize)}
              </div>
            </div>
          )}

          {lessonData && (
            <p className="text-sm text-muted-foreground">
              File Name:{" "}
              <span className="text-primary">{lessonData.originalName}</span>
            </p>
          )}
        </div>

        <div className="flex items-center gap-2 pt-4">
          {lessonId && (
            <CustomAlertDialog
              title="Delete Lesson"
              description="This action will remove lesson data for this module. Are you sure you want to delete this lesson?"
              trigger={<Button variant="destructive">Delete Lesson</Button>}
              onConfirm={handleDelete}
              className="ml-auto"
            />
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader /> {lessonId ? "Updating" : "Creating"} Lesson
              </>
            ) : (
              `${lessonId ? "Update" : "Create"} Lesson`
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
