"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Plus, Trash2 } from "lucide-react";

interface Question {
  id: string;
  type: "multiple-choice" | "true-false";
  question: string;
  options?: string[];
  correctAnswer: string;
}

interface QuestionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (question: Question) => void;
  editingQuestion?: Question | null;
}

export function QuestionModal({
  isOpen,
  onClose,
  onSave,
  editingQuestion,
}: QuestionModalProps) {
  const [questionType, setQuestionType] = useState<
    "multiple-choice" | "true-false"
  >("multiple-choice");
  const [questionText, setQuestionText] = useState("");
  const [options, setOptions] = useState<string[]>(["", ""]);
  const [correctAnswer, setCorrectAnswer] = useState<string>("");

  useEffect(() => {
    if (editingQuestion) {
      setQuestionType(editingQuestion.type);
      setQuestionText(editingQuestion.question);
      setOptions(editingQuestion.options || ["", ""]);
      setCorrectAnswer(editingQuestion.correctAnswer);
    } else {
      // Reset form
      setQuestionType("multiple-choice");
      setQuestionText("");
      setOptions(["", ""]);
      setCorrectAnswer("");
    }
  }, [editingQuestion, isOpen]);

  const handleAddOption = () => {
    if (options.length < 4) {
      setOptions([...options, ""]);
    }
  };

  const handleRemoveOption = (index: number) => {
    if (options.length > 2) {
      const newOptions = options.filter((_, i) => i !== index);
      setOptions(newOptions);
      // Reset correct answer if it was the removed option
      if (correctAnswer === ["A", "B", "C", "D"][index]) {
        setCorrectAnswer("");
      }
    }
  };

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...options];
    newOptions[index] = value;
    setOptions(newOptions);
  };

  const handleSave = () => {
    if (!questionText.trim()) return;

    if (questionType === "multiple-choice") {
      const filledOptions = options.filter((opt) => opt.trim());
      if (filledOptions.length < 2 || correctAnswer === "") return;

      onSave({
        id: editingQuestion?.id || "",
        type: questionType,
        question: questionText,
        options: filledOptions,
        correctAnswer,
      });
    } else {
      if (correctAnswer === "") return;

      onSave({
        id: editingQuestion?.id || "",
        type: questionType,
        question: questionText,
        correctAnswer: correctAnswer as string,
      });
    }
  };

  const isValid = () => {
    if (!questionText.trim()) return false;
    if (questionType === "multiple-choice") {
      const filledOptions = options.filter((opt) => opt.trim());
      return filledOptions.length >= 2 && correctAnswer !== "";
    }
    return correctAnswer !== "";
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {editingQuestion ? "Edit Question" : "Add New Question"}
          </DialogTitle>
          <DialogDescription>
            Create a new question for your quiz
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="questionType">Question Type</Label>
            <Select
              value={questionType}
              onValueChange={(value: "multiple-choice" | "true-false") => {
                setQuestionType(value);
                setCorrectAnswer("");
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="multiple-choice">Multiple Choice</SelectItem>
                <SelectItem value="true-false">True/False</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="questionText">Question</Label>
            <Textarea
              id="questionText"
              value={questionText}
              onChange={(e) => setQuestionText(e.target.value)}
              placeholder="Enter your question here..."
              rows={3}
            />
          </div>

          {questionType === "multiple-choice" && (
            <div>
              <div className="flex justify-between items-center mb-3">
                <Label>Answer Options</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddOption}
                  disabled={options.length >= 4}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Option
                </Button>
              </div>

              <div className="space-y-3">
                <RadioGroup
                  value={correctAnswer.toString()}
                  onValueChange={(value) => setCorrectAnswer(value)}
                >
                  {options.map((option, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <RadioGroupItem
                        value={["A", "B", "C", "D"][index]}
                        id={`option-${index}`}
                      />
                      <div className="flex-1 flex items-center gap-2">
                        <Label
                          htmlFor={`option-${index}`}
                          className="text-sm font-medium"
                        >
                          {String.fromCharCode(65 + index)}.
                        </Label>
                        <Input
                          value={option}
                          onChange={(e) =>
                            handleOptionChange(index, e.target.value)
                          }
                          placeholder={`Option ${String.fromCharCode(
                            65 + index
                          )}`}
                          className="flex-1"
                        />
                        {options.length > 2 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => handleRemoveOption(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                Select the correct answer by clicking the radio button next to
                it.
              </p>
            </div>
          )}

          {questionType === "true-false" && (
            <div>
              <Label>Correct Answer</Label>
              <RadioGroup
                value={correctAnswer as string}
                onValueChange={(value) => setCorrectAnswer(value)}
                className="flex gap-6 mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="true" id="true" />
                  <Label htmlFor="true">True</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="false" id="false" />
                  <Label htmlFor="false">False</Label>
                </div>
              </RadioGroup>
            </div>
          )}

          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={!isValid()}>
              {editingQuestion ? "Update Question" : "Add Question"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
