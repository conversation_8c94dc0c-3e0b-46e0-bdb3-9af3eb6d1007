import { Card, CardContent } from "@/components/ui/card";

export default function ModuleOverviewCard({
  totalModules,
  totalAttempts,
}: {
  totalModules: number;
  totalAttempts: number;
}) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
      <OverviewCardContent title="Total Modules" value={totalModules} />
      <OverviewCardContent title="Total Attempts" value={totalAttempts} />
    </div>
  );
}

function OverviewCardContent({
  title,
  value,
}: {
  title: string;
  value: number;
}) {
  return (
    <Card className="py-4">
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-sm text-gray-600">{title}</p>
      </CardContent>
    </Card>
  );
}
