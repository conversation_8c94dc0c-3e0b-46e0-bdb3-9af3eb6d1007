'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"
import { saveCertificate } from "../../certificates/certificates.action"


export async function trackModuleProgress(studentId: string, moduleId: string, progress: number, isCompleted?: boolean) {
  try {
    const progressRecord = await prisma.studentModuleProgress.upsert({
      where: {
        studentId_moduleId: {
          studentId,
          moduleId
        }
      },
      update: {
        progress: Math.max(progress, 0),
        lastAccessed: new Date(),
        completedAt: progress >= 100 ? new Date() : null,
        isCompleted
      },
      create: {
        studentId,
        moduleId,
        progress: Math.max(progress, 0),
        lastAccessed: new Date(),
        completedAt: progress >= 100 ? new Date() : null
      }
    })
    
    await updateCourseProgress(studentId, moduleId)

    revalidatePath('/student/dashboard/modules')
    
    return { success: true, progressRecord }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to track progress' }
  }
}

async function updateCourseProgress(studentId: string, moduleId: string) {
  const moduleD = await prisma.module.findUnique({
    where: { id: moduleId },
    include: { course: true }
  })
  
  if (!moduleD) return
  
  const [totalModules, completedModules, totalModulesWithAssessment, assessmentScore] = await Promise.all([
    prisma.module.count({
      where: {
        courseId: moduleD.courseId,
        OR: [
          { lesson: { isNot: null } },
          { assessment: { isNot: null } }
        ]
      }
    }),
    prisma.studentModuleProgress.count({
      where: {
        studentId,
        module: { courseId: moduleD.courseId },
        completedAt: { not: null }
      }
    }),
    prisma.module.count({
      where: {
        courseId: moduleD.courseId,
        assessment: { isNot: null }
      }
    }),
    prisma.module.findMany({
      where: {
        courseId: moduleD.courseId,
        assessment: { isNot: null }
      },
      select: {
        id: true,
        assessment: {
          select: {
            attempts: {
              where: { studentId },
              select: {
                score: true
              },
              orderBy: [{ score: 'desc' }],
              take: 1
            }
          }
        },
      }
    })
  ])
  
  // add all the assessment scores
  const totalAssessmentScore = assessmentScore.reduce((sum, assessment) => sum + (assessment.assessment?.attempts?.[0]?.score ?? 0), 0)
  const score = totalAssessmentScore / (totalModulesWithAssessment * 100) * 100 || 0

  const courseProgress = (completedModules / totalModules) * 100

  await prisma.$transaction([
    prisma.studentProgress.upsert({
      where: {
        studentId_courseId: {
          studentId,
          courseId: moduleD.courseId
        }
      },
      create: {
        studentId,
        courseId: moduleD.courseId,
        progress: courseProgress,
        score,
        completedAt: courseProgress >= 100 ? new Date() : null
      },
      update: {
        progress: courseProgress,
        score,
        completedAt: courseProgress >= 100 ? new Date() : null
      }
    }), 
    prisma.enrollment.updateMany({
      where: {
        studentId,
        courseId: moduleD.courseId
      },
      data: {
        progress: courseProgress >= 100 ? 50 : 0, // set progress to 50% if course assessment is completed
      }
    })
  ])

  if (courseProgress >= 100) {
    console.log(`Course ${moduleD.courseId} completed by student ${studentId}. Saving certificate...`);
    void saveCertificate(studentId).catch((err) => {
      console.error("Async error:", err);
    });
  }
}