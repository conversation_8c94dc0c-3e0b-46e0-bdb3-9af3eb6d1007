import { Suspense } from "react";
import Loading from "../_components/Loading";
import CourseStatusCard from "../courses/_components/cards/course/status-card";
import LoadMore from "../courses/_components/LoadMore";
import { appConfig } from "@/config/app";
import NoMore from "../courses/_components/shared/no-more";
import {
  getTeacherCourses,
  getTeacherCourseStatusData,
} from "@/lib/server/action/courses";
import { redirect } from "next/navigation";
import { auth } from "@/lib/server/auth";
import CourseCard from "../courses/_components/cards/course/course-card";

async function SuspendedComponent({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) {
  const { sessionId } = await params;

  const session = await auth();
  if (!session || !session.user) {
    redirect("/sign-in");
  }
  const [courses, courseStatusData] = await Promise.all([
    getTeacherCourses({
      teacherId: session.user.profileId as string,
      sessionId,
    }),
    getTeacherCourseStatusData(session.user.profileId as string, sessionId),
  ]);

  return (
    <div className="space-y-6 pb-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">All Courses</h1>
          <p className="text-gray-600 mt-1">
            Manage and organize your course content
          </p>
        </div>
      </div>

      {/* Stats */}
      <CourseStatusCard
        total={courseStatusData.total}
        draft={courseStatusData.draft}
        published={courseStatusData.published}
        students={courseStatusData.students}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {courses.map((course) => {
          const primaryTeacher = course.teacherAssignments.find(
            (ta) => ta.role === "PRIMARY"
          );
          const primaryTeacherName = primaryTeacher
            ? primaryTeacher.user.firstName + " " + primaryTeacher.user.lastName
            : "N/A";
          return (
            <CourseCard
              key={course.id}
              course={course}
              primaryTeacherName={primaryTeacherName}
              sessionId={sessionId}
            />
          );
        })}
      </div>

      {courses.length > appConfig.ITEMS_PER_PAGE ? <LoadMore /> : null}
      {courses.length === 0 && (
        <NoMore
          text="No courses found"
          link={`/dashboard/${sessionId}/my-courses`}
        />
      )}
    </div>
  );
}

export default function MyCoursesPage({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) {
  return (
    <>
      <Suspense fallback={<Loading />}>
        <SuspendedComponent params={params} />
      </Suspense>
    </>
  );
}
