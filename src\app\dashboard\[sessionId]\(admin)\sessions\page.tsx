import { But<PERSON> } from "@/components/ui/button";
import PageWrapper from "../../_components/layout/PageWrapper";
import { Suspense } from "react";
import Loading from "../../_components/Loading";
import { CustomSheet } from "@/components/shared/CustomSheet";
import { getSessions } from "@/lib/server/action/sessions";
import { DataTable } from "../../_components/table/data-table";
import { columns } from "./columns";
import SessionForm from "../../_components/form/SessionForm";

const breadcrumbItems = [
  { label: "Home", href: "/dashboard" },
  { label: "Sessions" },
];

async function SuspendedComponent() {
  const sessions = await getSessions();

  return (
    <>
      <DataTable columns={columns} data={sessions} />
    </>
  );
}

export default function SessionsPage() {
  const RenderButton = () => (
    <CustomSheet
      title="Create Session"
      trigger={<Button size="sm">Add Session</Button>}
    >
      <SessionForm />
    </CustomSheet>
  );

  return (
    <PageWrapper
      pgTitle="Manage Sessions"
      breadcrumbItems={breadcrumbItems}
      headerButton={<RenderButton />}
    >
      <Suspense fallback={<Loading />}>
        <SuspendedComponent />
      </Suspense>
    </PageWrapper>
  );
}
