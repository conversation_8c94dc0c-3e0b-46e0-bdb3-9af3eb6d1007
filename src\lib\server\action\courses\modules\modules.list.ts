'use server'

import prisma from "@/lib/prisma"
import { Prisma } from "@prisma/client";

export type ModulesWithDetails = {
  id: string;
  title: string;
  description: string | null;
  order: number;
  questions: number;
  totalPoints: number;
  created: string;
  attempts: number;
};

export async function getModules({ courseId, search }: { courseId: string, search?: string }) {
  const whereClause: Prisma.ModuleWhereInput = {};

  whereClause.courseId = courseId;

  if (search) {
    whereClause.OR = [
      { title: { contains: search } },
      { description: { contains: search } },
    ];
  }

  try {
    const modules = await prisma.module.findMany({
      where: whereClause,
      orderBy: { order: 'asc' },
      select: {
        id: true,
        title: true,
        description: true,
        createdAt: true,
        order: true,
        assessment: {
          select: {
            _count: {
              select: {
                attempts: true,
                questions: true
              }
            },
            questions: {
              select: {
                points: true
              }
            }
          },
        }
      }
    })

    const moduleData = modules.map(module => ({
      id: module.id,
      title: module.title,
      description: module.description,
      order: module.order,
      questions: module.assessment?._count.questions ?? 0,
      totalPoints: module.assessment?.questions.reduce((sum, q) => sum + q.points, 0) ?? 0,
      created: module.createdAt.toDateString(),
      attempts: module.assessment?._count.attempts ?? 0,
    }))
    
    return moduleData
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch modules.")
  }
}

export async function getModuleOverviewCounts (courseId: string) {
  try {
    // const counts = await prisma.module.findMany({
    //   where: { courseId },
    //   select: {
    //     assessment: {
    //       select: {
    //         _count: true
    //       }
    //     }
    //   }
    // })

    const [totalModules, totalAttempts] = await Promise.all([prisma.module.count({
      where: { courseId }
    }), prisma.assessmentAttempt.count({
      where: { assessment: { module: { courseId } } }
    })])
    // const totalAttempts = counts.reduce((acc, module) => acc + (module.assessment?._count.attempts ?? 0), 0)
    
    return { totalModules, totalAttempts }
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch modules.")
  }
}

export async function getModule(moduleId: string) {
  try {
    const moduleD = await prisma.module.findUnique({
      where: { id: moduleId },
      select: {
        id: true,
        title: true,
        description: true,
        course: {
          select: {
            title: true,
            _count: {
              select: {
                enrollments: true
              }
            },
            teacherAssignments: {
              where: { role: "PRIMARY" },
              select: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            },
            program: {
              select: {
                name: true
              }
            }
          }
        },
        assessment: {
          select: {
            _count: {
              select: {
                questions: true
              }
            }
          }
        }
      }
    })

    if (!moduleD) return null

    const moduleData = {
      id: moduleD.id,
      title: moduleD.title,
      description: moduleD.description,
      course: moduleD.course.title,
      program: moduleD.course.program.name,
      teacher: moduleD.course.teacherAssignments[0].user.firstName + " " + moduleD?.course?.teacherAssignments[0].user.lastName,
      count: {
        questions: moduleD.assessment?._count.questions ?? 0,
        students: moduleD.course._count.enrollments ?? 0
      }
    }
    
    return moduleData
  } catch (error) {
    console.log(error)
    return null
  }
}

// Student
export async function getStudentModules(courseId: string, studentId: string) {
  try {
    const modules = await prisma.module.findMany({
      where: { courseId },
      orderBy: { order: 'asc' },
      select: {
        id: true,
        title: true,
        description: true,
        createdAt: true,
        order: true,
        assessment: {
          select: {
            id: true,
            passingScore: true,
            maxStudentQuestions: true,
            instructions: true,
            _count: {
              select: {
                attempts: true,
                questions: true
              }
            },
            attempts: {
              where: { studentId },
              select: {
                attemptNumber: true,
                status: true,
                score: true,
                totalPoints: true,
                passed: true,
                startedAt: true,
                completedAt: true
              },
              orderBy: [
                { score: 'desc' },
                { attemptNumber: 'desc' }
              ],
              take: 1
            }
          },
        },
        progress: {
          where: { studentId },
          select: {
            progress: true,
            completedAt: true,
            isCompleted: true
          },
          orderBy: { updatedAt: 'desc' },
          take: 1
        },
        lesson: {
          select: {
            id: true,
            title: true,
            description: true,
            type: true,
            fileUrl: true,
            fileId: true,
            videoUrl: true,
            thumbnailUrl: true,
            duration: true,
            progress: {
              where: { studentId },
              select: {
                progress: true,
                completedAt: true,
                isCompleted: true,
                hasWatched: true
              },
              orderBy: { updatedAt: 'desc' },
              take: 1
            },
          }
        }
      }
    })

    const moduleData = modules.map(module => {
      // Safely access nested properties
      const lesson = module.lesson;
      const assessment = module.assessment;

      const bestAttempt = assessment?.attempts?.[0];
      const lessonProgress = lesson?.progress?.[0];

      return {
        id: module.id,
        title: module.title,
        description: module.description,
        lesson: lesson ? {
          id: lesson.id,
          title: lesson.title,
          description: lesson.description || "",
          type: lesson.type,
          created: module.createdAt,
          fileUrl: lesson.fileUrl,
          fileId: lesson.fileId,
          video: lesson.type === 'VIDEO' ? {
            duration: lesson.duration || 0,
            videoUrl: lesson.videoUrl!,
            thumbnailUrl: lesson.thumbnailUrl!,
            progress: lessonProgress ? {
              percentage: lessonProgress.progress,
              completedAt: lessonProgress.completedAt,
              isCompleted: lessonProgress.isCompleted,
              hasWatched: lessonProgress.hasWatched,
            } : null,
          } : null,
        } : null,
        assessment: assessment ? {
          id: assessment.id,
          passingScore: assessment.passingScore,
          maxStudentQuestions: assessment.maxStudentQuestions,
          instructions: assessment.instructions,
          attemptsCount: assessment._count.attempts,
          questionCount: assessment._count.questions,
          bestAttempt: bestAttempt ? {
            attemptNumber: bestAttempt.attemptNumber,
            status: bestAttempt.status,
            score: bestAttempt.score,
            totalPoints: bestAttempt.totalPoints,
            passed: bestAttempt.passed,
            completedAt: bestAttempt.completedAt,
          } : null,
        } : null,
        progress: module.progress[0]?.progress ?? 0,
        completedAt: module.progress[0]?.completedAt ?? null,
        isCompleted: module.progress[0]?.isCompleted ?? false
      };
    });
    
    return moduleData.filter(module => module.lesson || module.assessment)
  } catch (error) {
    console.error("Error fetching student modules:", error)
    throw new Error("Failed to fetch modules.")
  }
}

export type StudentModule = Awaited<ReturnType<typeof getStudentModules>>[number];